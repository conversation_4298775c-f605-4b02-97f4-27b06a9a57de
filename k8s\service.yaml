apiVersion: v1
kind: Service
metadata:
  name: specific-ai-auth-service
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
  - port: 10086
    targetPort: 10086
    protocol: TCP
    name: http
  selector:
    app: specific-ai-auth

---
apiVersion: v1
kind: Service
metadata:
  name: specific-ai-auth-nodeport
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
spec:
  type: NodePort
  ports:
  - port: 10086
    targetPort: 10086
    nodePort: 30086
    protocol: TCP
    name: http
  selector:
    app: specific-ai-auth
