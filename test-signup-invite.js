#!/usr/bin/env node

/**
 * sign-up-invite 接口测试脚本
 * 
 * 功能：
 * 1. 自动获取邀请码
 * 2. 生成随机测试数据
 * 3. 测试注册接口性能
 * 4. 展示详细结果
 */

// 配置
const CONFIG = {
  // 邀请码生成服务
  INVITE_SERVICE_URL: 'http://localhost:8000/api/v1/invitations/generate',
  
  // 认证服务
  AUTH_SERVICE_URL: 'http://localhost:10086/api/auth/sign-up-invite',
  LOGIN_URL: 'http://localhost:10086/api/auth/sign-in/email',
  
  // 测试配置
  TIMEOUT: 30000, // 30秒超时
  BATCH_SIZE: 10,  // 批量测试数量
  FULL_TEST: false, // 是否进行完整测试（包括登录和企业富化）
};

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorLog(color, ...args) {
  console.log(colors[color], ...args, colors.reset);
}

// 生成随机数据
function generateRandomData() {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  
  return {
    email: `test_${timestamp}_${randomSuffix}@example.com`,
    password: 'TestPassword123!',
    name: `测试用户_${randomSuffix}`,
    company_name: `测试公司_${randomSuffix}`,
    language: 'chinese'
  };
}

// 获取邀请码
async function getInviteCode() {
  colorLog('blue', '📝 正在获取邀请码...');
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(CONFIG.INVITE_SERVICE_URL, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        description: `测试邀请码 - ${new Date().toISOString()}`
      }),
      signal: AbortSignal.timeout(CONFIG.TIMEOUT)
    });

    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.code !== 200 || !data.success || !data.data?.code) {
      throw new Error(`邀请码获取失败: ${data.message || '未知错误'}`);
    }

    colorLog('green', `✅ 邀请码获取成功: ${data.data.code} (${responseTime}ms)`);
    
    return {
      code: data.data.code,
      responseTime,
      data: data.data
    };
    
  } catch (error) {
    colorLog('red', `❌ 邀请码获取失败: ${error.message}`);
    throw error;
  }
}

// 测试注册接口
async function testSignUpInvite(inviteCode, userData) {
  colorLog('blue', '🚀 正在测试注册接口...');
  
  try {
    const startTime = Date.now();
    
    const requestBody = {
      ...userData,
      invite_code: inviteCode
    };
    
    colorLog('cyan', '📤 请求数据:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch(CONFIG.AUTH_SERVICE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(CONFIG.TIMEOUT)
    });

    const responseTime = Date.now() - startTime;
    const responseData = await response.json();
    
    colorLog('cyan', '📥 响应数据:', JSON.stringify(responseData, null, 2));
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${responseData.message || response.statusText}`);
    }

    if (!responseData.success) {
      throw new Error(`注册失败: ${responseData.message || '未知错误'}`);
    }

    colorLog('green', `✅ 注册成功! (${responseTime}ms)`);
    colorLog('green', `👤 用户ID: ${responseData.user.id}`);
    colorLog('green', `📧 邮箱: ${responseData.user.email}`);
    colorLog('green', `🏢 公司: ${responseData.user.company_name}`);
    
    return {
      success: true,
      responseTime,
      userData: responseData.user,
      response: responseData
    };
    
  } catch (error) {
    colorLog('red', `❌ 注册失败: ${error.message}`);
    
    return {
      success: false,
      error: error.message,
      responseTime: null
    };
  }
}

// 测试登录接口
async function testLogin(email, password) {
  colorLog('blue', '🔐 正在测试登录接口...');
  
  try {
    const startTime = Date.now();
    
    const requestBody = { email, password };
    
    const response = await fetch(CONFIG.LOGIN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(CONFIG.TIMEOUT)
    });

    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
    }

    // 提取 cookies（修复格式以匹配企业富化服务预期）
    const setCookieHeaders = response.headers.getSetCookie ? response.headers.getSetCookie() : [];
    const cookies = setCookieHeaders
      .map(cookie => {
        // 只取 name=value 部分，忽略其他参数（如 Path, HttpOnly 等）
        const nameValue = cookie.split(';')[0].trim();
        return nameValue;
      })
      .filter(Boolean)
      .join('; '); // 用分号空格连接，符合标准格式
    
    colorLog('green', `✅ 登录成功! (${responseTime}ms)`);
    colorLog('cyan', `🍪 获取到 cookies: ${cookies ? '已获取' : '未获取'}`);
    if (cookies) {
      colorLog('cyan', `🔍 Cookies 内容: ${cookies}`);
    }
    
    return {
      success: true,
      responseTime,
      cookies
    };
    
  } catch (error) {
    colorLog('red', `❌ 登录失败: ${error.message}`);
    
    return {
      success: false,
      error: error.message,
      responseTime: null,
      cookies: null
    };
  }
}

// 显示测试摘要
function displaySummary(results) {
  colorLog('bright', '\n📊 测试摘要');
  colorLog('bright', '='.repeat(50));
  
  // 注册测试统计
  const signupSuccessful = results.filter(r => r.signup?.success).length;
  const signupFailed = results.length - signupSuccessful;
  
  colorLog('cyan', '\n📝 注册测试结果:');
  colorLog('green', `  ✅ 成功: ${signupSuccessful}`);
  colorLog('red', `  ❌ 失败: ${signupFailed}`);
  
  if (signupSuccessful > 0) {
    const signupTimes = results
      .filter(r => r.signup?.success && r.signup?.responseTime)
      .map(r => r.signup.responseTime);
    
    if (signupTimes.length > 0) {
      const avgTime = Math.round(signupTimes.reduce((a, b) => a + b, 0) / signupTimes.length);
      const minTime = Math.min(...signupTimes);
      const maxTime = Math.max(...signupTimes);
      
      colorLog('blue', `  ⏱️  平均响应时间: ${avgTime}ms`);
      colorLog('blue', `  ⚡ 最快响应: ${minTime}ms`);
      colorLog('blue', `  🐌 最慢响应: ${maxTime}ms`);
    }
  }
  
  // 如果有完整测试，显示登录和富化统计
  const fullTestResults = results.filter(r => r.fullTest);
  if (fullTestResults.length > 0) {
    colorLog('cyan', '\n🔐 登录测试结果:');
    const loginSuccessful = fullTestResults.filter(r => r.login?.success).length;
    const loginFailed = fullTestResults.length - loginSuccessful;
    colorLog('green', `  ✅ 成功: ${loginSuccessful}`);
    colorLog('red', `  ❌ 失败: ${loginFailed}`);
    
    colorLog('cyan', '\n🏢 企业富化测试结果:');
    const enrichSuccessful = fullTestResults.filter(r => r.enrichment?.success).length;
    const enrichFailed = fullTestResults.length - enrichSuccessful;
    colorLog('green', `  ✅ 成功: ${enrichSuccessful}`);
    colorLog('red', `  ❌ 失败: ${enrichFailed}`);
  }
  
  colorLog('bright', '='.repeat(50));
}

// 主测试函数
async function runTest() {
  colorLog('bright', '🧪 sign-up-invite 接口测试开始');
  colorLog('bright', '='.repeat(50));
  
  const results = [];
  
  try {
    for (let i = 0; i < CONFIG.BATCH_SIZE; i++) {
      if (CONFIG.BATCH_SIZE > 1) {
        colorLog('yellow', `\n📋 执行测试 ${i + 1}/${CONFIG.BATCH_SIZE}`);
      }
      
      // 1. 获取邀请码
      const inviteResult = await getInviteCode();
      
      // 2. 生成测试数据
      const userData = generateRandomData();
      colorLog('blue', '🎲 生成测试数据:', {
        email: userData.email,
        name: userData.name,
        company_name: userData.company_name
      });
      
      // 3. 测试注册
      const signUpResult = await testSignUpInvite(inviteResult.code, userData);
      
      let loginResult = { success: false };
      let enrichResult = { success: false };
      
      // 4. 如果启用完整测试且注册成功，则继续测试登录和企业富化
      if (CONFIG.FULL_TEST && signUpResult.success) {
        colorLog('yellow', '\n🔄 开始完整测试流程...');
        
        // 4a. 测试登录
        loginResult = await testLogin(userData.email, userData.password);
        
        // 4b. 如果登录成功，测试企业富化
        if (loginResult.success && loginResult.cookies) {
          enrichResult = await testCompanyEnrichment(loginResult.cookies);
        }
      }
      
      const testResult = {
        signup: signUpResult,
        login: loginResult,
        enrichment: enrichResult,
        inviteCode: inviteResult.code,
        testData: userData,
        fullTest: CONFIG.FULL_TEST
      };
      
      results.push(testResult);
      
      // 在批量测试中添加延迟
      if (CONFIG.BATCH_SIZE > 1 && i < CONFIG.BATCH_SIZE - 1) {
        colorLog('yellow', '⏳ 等待 2 秒后继续下一个测试...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // 显示摘要
    displaySummary(results);
    
  } catch (error) {
    colorLog('red', `💥 测试过程发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 处理命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--invite-url':
        CONFIG.INVITE_SERVICE_URL = args[++i];
        break;
      case '--auth-url':
        CONFIG.AUTH_SERVICE_URL = args[++i];
        break;
      case '--batch':
        CONFIG.BATCH_SIZE = parseInt(args[++i]) || 1;
        break;
      case '--timeout':
        CONFIG.TIMEOUT = parseInt(args[++i]) || 30000;
        break;
      case '--full-test':
        CONFIG.FULL_TEST = true;
        break;
      case '--help':
        console.log(`
使用方法: node test-signup-invite.js [选项]

选项:
  --invite-url <url>    邀请码生成服务地址 (默认: ${CONFIG.INVITE_SERVICE_URL})
  --auth-url <url>      认证服务地址 (默认: ${CONFIG.AUTH_SERVICE_URL})
  --batch <number>      批量测试数量 (默认: ${CONFIG.BATCH_SIZE})
  --timeout <ms>        请求超时时间 (默认: ${CONFIG.TIMEOUT}ms)
  --full-test          启用完整测试（包括登录和企业富化）
  --help               显示帮助信息

示例:
  node test-signup-invite.js                          # 只测试注册性能
  node test-signup-invite.js --full-test             # 完整测试流程
  node test-signup-invite.js --batch 5               # 批量测试5次
  node test-signup-invite.js --auth-url http://localhost:3001/api/auth/sign-up-invite
        `);
        process.exit(0);
        break;
    }
  }
}

// 主入口
async function main() {
  parseArgs();
  
  colorLog('cyan', '🔧 配置信息:');
  colorLog('cyan', `   邀请码服务: ${CONFIG.INVITE_SERVICE_URL}`);
  colorLog('cyan', `   认证服务: ${CONFIG.AUTH_SERVICE_URL}`);
  colorLog('cyan', `   登录接口: ${CONFIG.LOGIN_URL}`);
  // colorLog('cyan', `   富化接口: ${CONFIG.ENRICH_URL}`);  
  colorLog('cyan', `   批量数量: ${CONFIG.BATCH_SIZE}`);
  colorLog('cyan', `   超时时间: ${CONFIG.TIMEOUT}ms`);
  colorLog('cyan', `   完整测试: ${CONFIG.FULL_TEST ? '启用' : '禁用'}`);
  console.log();
  
  await runTest();
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', '💥 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  colorLog('red', '💥 未捕获的异常:', error.message);
  process.exit(1);
});

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    colorLog('red', '💥 测试失败:', error.message);
    process.exit(1);
  });
}