/**
 * 邮件邀请服务
 * 处理用户邀请相关的邮件发送
 */

import { randomUUID } from 'crypto';
import { EmailService } from './email-service.js';
import { inviteCodeService } from './invite-code.js';
import { EmailTemplateData, EmailTemplateVersion, EmailSendResult } from '../types/email.js';
import { db } from '../lib/drizzle.js';
import { emailLog } from '../lib/auth-schema.js';
import { eq, gte, and } from 'drizzle-orm';

export interface InviteEmailOptions {
  recipientEmail: string;
  recipientName?: string;
  inviterName?: string;
  companyName?: string;
  inviteCode: string;
  inviteUrl: string;
  language?: string;
  version?: EmailTemplateVersion;
}

export interface InviteEmailResult extends EmailSendResult {
  inviteCode?: string;
  inviteUrl?: string;
}

/**
 * 邮件邀请服务类
 */
export class EmailInviteService {
  private emailService: EmailService;

  constructor(emailService: EmailService) {
    this.emailService = emailService;
  }

  /**
   * 发送邀请邮件
   */
  async sendInviteEmail(options: InviteEmailOptions): Promise<InviteEmailResult> {
    const {
      recipientEmail,
      recipientName,
      inviterName,
      companyName,
      inviteCode,
      inviteUrl,
      language = 'zh-CN',
      version = 'v3'
    } = options;

    const logId = randomUUID();

    try {
      // 1. 验证邀请码是否有效
      console.log(`🔍 验证邀请码: ${inviteCode}`);
      const codeVerification = await inviteCodeService.verifyCode(inviteCode);
      
      if (!codeVerification.valid) {
        console.error(`❌ 邀请码验证失败: ${codeVerification.error}`);
        return {
          success: false,
          error: `邀请码无效: ${codeVerification.error}`,
          inviteCode,
          inviteUrl
        };
      }

      // 2. 记录邮件发送开始日志
      await this.logInviteAttempt(logId, {
        email: recipientEmail,
        inviteCode,
        inviterName,
        companyName,
        status: 'pending',
        version,
        metadata: {
          hasRecipientName: !!recipientName,
          hasInviterName: !!inviterName,
          hasCompanyName: !!companyName,
          language
        }
      });

      console.log(`📧 准备发送邀请邮件到: ${recipientEmail}`, {
        logId,
        inviteCode,
        version,
        language,
        inviterName,
        companyName
      });

      // 3. 构建邮件模板数据
      const templateData: EmailTemplateData = {
        userEmail: recipientEmail,
        userName: recipientName,
        inviterName,
        companyName,
        inviteCode,
        inviteUrl,
        locale: language
      };

      // 4. 发送邮件
      const emailResult = await this.emailService.sendInviteEmail({
        userEmail: recipientEmail,
        userName: recipientName,
        inviteCode,
        inviteUrl,
        inviterName,
        companyName,
        version,
        language
      });

      if (!emailResult.success) {
        console.error('❌ 邀请邮件发送失败:', emailResult.error);
        
        // 更新失败状态
        await this.updateInviteStatus(logId, 'failed', emailResult.error);
        
        return {
          success: false,
          error: emailResult.error,
          inviteCode,
          inviteUrl
        };
      }

      // 5. 更新成功状态
      await this.updateInviteStatus(logId, 'sent', undefined, emailResult.messageId);

      console.log('✅ 邀请邮件发送成功:', {
        logId,
        messageId: emailResult.messageId,
        recipient: recipientEmail,
        inviteCode,
        version
      });

      return {
        success: true,
        messageId: emailResult.messageId,
        logId,
        inviteCode,
        inviteUrl
      };

    } catch (error) {
      console.error('❌ 邀请邮件发送异常:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      // 更新异常状态
      await this.updateInviteStatus(logId, 'failed', errorMessage);
      
      return {
        success: false,
        error: `邀请邮件发送异常: ${errorMessage}`,
        inviteCode,
        inviteUrl
      };
    }
  }

  /**
   * 批量发送邀请邮件
   */
  async sendBatchInviteEmails(invites: InviteEmailOptions[]): Promise<{
    successful: InviteEmailResult[];
    failed: InviteEmailResult[];
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    console.log(`📧 开始批量发送邀请邮件，总数: ${invites.length}`);
    
    const successful: InviteEmailResult[] = [];
    const failed: InviteEmailResult[] = [];

    // 并发发送，但限制并发数量
    const BATCH_SIZE = 5;
    for (let i = 0; i < invites.length; i += BATCH_SIZE) {
      const batch = invites.slice(i, i + BATCH_SIZE);
      
      const batchResults = await Promise.allSettled(
        batch.map(invite => this.sendInviteEmail(invite))
      );

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            successful.push(result.value);
          } else {
            failed.push(result.value);
          }
        } else {
          const invite = batch[index];
          if (invite) {
            failed.push({
              success: false,
              error: `批量发送失败: ${result.reason}`,
              inviteCode: invite.inviteCode,
              inviteUrl: invite.inviteUrl
            });
          }
        }
      });

      // 批次间延迟，避免过快发送
      if (i + BATCH_SIZE < invites.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const summary = {
      total: invites.length,
      successful: successful.length,
      failed: failed.length
    };

    console.log('📊 批量邀请邮件发送完成:', summary);

    return {
      successful,
      failed,
      summary
    };
  }

  /**
   * 生成邀请链接
   */
  generateInviteUrl(baseUrl: string, inviteCode: string, additionalParams?: Record<string, string>): string {
    const url = new URL(`${baseUrl}/register`);
    url.searchParams.set('invite_code', inviteCode);
    
    if (additionalParams) {
      Object.entries(additionalParams).forEach(([key, value]) => {
        url.searchParams.set(key, value);
      });
    }
    
    return url.toString();
  }

  /**
   * 记录邀请邮件发送尝试
   */
  private async logInviteAttempt(logId: string, data: {
    email: string;
    inviteCode: string;
    inviterName?: string;
    companyName?: string;
    status: 'pending' | 'sent' | 'failed';
    version: EmailTemplateVersion;
    messageId?: string;
    errorMessage?: string;
    metadata?: any;
  }): Promise<void> {
    try {
      const now = new Date();
      const logData = {
        id: logId,
        userId: null, // 邀请邮件通常发送给未注册用户
        email: data.email,
        emailType: 'invite',
        status: data.status,
        provider: 'resend',
        messageId: data.messageId,
        errorMessage: data.errorMessage,
        attemptCount: 1,
        sentAt: data.status === 'sent' ? now : null,
        deliveredAt: null,
        createdAt: now,
        updatedAt: now,
        metadata: JSON.stringify({
          inviteCode: data.inviteCode,
          inviterName: data.inviterName,
          companyName: data.companyName,
          version: data.version,
          ...data.metadata
        })
      };

      await db.insert(emailLog).values(logData);
    } catch (error) {
      console.error('记录邀请邮件日志失败:', error);
    }
  }

  /**
   * 更新邀请邮件状态
   */
  private async updateInviteStatus(
    logId: string,
    status: 'pending' | 'sent' | 'failed' | 'delivered' | 'bounced',
    errorMessage?: string,
    messageId?: string
  ): Promise<void> {
    try {
      const now = new Date();
      const updateData: any = {
        status,
        updatedAt: now
      };

      if (status === 'sent') {
        updateData.sentAt = now;
        if (messageId) {
          updateData.messageId = messageId;
        }
      } else if (status === 'delivered') {
        updateData.deliveredAt = now;
      } else if (status === 'failed' && errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      await db
        .update(emailLog)
        .set(updateData)
        .where(eq(emailLog.id, logId));
    } catch (error) {
      console.error('更新邀请邮件状态失败:', error);
    }
  }

  /**
   * 获取邀请邮件发送统计
   */
  async getInviteEmailStats(timeRangeHours: number = 24): Promise<{
    totalSent: number;
    totalFailed: number;
    successRate: number;
    byCompany: Record<string, { sent: number; failed: number }>;
  }> {
    try {
      const since = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);
      
      const logs = await db
        .select()
        .from(emailLog)
        .where(
          and(
            eq(emailLog.emailType, 'verification'),
            gte(emailLog.createdAt, since)
          )
        );

      const result = {
        totalSent: 0,
        totalFailed: 0,
        successRate: 0,
        byCompany: {} as Record<string, { sent: number; failed: number }>
      };

      for (const log of logs) {
        const metadata = log.metadata ? JSON.parse(log.metadata) : {};
        const company = metadata.companyName || 'Unknown';

        if (!result.byCompany[company]) {
          result.byCompany[company] = { sent: 0, failed: 0 };
        }

        if (log.status === 'sent' || log.status === 'delivered') {
          result.byCompany[company].sent += 1;
          result.totalSent += 1;
        } else if (log.status === 'failed') {
          result.byCompany[company].failed += 1;
          result.totalFailed += 1;
        }
      }

      const total = result.totalSent + result.totalFailed;
      result.successRate = total > 0 ? (result.totalSent / total) * 100 : 0;

      return result;
    } catch (error) {
      console.error('获取邀请邮件统计失败:', error);
      return {
        totalSent: 0,
        totalFailed: 0,
        successRate: 0,
        byCompany: {}
      };
    }
  }
}

// 扩展邮件服务以支持邀请邮件
declare module './email-service.js' {
  interface EmailService {
    sendInviteEmail(options: {
      userEmail: string;
      userName?: string;
      inviteCode: string;
      inviteUrl: string;
      inviterName?: string;
      companyName?: string;
      version?: EmailTemplateVersion;
      language?: string;
    }): Promise<EmailSendResult>;
  }
}

// 创建邮件邀请服务实例的工厂函数
export function createEmailInviteService(emailService: EmailService): EmailInviteService {
  return new EmailInviteService(emailService);
}