/**
 * 临时注册系统测试
 */

import { test, describe, expect, beforeAll, afterAll } from "@jest/globals";
import { app, initializeApp } from "../src/app.js";
import { pendingRegistrationService } from "../src/services/pending-registration.js";
import { db } from "../src/lib/drizzle.js";
import { pendingRegistration, user } from "../src/lib/auth-schema.js";
import { eq } from "drizzle-orm";

describe("临时注册系统", () => {
  beforeAll(async () => {
    await initializeApp();
  });

  afterAll(async () => {
    await app.close();
  });

  const testEmail = `test-${Date.now()}@example.com`;
  const testData = {
    email: testEmail,
    password: "testPassword123",
    name: "Test User",
    company_name: "Test Company",
    invite_code: "TEST2025",
    language: "english",
  };

  describe("临时注册创建", () => {
    test("应该成功创建临时注册记录", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: testData,
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.pendingId).toBeDefined();
      expect(body.data.email).toBe(testEmail);
      expect(body.data.nextSteps.requireEmailVerification).toBe(true);
    });

    test("应该拒绝无效的邀请码", async () => {
      const invalidData = { ...testData, invite_code: "INVALID" };
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: invalidData,
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe("INVALID_INVITE_CODE");
    });

    test("应该拒绝重复的邮箱", async () => {
      // 先创建一个记录
      await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: testData,
      });

      // 再次尝试创建相同邮箱的记录
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: testData,
      });

      expect(response.statusCode).toBe(409);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe("USER_EXISTS");
    });

    test("应该验证必需字段", async () => {
      const incompleteData = {
        email: testEmail,
        password: "test123",
        // 缺少其他必需字段
      };

      const response = await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: incompleteData,
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe("VALIDATION_ERROR");
    });
  });

  describe("重发验证邮件", () => {
    let testEmailForResend: string;

    beforeAll(async () => {
      testEmailForResend = `resend-test-${Date.now()}@example.com`;
      // 创建一个临时注册记录
      await app.inject({
        method: "POST",
        url: "/api/auth/register-pending",
        payload: {
          ...testData,
          email: testEmailForResend,
        },
      });
    });

    test("应该成功重发验证邮件", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/resend-pending-verification",
        payload: {
          email: testEmailForResend,
        },
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.email).toBe(testEmailForResend);
    });

    test("应该拒绝不存在的邮箱", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/resend-pending-verification",
        payload: {
          email: "<EMAIL>",
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe("RESEND_FAILED");
    });
  });

  describe("管理员统计接口", () => {
    test("应该返回注册统计信息", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/admin/pending-registration-stats",
        // 注意：这里没有提供认证信息，实际测试中需要添加
      });

      // 由于没有认证，应该返回401
      expect(response.statusCode).toBe(401);
    });
  });

  describe("临时注册服务单元测试", () => {
    test("应该能清理过期的记录", async () => {
      const cleanedCount = await pendingRegistrationService.cleanupExpiredRegistrations();
      expect(typeof cleanedCount).toBe("number");
      expect(cleanedCount).toBeGreaterThanOrEqual(0);
    });

    test("应该能获取注册统计", async () => {
      const stats = await pendingRegistrationService.getRegistrationStats();
      expect(Array.isArray(stats)).toBe(true);
    });
  });

  describe("数据库表验证", () => {
    test("pending_registration 表应该存在并可用", async () => {
      // 尝试查询表结构
      const result = await db.select().from(pendingRegistration).limit(1);
      expect(Array.isArray(result)).toBe(true);
    });

    test("应该能正确插入和查询临时注册记录", async () => {
      const testRecord = {
        id: `test-${Date.now()}`,
        email: `db-test-${Date.now()}@example.com`,
        password: "hashedPassword",
        name: "DB Test User",
        companyName: "DB Test Company",
        language: "chinese",
        inviteCode: "TEST2025",
        inviteCodeVerified: true,
        status: "pending" as const,
        registrationStep: "email_verification" as const,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

      // 插入记录
      await db.insert(pendingRegistration).values(testRecord);

      // 查询记录
      const found = await db
        .select()
        .from(pendingRegistration)
        .where(eq(pendingRegistration.id, testRecord.id))
        .limit(1);

      expect(found.length).toBe(1);
      expect(found[0]?.email).toBe(testRecord.email);
      expect(found[0]?.status).toBe("pending");

      // 清理测试记录
      await db
        .delete(pendingRegistration)
        .where(eq(pendingRegistration.id, testRecord.id));
    });
  });

  // 清理测试数据
  afterAll(async () => {
    try {
      // 清理临时注册记录
      await db
        .delete(pendingRegistration)
        .where(eq(pendingRegistration.email, testEmail));

      // 清理可能创建的用户记录
      await db
        .delete(user)
        .where(eq(user.email, testEmail));
    } catch (error) {
      console.warn("清理测试数据时出错:", error);
    }
  });
});