import { config } from 'dotenv';

// 加载测试环境变量
config({ path: '.env.test' });
config({ path: '.env' });

// 设置测试超时
jest.setTimeout(30000);

// 全局测试设置
beforeAll(async () => {
  // 这里可以添加全局测试前的设置
  // 例如：数据库连接、测试数据准备等
});

afterAll(async () => {
  // 这里可以添加全局测试后的清理
  // 例如：关闭数据库连接、清理测试数据等
});

// 模拟环境变量（如果需要的话）
process.env.NODE_ENV = 'test';
process.env.BETTER_AUTH_URL = process.env.BETTER_AUTH_URL || 'http://localhost:10086';

export {};