#!/bin/bash

# SpecificAI Auth Kubernetes 一键部署脚本
# 确保Pod、Service使用最新配置，包含完整的连通性测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 默认配置
NAMESPACE="ovs"
APP_NAME="specific-ai-auth"
IMAGE_REGISTRY="**************/specific-ai"
IMAGE_NAME="specific-ai-auth"
DEFAULT_TAG="latest"
DEPLOYMENT_TIMEOUT="300s"
ROLLOUT_TIMEOUT="300s"
HEALTH_CHECK_TIMEOUT=60
CONNECTION_TEST_TIMEOUT=30

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# 解析命令行参数
ENVIRONMENT="dev"
IMAGE_TAG="$DEFAULT_TAG"
FORCE_UPDATE=true
FORCE_LATEST=true
PULL_FIRST=true
DRY_RUN=false
VERBOSE=false
SKIP_TESTS=false
ENV_CONFIG_DIR=""

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_separator() {
    echo -e "${CYAN}----------------------------------------${NC}"
}

# 快速诊断函数
quick_diagnosis() {
    local pod_name="$1"
    
    log_info "🔍 快速诊断 Pod: $pod_name"
    log_separator
    
    # 1. Pod基本信息
    log_info "Pod状态详情:"
    kubectl describe pod -n $NAMESPACE "$pod_name" | grep -A 5 -E "(Status|Conditions|Events)" | sed 's/^/  /'
    
    # 2. 容器日志
    log_info "应用容器日志 (最后15行):"
    kubectl logs -n $NAMESPACE "$pod_name" -c $APP_NAME --tail=15 | sed 's/^/  ' || log_warning "无法获取应用日志"
    
    # 3. 端口检查
    log_info "端口监听检查:"
    kubectl exec -n $NAMESPACE "$pod_name" -c $APP_NAME -- ss -tlnp 2>/dev/null | grep -E "(10086|LISTEN)" | sed 's/^/  ' || \
    kubectl exec -n $NAMESPACE "$pod_name" -c $APP_NAME -- netstat -tlnp 2>/dev/null | grep -E "(10086|LISTEN)" | sed 's/^/  ' || \
    log_info "  无法检查端口状态（可能缺少网络工具）"
    
    # 4. 进程检查
    log_info "运行进程:"
    kubectl exec -n $NAMESPACE "$pod_name" -c $APP_NAME -- ps aux | head -10 | sed 's/^/  ' || log_info "  无法检查进程状态"
    
    # 5. 直接测试健康端点
    log_info "直接测试健康端点:"
    local direct_test
    direct_test=$(kubectl exec -n $NAMESPACE "$pod_name" -c $APP_NAME -- node -e "
        const http = require('http');
        const req = http.get('http://localhost:10086/api/health', (res) => {
            console.log('Status:', res.statusCode);
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => console.log('Response:', data.substring(0, 200)));
        });
        req.on('error', (err) => console.log('Error:', err.message));
        req.setTimeout(3000, () => { req.destroy(); console.log('Timeout'); });
    " 2>&1 | head -5)
    if [[ -n "$direct_test" ]]; then
        echo "$direct_test" | sed 's/^/  /'
    else
        log_info "  健康端点无响应"
    fi
    
    log_separator
}

# 添加脚本状态检查函数
check_script_health() {
    local function_name="$1"
    local start_time="$2"
    local current_time=$(date +%s)
    local elapsed=$((current_time - start_time))
    
    # 如果某个函数执行超过2分钟，记录警告
    if [[ $elapsed -gt 120 ]]; then
        log_warning "函数 $function_name 执行时间较长: ${elapsed}秒"
    fi
    
    # 检查系统资源
    if [[ "$VERBOSE" == true ]]; then
        log_info "[DEBUG] 当前时间: $(date)"
        log_info "[DEBUG] 内存使用: $(free -h | grep '^Mem:' | awk '{print $3"/"$2}')"
        log_info "[DEBUG] 磁盘使用: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5")"}')"
    fi
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
 SpecificAI Auth Kubernetes 一键部署脚本

用法:
    ./deploy-k8s.sh [选项] [镜像标签]

选项:
    -e, --env ENV           部署环境 (dev|prod, 默认: dev)
    -t, --tag TAG           镜像标签 (默认: latest)
    -f, --force             强制更新部署（即使镜像没有变化）
    --force-latest          强制拉取最新latest镜像并重建Pod
    -p, --pull              先拉取最新镜像，然后部署
    -d, --dry-run           干运行模式，仅显示将要执行的操作
    -v, --verbose           详细输出模式
    --skip-tests            跳过连通性测试
    -h, --help              显示此帮助信息

示例:
    ./deploy-k8s.sh                           # 默认dev环境，使用latest镜像部署
    ./deploy-k8s.sh --env prod                # 部署到生产环境（使用prod配置）
    ./deploy-k8s.sh --dry-run --env prod      # 干运行，查看生产环境部署操作
    ./deploy-k8s.sh --skip-tests              # 跳过连通性测试快速部署

配置:
    命名空间: ${NAMESPACE}
    应用名称: ${APP_NAME}
    镜像仓库: ${IMAGE_REGISTRY}
    镜像名称: ${IMAGE_NAME}
    NodePort端口: 30086
    Pod端口: 10086

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -f|--force)
                FORCE_UPDATE=true
                shift
                ;;
            --force-latest)
                FORCE_LATEST=true
                FORCE_UPDATE=true
                PULL_FIRST=true
                shift
                ;;
            -p|--pull)
                PULL_FIRST=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                IMAGE_TAG="$1"
                shift
                ;;
        esac
    done

    # 验证环境参数
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
        log_error "无效的环境参数: $ENVIRONMENT (支持: dev, prod)"
        exit 1
    fi

    # 设置环境配置目录
    ENV_CONFIG_DIR="$SCRIPT_DIR/$ENVIRONMENT"
    
    # 检查环境配置目录是否存在
    if [[ ! -d "$ENV_CONFIG_DIR" ]]; then
        log_error "环境配置目录不存在: $ENV_CONFIG_DIR"
        log_info "请确保存在 $ENVIRONMENT/configmap.yaml 和 $ENVIRONMENT/secret.yaml"
        exit 1
    fi

    # 强制latest模式的特殊处理
    if [[ "$FORCE_LATEST" == true ]]; then
        IMAGE_TAG="latest"
        log_info "使用统一latest镜像，环境差异通过配置注入"
    fi
}

# 检查依赖
check_dependencies() {
    log_step "检查部署依赖..."
    
    # 检查 kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 kubectl 连接
    if ! kubectl version --client &> /dev/null; then
        log_error "kubectl 客户端检查失败"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        log_info "请检查 kubeconfig 配置和集群状态"
        exit 1
    fi
    
    # 检查 Docker（如果需要拉取镜像）
    if [[ "$PULL_FIRST" == true ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装或不在 PATH 中"
            exit 1
        fi
        
        if ! docker info &> /dev/null; then
            log_error "Docker 服务未运行"
            exit 1
        fi
    fi
    
    log_success "依赖检查通过"
}

# 检查私有仓库连通性
check_registry_connectivity() {
    log_step "检查私有仓库连通性..."
    
    local registry_host=$(echo "$IMAGE_REGISTRY" | cut -d'/' -f1)
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将检查仓库连通性: $registry_host"
        return 0
    fi
    
    # 检查网络连通性
    if ! ping -c 1 -W 3 "$registry_host" >/dev/null 2>&1; then
        log_error "无法连接到私有仓库: $registry_host"
        log_info "请检查网络连接和仓库地址"
        exit 1
    fi
    
    log_success "私有仓库连通性检查通过"
}

# 强制删除旧Pod（确保使用最新镜像）
force_delete_old_pods() {
    log_step "强制删除旧Pod以确保使用最新镜像..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将删除命名空间 $NAMESPACE 中的所有 $APP_NAME Pod"
        return 0
    fi
    
    # 获取现有Pod列表
    log_info "检查现有Pod..."
    local existing_pods
    existing_pods=$(kubectl get pods -n "$NAMESPACE" -l app="$APP_NAME" --no-headers 2>/dev/null | awk '{print $1}' || true)
    
    if [[ -n "$existing_pods" ]]; then
        local pod_count=$(echo "$existing_pods" | wc -l)
        log_info "发现 $pod_count 个现有Pod，准备删除以强制拉取最新镜像..."
        
        if [[ "$VERBOSE" == true ]]; then
            log_info "现有Pod列表:"
            echo "$existing_pods" | sed 's/^/  /'
        fi
        
        # 强制删除Pod
        kubectl delete pods -n "$NAMESPACE" -l app="$APP_NAME" --grace-period=0 --force 2>/dev/null || {
            log_warning "强制删除Pod命令执行完成，继续等待删除确认..."
        }
        
        # 等待Pod完全删除 - 优化的等待逻辑
        log_info "等待Pod完全删除..."
        local wait_count=0
        local max_wait=60  # 增加最大等待时间到60秒
        local check_interval=3  # 检查间隔改为3秒
        
        while [[ $wait_count -lt $max_wait ]]; do
            # 使用更精确的Pod检查方式 - 只检查正在删除的Pod
            local terminating_pods
            terminating_pods=$(kubectl get pods -n "$NAMESPACE" -l app="$APP_NAME" --no-headers 2>/dev/null | grep "Terminating" | wc -l || echo "0")
            
            # 如果没有正在终止的Pod，说明删除完成
            if [[ "$terminating_pods" == "0" ]]; then
                log_success "Pod删除操作完成（可能有新Pod已创建）"
                return 0
            fi
            
            # 显示删除进度
            local all_pods
            all_pods=$(kubectl get pods -n "$NAMESPACE" -l app="$APP_NAME" --no-headers 2>/dev/null | wc -l || echo "0")
            
            if [[ "$terminating_pods" -gt 0 ]]; then
                log_info "等待Pod删除完成... (${terminating_pods}个Pod正在终止中, $((wait_count * check_interval))/${max_wait}s)"
            else
                log_info "等待Pod删除完成... (当前${all_pods}个Pod, $((wait_count * check_interval))/${max_wait}s)"
            fi
            
            # 详细调试信息（verbose模式）
            if [[ "$VERBOSE" == true ]]; then
                kubectl get pods -n "$NAMESPACE" -l app="$APP_NAME" 2>/dev/null | sed 's/^/  [DEBUG] /' || true
            fi
            
            sleep $check_interval
            ((wait_count++))
        done
        
        # 超时处理
        log_warning "Pod删除等待超时，但继续部署流程"
        log_info "当前Pod状态:"
        kubectl get pods -n "$NAMESPACE" -l app="$APP_NAME" 2>/dev/null || log_info "无法获取Pod状态"
        
        # 尝试最后一次强制删除
        log_info "尝试最终强制清理..."
        kubectl delete pods -n "$NAMESPACE" -l app="$APP_NAME" --grace-period=0 --force --ignore-not-found=true 2>/dev/null || true
        sleep 5
        
    else
        log_info "未找到现有Pod，将进行全新部署"
    fi
    
    log_success "Pod清理阶段完成"
}

# 拉取最新镜像
pull_latest_image() {
    log_step "拉取私有仓库最新镜像..."
    
    local full_image="${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将拉取镜像: $full_image"
        return 0
    fi
    
    log_info "拉取镜像: $full_image"
    
    # 如果是强制latest模式，先删除本地镜像缓存
    if [[ "$FORCE_LATEST" == true ]]; then
        log_info "删除本地镜像缓存以确保拉取最新版本..."
        docker rmi "$full_image" 2>/dev/null || true
    fi
    
    if docker pull "$full_image"; then
        log_success "镜像拉取成功"
        
        # 显示镜像信息
        if [[ "$VERBOSE" == true ]]; then
            log_info "镜像详细信息："
            docker inspect "$full_image" --format='{{.Id}}' | sed 's/^/  镜像ID: /'
            docker inspect "$full_image" --format='{{.Created}}' | sed 's/^/  创建时间: /'
        fi
    else
        log_error "镜像拉取失败，请检查私有仓库连接或镜像是否存在"
        log_info "您可以先运行构建脚本: ../build-and-push.sh"
        exit 1
    fi
}

# 检查命名空间
check_namespace() {
    log_step "检查命名空间: $NAMESPACE"
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 不存在，正在创建..."
        
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] 将创建命名空间: $NAMESPACE"
        else
            kubectl apply -f "$SCRIPT_DIR/namespace.yaml"
            log_success "命名空间创建成功"
        fi
    else
        log_info "命名空间 $NAMESPACE 已存在"
    fi
}

# 应用配置文件
apply_configs() {
    log_step "应用${ENVIRONMENT}环境配置文件..."
    
    local config_files=(
        "configmap.yaml"
        "secret.yaml"
    )
    
    for config_file in "${config_files[@]}"; do
        local file_path="$ENV_CONFIG_DIR/$config_file"
        
        if [[ -f "$file_path" ]]; then
            log_info "应用${ENVIRONMENT}环境配置: $config_file"
            
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] 将应用: $ENVIRONMENT/$config_file"
                if [[ "$VERBOSE" == true ]]; then
                    echo "文件内容预览:"
                    head -10 "$file_path" | sed 's/^/  /'
                    echo "  ..."
                fi
            else
                kubectl apply -f "$file_path"
            fi
        else
            log_error "环境配置文件不存在: $file_path"
            log_info "请确保 $ENVIRONMENT/$config_file 文件存在"
            exit 1
        fi
    done
    
    log_success "${ENVIRONMENT}环境配置文件应用完成"
}

# 更新部署镜像
update_deployment() {
    log_step "更新部署镜像..."
    
    local full_image="${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    local deployment_name="$APP_NAME"
    
    log_info "更新镜像为: $full_image"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将更新部署 $deployment_name 的镜像为: $full_image"
        return 0
    fi
    
    # 首先应用部署文件
    if [[ -f "$SCRIPT_DIR/deployment.yaml" ]]; then
        log_info "应用部署文件..."
        kubectl apply -f "$SCRIPT_DIR/deployment.yaml"
    fi
    
    # 更新镜像
    kubectl set image deployment/$deployment_name \
        $APP_NAME=$full_image \
        -n $NAMESPACE
    
    # 如果是强制更新模式，添加重启注解确保Pod重建
    if [[ "$FORCE_UPDATE" == true ]]; then
        log_info "添加重启注解确保Pod重建..."
        kubectl patch deployment $deployment_name -n $NAMESPACE -p \
            "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +'%Y-%m-%dT%H:%M:%S%z')\"}}}}}"
    fi
    
    log_success "部署镜像更新完成"
}

# 应用服务配置
apply_services() {
    log_step "应用服务配置..."
    
    # 检查是否有服务配置文件
    local service_files=(
        "service.yaml"
    )
    
    for service_file in "${service_files[@]}"; do
        local file_path="$SCRIPT_DIR/$service_file"
        
        if [[ -f "$file_path" ]]; then
            log_info "应用服务配置: $service_file"
            
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] 将应用服务配置: $service_file"
            else
                kubectl apply -f "$file_path"
            fi
            break
        fi
    done
    
    log_success "服务配置应用完成"
}

# 等待部署完成
wait_for_deployment() {
    log_step "等待部署完成..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将等待部署滚动更新完成"
        return 0
    fi
    
    log_info "等待部署 $APP_NAME 滚动更新完成..."
    
    # 使用更详细的等待逻辑，避免卡住
    local wait_count=0
    local max_rollout_wait=300  # 5分钟超时
    local check_interval=10
    
    while [[ $wait_count -lt $((max_rollout_wait / check_interval)) ]]; do
        # 检查部署状态
        local rollout_status
        rollout_status=$(kubectl rollout status deployment/$APP_NAME -n $NAMESPACE --timeout=${check_interval}s 2>&1)
        local rollout_exit_code=$?
        
        if [[ $rollout_exit_code -eq 0 ]]; then
            log_success "部署滚动更新完成"
            return 0
        fi
        
        # 显示进度信息
        local ready_replicas
        ready_replicas=$(kubectl get deployment $APP_NAME -n $NAMESPACE -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local desired_replicas
        desired_replicas=$(kubectl get deployment $APP_NAME -n $NAMESPACE -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "1")
        
        log_info "部署进度: ${ready_replicas}/${desired_replicas} Pod就绪 ($((wait_count * check_interval))/${max_rollout_wait}s)"
        
        # 显示Pod状态（verbose模式）
        if [[ "$VERBOSE" == true ]]; then
            log_info "当前Pod状态:"
            kubectl get pods -n $NAMESPACE -l app=$APP_NAME --no-headers 2>/dev/null | sed 's/^/  [DEBUG] /' || true
        fi
        
        ((wait_count++))
    done
    
    # 超时处理
    log_error "部署滚动更新超时"
    
    # 显示详细的故障信息
    log_info "部署状态:"
    kubectl get deployment $APP_NAME -n $NAMESPACE -o wide 2>/dev/null || log_warning "无法获取部署状态"
    
    log_info "Pod状态:"
    kubectl get pods -n $NAMESPACE -l app=$APP_NAME 2>/dev/null || log_warning "无法获取Pod状态"
    
    log_info "最新Pod日志:"
    kubectl logs -n $NAMESPACE -l app=$APP_NAME --tail=50 2>/dev/null || log_warning "无法获取Pod日志"
    
    # 显示部署事件
    log_info "相关事件:"
    kubectl get events -n $NAMESPACE --field-selector involvedObject.name=$APP_NAME --sort-by='.firstTimestamp' 2>/dev/null | tail -10 || true
    
    return 1
}

# 健康检查
health_check() {
    log_step "执行健康检查..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将执行健康检查"
        return 0
    fi
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o wide
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get services -n $NAMESPACE -l app=$APP_NAME
    
    # 等待Pod就绪
    log_info "等待Pod就绪..."
    local ready_pod=""
    local wait_count=0
    local check_interval=5  # 检查间隔改为5秒
    local max_checks=$((HEALTH_CHECK_TIMEOUT / check_interval))
    
    while [[ $wait_count -lt $max_checks ]]; do
        # 使用超时保护的kubectl命令
        ready_pod=$(timeout 10 kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' 2>/dev/null | awk '{print $1}' || echo "")
        
        if [[ -n "$ready_pod" ]]; then
            # 检查Pod是否真正就绪
            local ready_status
            ready_status=$(timeout 10 kubectl get pod -n $NAMESPACE "$ready_pod" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null || echo "")
            
            if [[ "$ready_status" == "True" ]]; then
                log_success "Pod $ready_pod 已就绪"
                break
            else
                log_info "Pod $ready_pod 运行中但未完全就绪... ($((wait_count * check_interval))/${HEALTH_CHECK_TIMEOUT}s)"
            fi
        else
            log_info "等待Pod启动... ($((wait_count * check_interval))/${HEALTH_CHECK_TIMEOUT}s)"
        fi
        
        # 显示详细Pod状态（verbose模式）
        if [[ "$VERBOSE" == true ]]; then
            log_info "当前Pod状态详情:"
            timeout 10 kubectl get pods -n $NAMESPACE -l app=$APP_NAME --no-headers 2>/dev/null | sed 's/^/  [DEBUG] /' || log_info "  [DEBUG] 无法获取Pod状态"
        fi
        
        sleep $check_interval
        ((wait_count++))
    done
    
    if [[ -z "$ready_pod" ]]; then
        log_error "Pod在 ${HEALTH_CHECK_TIMEOUT}s 内未能就绪"
        log_info "Pod状态详情："
        kubectl describe pods -n $NAMESPACE -l app=$APP_NAME
        return 1
    fi
    
    # 执行内部健康检查 - 增加Pod稳定性等待
    log_info "开始执行内部健康检查..."
    
    # 额外等待Pod完全稳定（避免Pod刚创建就检查）
    log_info "等待Pod完全稳定..."
    sleep 10
    
    local health_attempts=0
    local max_health_attempts=15  # 增加最大尝试次数
    
    while [[ $health_attempts -lt $max_health_attempts ]]; do
        # 每次都重新获取当前存在的Pod名称，避免Pod重新创建导致的名称过期
        local current_pod
        current_pod=$(timeout 10 kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' 2>/dev/null | awk '{print $1}' || echo "")
        
        if [[ -z "$current_pod" ]]; then
            log_info "健康检查尝试 $((health_attempts + 1))/$max_health_attempts - 等待Pod启动..."
            sleep 5
            ((health_attempts++))
            continue
        fi
        
        # 验证Pod是否真正就绪
        local pod_ready_status
        pod_ready_status=$(timeout 10 kubectl get pod -n $NAMESPACE "$current_pod" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null || echo "")
        
        if [[ "$pod_ready_status" != "True" ]]; then
            log_info "健康检查尝试 $((health_attempts + 1))/$max_health_attempts - Pod $current_pod 未完全就绪..."
            sleep 5
            ((health_attempts++))
            continue
        fi
        
        # 使用Node.js内置http模块进行健康检查
        local health_check_result=""
        local health_exit_code=1
        
        log_info "健康检查尝试 $((health_attempts + 1))/$max_health_attempts - 使用Pod $current_pod"
        # 使用Node.js内置http模块进行健康检查（避免依赖curl）
        health_check_result=$(kubectl exec -n $NAMESPACE "$current_pod" -- node -e "
            const http = require('http');
            const req = http.get('http://localhost:10086/api/health', (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200) {
                        console.log('Health check passed');
                        process.exit(0);
                    } else {
                        console.log('Health check failed with status:', res.statusCode);
                        process.exit(1);
                    }
                });
            });
            req.on('error', (err) => {
                console.log('Health check error:', err.message);
                process.exit(1);
            });
            req.setTimeout(5000, () => {
                console.log('Health check timeout');
                req.destroy();
                process.exit(1);
            });
        " 2>&1)
        health_exit_code=$?
        
        if [[ $health_exit_code -eq 0 ]]; then
            log_success "内部健康检查通过 ✅"
            return 0
        fi
        
        # 健康检查失败，显示详细错误信息
        if [[ "$VERBOSE" == true ]] || [[ $health_attempts -ge 3 ]]; then
            log_error "健康检查详细错误:"
            echo "  命令输出: $health_check_result"
            
            # 显示Pod日志（最后10行）
            log_info "Pod最新日志:"
            kubectl logs -n $NAMESPACE "$current_pod" -c $APP_NAME --tail=10 2>/dev/null | sed 's/^/  /' || log_warning "无法获取Pod日志"
            
            # 检查端口是否监听
            log_info "检查端口监听状态:"
            kubectl exec -n $NAMESPACE "$current_pod" -c $APP_NAME -- netstat -tlnp 2>/dev/null | grep ":10086" | sed 's/^/  /' || log_info "  端口10086未在监听"
            
            # 检查进程状态
            log_info "检查应用进程:"
            kubectl exec -n $NAMESPACE "$current_pod" -c $APP_NAME -- ps aux 2>/dev/null | grep -E "(node|specific)" | sed 's/^/  /' || log_info "  未找到相关进程"
        fi
        
        sleep 5
        ((health_attempts++))
    done
    
    # 健康检查完全失败，执行详细诊断
    log_error "内部健康检查失败，执行详细诊断..."
    
    # 获取当前可用的Pod进行诊断
    local final_diagnosis_pod
    final_diagnosis_pod=$(timeout 10 kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -n "$final_diagnosis_pod" ]]; then
        quick_diagnosis "$final_diagnosis_pod"
    else
        log_error "无法找到可用的Pod进行诊断"
        kubectl get pods -n $NAMESPACE -l app=$APP_NAME 2>/dev/null || true
    fi
    
    return 1
}

# 连通性测试
connectivity_test() {
    if [[ "$SKIP_TESTS" == true ]]; then
        log_info "跳过连通性测试"
        return 0
    fi
    
    log_step "执行连通性测试..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] 将执行连通性测试"
        return 0
    fi
    
    local test_passed=0
    local test_total=0
    
    # 获取就绪的Pod，增加就绪状态验证
    local ready_pod
    ready_pod=$(kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' 2>/dev/null | awk '{print $1}')
    
    if [[ -z "$ready_pod" ]]; then
        log_error "未找到运行中的Pod，无法执行连通性测试"
        kubectl get pods -n $NAMESPACE -l app=$APP_NAME 2>/dev/null || true
        return 1
    fi
    
    # 验证Pod就绪状态
    local pod_ready_status
    pod_ready_status=$(kubectl get pod -n $NAMESPACE "$ready_pod" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null || echo "")
    
    if [[ "$pod_ready_status" != "True" ]]; then
        log_error "Pod $ready_pod 未完全就绪，无法执行连通性测试"
        return 1
    fi
    
    log_info "使用Pod $ready_pod 执行连通性测试"
    
    echo -e "\n${MAGENTA}=== 连通性测试结果 ===${NC}"
    
    # 测试1: Pod内部健康检查
    echo -n "1. Pod内部健康检查 (10086端口): "
    ((test_total++))
    local test1_result
    test1_result=$(timeout $CONNECTION_TEST_TIMEOUT kubectl exec -n $NAMESPACE "$ready_pod" -c $APP_NAME -- node -e "
        const http = require('http');
        const req = http.get('http://localhost:10086/api/health', (res) => {
            if (res.statusCode === 200) {
                console.log('OK');
                process.exit(0);
            } else {
                process.exit(1);
            }
        });
        req.on('error', () => process.exit(1));
        req.setTimeout(5000, () => { req.destroy(); process.exit(1); });
    " 2>&1)
    local test1_exit_code=$?
    
    if [[ $test1_exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((test_passed++))
    else
        echo -e "${RED}❌ 失败${NC}"
        if [[ "$VERBOSE" == true ]]; then
            echo "     错误详情: $test1_result"
        fi
    fi
    
    # 测试2: ClusterIP服务访问
    echo -n "2. ClusterIP服务访问: "
    ((test_total++))
    local cluster_ip
    cluster_ip=$(timeout 10 kubectl get service ${APP_NAME}-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}' 2>/dev/null || echo "")
    
    if [[ -n "$cluster_ip" ]]; then
        # 使用更可靠的测试方法
        if timeout $CONNECTION_TEST_TIMEOUT kubectl run connectivity-test-$(date +%s) --rm -i --restart=Never --image=busybox --quiet -- wget -q --spider --timeout=10 http://$cluster_ip:10086/api/health 2>/dev/null; then
            echo -e "${GREEN}✅ 通过${NC} (ClusterIP: $cluster_ip)"
            ((test_passed++))
        else
            echo -e "${RED}❌ 失败${NC} (ClusterIP: $cluster_ip)"
        fi
    else
        echo -e "${RED}❌ 失败${NC} (无法获取ClusterIP)"
    fi
    
    # 测试3: NodePort外部访问
    echo -n "3. NodePort外部访问 (30086端口): "
    ((test_total++))
    local node_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}' 2>/dev/null)
    if [[ -n "$node_ip" ]] && timeout $CONNECTION_TEST_TIMEOUT curl -s http://$node_ip:30086/api/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过${NC} (NodeIP: $node_ip)"
        ((test_passed++))
    else
        echo -e "${YELLOW}⚠️  需要手动验证${NC} (可能需要防火墙配置)"
        echo "   测试命令: curl http://$node_ip:30086/api/health"
    fi
    
    echo
    echo -e "${CYAN}测试结果: $test_passed/$test_total 通过${NC}"
    
    if [[ $test_passed -eq $test_total ]]; then
        log_success "所有连通性测试通过！🎉"
    elif [[ $test_passed -gt 0 ]]; then
        log_warning "部分连通性测试通过，请检查失败的测试项"
    else
        log_error "所有连通性测试失败，请检查部署状态"
        return 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "显示部署信息..."
    
    if [[ "$DRY_RUN" == true ]]; then
        echo
        echo "=== DRY-RUN 模式部署信息 ==="
        echo "环境: $ENVIRONMENT"
        echo "命名空间: $NAMESPACE"
        echo "应用名称: $APP_NAME"
        echo "镜像: ${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
        echo "强制更新: $FORCE_UPDATE"
        echo "强制latest: $FORCE_LATEST"
        echo "先拉取: $PULL_FIRST"
        echo
        return 0
    fi
    
    echo
    echo -e "${CYAN}=== 部署完成信息 ===${NC}"
    echo "环境: $ENVIRONMENT"
    echo "命名空间: $NAMESPACE"
    echo "应用名称: $APP_NAME"
    echo "镜像: ${IMAGE_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    echo "部署时间: $(date)"
    echo
    
    echo -e "${CYAN}=== 资源状态 ===${NC}"
    echo "Deployment:"
    kubectl get deployment $APP_NAME -n $NAMESPACE -o wide
    echo
    echo "Pods:"
    kubectl get pods -n $NAMESPACE -l app=$APP_NAME -o wide
    echo
    echo "Services:"
    kubectl get services -n $NAMESPACE -l app=$APP_NAME
    echo
    
    # 获取访问信息
    local node_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}' 2>/dev/null)
    local cluster_ip=$(kubectl get service ${APP_NAME}-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}' 2>/dev/null)
    
    echo -e "${CYAN}=== 访问信息 ===${NC}"
    if [[ -n "$node_ip" ]]; then
        echo "NodePort访问: http://$node_ip:30086"
        echo "NodePort健康检查: http://$node_ip:30086/api/health"
    fi
    if [[ -n "$cluster_ip" ]]; then
        echo "ClusterIP访问: http://$cluster_ip:10086 (集群内部)"
    fi
    echo
    
    echo -e "${CYAN}=== 常用命令 ===${NC}"
    echo "查看实时日志:"
    echo "  kubectl logs -f -n $NAMESPACE -l app=$APP_NAME"
    echo
    echo "查看Pod详情:"
    echo "  kubectl describe pod -n $NAMESPACE -l app=$APP_NAME"
    echo
    echo "进入Pod:"
    echo "  kubectl exec -it -n $NAMESPACE \$(kubectl get pod -n $NAMESPACE -l app=$APP_NAME -o jsonpath='{.items[0].metadata.name}') -- /bin/sh"
    echo
    echo "重启部署:"
    echo "  kubectl rollout restart deployment/$APP_NAME -n $NAMESPACE"
    echo
    echo "强制更新最新镜像:"
    echo "  ./deploy-k8s.sh --force-latest"
    echo
    echo "手动连通性测试:"
    if [[ -n "$node_ip" ]]; then
        echo "  curl http://$node_ip:30086/api/health"
    fi
    echo
}

# 主函数
main() {
    echo -e "${CYAN}🚀 SpecificAI Auth Kubernetes 一键部署脚本${NC}"
    echo "================================================"
    
    # 解析参数
    parse_args "$@"
    
    # 显示配置信息
    log_info "部署配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  配置目录: $ENV_CONFIG_DIR"
    log_info "  镜像标签: $IMAGE_TAG (统一业务逻辑)"
    log_info "  命名空间: $NAMESPACE"
    log_info "  强制更新: $FORCE_UPDATE"
    log_info "  强制latest: $FORCE_LATEST"
    log_info "  先拉取: $PULL_FIRST"
    log_info "  干运行: $DRY_RUN"
    log_info "  跳过测试: $SKIP_TESTS"
    echo
    
    # 记录开始时间
    local script_start_time=$(date +%s)
    
    # 检查依赖
    local step_start_time=$(date +%s)
    check_dependencies
    check_script_health "check_dependencies" $step_start_time
    
    # 检查私有仓库连通性
    step_start_time=$(date +%s)
    check_registry_connectivity
    check_script_health "check_registry_connectivity" $step_start_time
    
    # 如果是强制latest模式，先删除旧Pod
    if [[ "$FORCE_LATEST" == true ]]; then
        step_start_time=$(date +%s)
        force_delete_old_pods
        check_script_health "force_delete_old_pods" $step_start_time
    fi
    
    # 拉取最新镜像（如果需要）
    if [[ "$PULL_FIRST" == true ]]; then
        step_start_time=$(date +%s)
        pull_latest_image
        check_script_health "pull_latest_image" $step_start_time
    fi
    
    # 检查命名空间
    step_start_time=$(date +%s)
    check_namespace
    check_script_health "check_namespace" $step_start_time
    
    # 应用配置
    step_start_time=$(date +%s)
    apply_configs
    check_script_health "apply_configs" $step_start_time
    
    # 更新部署
    step_start_time=$(date +%s)
    update_deployment
    check_script_health "update_deployment" $step_start_time
    
    # 应用服务
    step_start_time=$(date +%s)
    apply_services
    check_script_health "apply_services" $step_start_time
    
    # 等待部署完成
    step_start_time=$(date +%s)
    wait_for_deployment || {
        log_error "部署等待失败，但继续执行后续检查"
    }
    check_script_health "wait_for_deployment" $step_start_time
    
    # 健康检查
    step_start_time=$(date +%s)
    if health_check; then
        log_success "健康检查通过"
    else
        log_warning "健康检查未完全通过，但继续执行连通性测试"
    fi
    check_script_health "health_check" $step_start_time
    
    # 连通性测试
    step_start_time=$(date +%s)
    connectivity_test
    check_script_health "connectivity_test" $step_start_time
    
    # 显示部署信息
    show_deployment_info
    
    # 计算总执行时间
    local script_end_time=$(date +%s)
    local total_elapsed=$((script_end_time - script_start_time))
    local minutes=$((total_elapsed / 60))
    local seconds=$((total_elapsed % 60))
    
    echo "================================================"
    log_info "总执行时间: ${minutes}分${seconds}秒"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_success "🎉 DRY-RUN 模式完成！使用 --force-latest 参数执行实际部署"
    else
        log_success "🎉 部署流程完成！服务已就绪并通过连通性测试"
    fi
}

# 执行主函数
main "$@"