apiVersion: v1
kind: ConfigMap
metadata:
  name: specific-ai-auth-config
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
    environment: dev
data:
  # 服务器配置
  PORT: "10086"
  HOST: "0.0.0.0"
  
  # 环境标识
  NODE_ENV: "development"
  
  # 认证配置（非敏感信息）
  BETTER_AUTH_URL: "http://localhost:10086"
  CORS_ORIGIN: "*"
  
  # 开发环境业务服务API配置
  BUSINESS_BASE_API: "http://***************:8000"
  
  # Google OAuth 配置（非敏感部分）
  GOOGLE_CLIENT_ID: "your-dev-google-client-id.apps.googleusercontent.com"
  
  # 开发环境特有配置
  DEBUG_MODE: "true"
  LOG_LEVEL: "debug"