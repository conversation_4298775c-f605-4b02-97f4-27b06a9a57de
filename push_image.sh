#!/bin/bash

set -e

# 配置
DOCKERFILE_PATH="./Dockerfile"
BASE_IMAGE_NAME="specific-ai-auth"
PRIVATE_REGISTRY="192.168.50.112"
CONTEXT_PATH="."
TAG="latest"

# 完整镜像名
LOCAL_IMAGE_FULL_NAME="${BASE_IMAGE_NAME}:${TAG}"
REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"

echo "--------------------------------------------------"
echo "构建和推送 ${BASE_IMAGE_NAME} Docker 镜像"
echo "镜像标签: ${TAG}"
echo "--------------------------------------------------"

# 检查Dockerfile是否存在
if [ ! -f "${DOCKERFILE_PATH}" ]; then
    echo "错误: ${DOCKERFILE_PATH} 不存在"
    exit 1
fi

# 删除旧的本地镜像（如果存在）
if docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "^${BASE_IMAGE_NAME}:${TAG}$" >/dev/null; then
    echo "删除旧的本地镜像: ${LOCAL_IMAGE_FULL_NAME}"
    docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true
fi

# 构建新镜像（使用缓存优化构建速度）
echo "构建 Docker 镜像: ${LOCAL_IMAGE_FULL_NAME}"
echo "使用缓存优化构建速度，如需强制重新构建请传参数 --no-cache"

# 检查是否传入 --no-cache 参数
NO_CACHE_FLAG=""
if [[ "$*" == *"--no-cache"* ]]; then
    NO_CACHE_FLAG="--no-cache"
    echo "检测到 --no-cache 参数，将强制重新构建所有层级"
fi

docker build $NO_CACHE_FLAG \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    -f "${DOCKERFILE_PATH}" \
    -t "${LOCAL_IMAGE_FULL_NAME}" \
    "${CONTEXT_PATH}"

# 标记并推送
echo "标记 Docker 镜像: ${REMOTE_IMAGE_FULL_NAME}"
docker tag "${LOCAL_IMAGE_FULL_NAME}" "${REMOTE_IMAGE_FULL_NAME}"

echo "推送 Docker 镜像到私有仓库: ${REMOTE_IMAGE_FULL_NAME}"
docker push "${REMOTE_IMAGE_FULL_NAME}"

echo "清理本地镜像..."
docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true
docker rmi "${REMOTE_IMAGE_FULL_NAME}" || true

echo "推送完成: ${REMOTE_IMAGE_FULL_NAME}"
echo "--------------------------------------------------" 