import { Resend } from 'resend';
import { randomUUID } from 'crypto';
import {
  EmailServiceConfig,
  EmailSendResult,
  EmailTemplateData,
  EmailType,
  EmailTemplateVersion,
} from '../types/email.js';
import { EmailTemplateManager } from './email-templates.js';
import { emailCacheService, EmailCacheService } from './cache-service.js';
import { db } from '../lib/drizzle.js';
import { emailLog } from '../lib/auth-schema.js';
import { eq, and, sql } from 'drizzle-orm';

/**
 * 邮件服务类 - 提供统一的邮件发送接口
 */
export class EmailService {
  private resend: Resend;
  private config: EmailServiceConfig;
  private templateManager: EmailTemplateManager;
  private cacheService: EmailCacheService;

  constructor(config: EmailServiceConfig) {
    this.config = config;
    this.resend = new Resend(config.apiKey);
    this.templateManager = new EmailTemplateManager();
    this.cacheService = emailCacheService;
  }

  /**
   * 发送邮箱验证邮件（增强版，支持缓存和频率限制）
   */
  async sendVerificationEmail(options: {
    userEmail: string;
    userName?: string;
    verificationUrl: string;
    inviteCode?: string;
    version?: EmailTemplateVersion;
    userId?: string;
  }): Promise<EmailSendResult> {
    const { userEmail, userName, verificationUrl, inviteCode, version, userId } = options;
    const emailType = 'verification';
    const logId = randomUUID();

    try {
      // 1. 检查缓存中的发送状态和频率限制
      if (userId && !this.cacheService.canSendEmail(userId, emailType, 2 * 60 * 1000)) { // 2分钟内不能重复发送
        const cachedStatus = this.cacheService.getEmailStatus(userId, emailType);
        console.log(`⏱️ 邮件发送频率限制: ${userEmail}, 上次发送: ${cachedStatus?.lastAttemptAt}`);
        
        return {
          success: false,
          error: '邮件发送过于频繁，请稍后再试',
          rateLimited: true,
          retryAfter: cachedStatus?.nextRetryAt,
        };
      }

      // 2. 记录邮件发送开始日志
      await this.logEmailAttempt(logId, {
        userId,
        email: userEmail,
        emailType,
        status: 'pending',
        provider: 'resend',
        attemptCount: 1,
        metadata: {
          version: version || this.config.defaultVersion,
          hasInviteCode: !!inviteCode,
          verificationUrl,
        },
      });

      console.log(`📧 准备发送邮箱验证邮件到: ${userEmail}`, {
        logId,
        version: version || this.config.defaultVersion,
        hasInviteCode: !!inviteCode,
      });

      const templateData: EmailTemplateData = {
        userEmail,
        userName,
        verificationUrl,
        inviteCode,
      };

      const template = this.templateManager.render({
        type: 'verification',
        version: version || this.config.defaultVersion,
        data: templateData,
      });

      // 3. 发送邮件
      const result = await this.resend.emails.send({
        from: `${this.config.fromName} <${this.config.fromEmail}>`,
        to: [userEmail],
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: '<EMAIL>',
      });

      if (result.error) {
        console.error('❌ 邮箱验证邮件发送失败:', result.error);
        
        // 4a. 更新失败状态
        await this.updateEmailStatus(logId, 'failed', result.error.message);
        if (userId) {
          this.cacheService.updateEmailAttempt(userId, emailType, false, undefined, result.error.message);
        }
        
        return {
          success: false,
          error: `邮件发送失败: ${result.error.message}`,
        };
      }

      // 4b. 更新成功状态
      const messageId = result.data?.id;
      await this.updateEmailStatus(logId, 'sent', undefined, messageId);
      if (userId) {
        this.cacheService.updateEmailAttempt(userId, emailType, true, messageId);
      }

      console.log('✅ 邮箱验证邮件发送成功:', {
        logId,
        messageId,
        recipient: userEmail,
        version: version || this.config.defaultVersion,
      });

      return {
        success: true,
        messageId,
        logId,
      };
    } catch (error) {
      console.error('❌ 邮箱验证邮件发送异常:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      // 更新异常状态
      await this.updateEmailStatus(logId, 'failed', errorMessage);
      if (userId) {
        this.cacheService.updateEmailAttempt(userId, emailType, false, undefined, errorMessage);
      }
      
      return {
        success: false,
        error: `邮件发送异常: ${errorMessage}`,
      };
    }
  }

  /**
   * 发送邀请邮件
   */
  async sendInviteEmail(options: {
    userEmail: string;
    userName?: string;
    inviteCode: string;
    inviteUrl: string;
    inviterName?: string;
    companyName?: string;
    version?: EmailTemplateVersion;
    language?: string;
  }): Promise<EmailSendResult> {
    const { 
      userEmail, 
      userName, 
      inviteCode, 
      inviteUrl, 
      inviterName, 
      companyName, 
      version,
      language = 'zh-CN'
    } = options;
    const logId = randomUUID();

    try {
      // 记录邮件发送开始日志
      await this.logEmailAttempt(logId, {
        email: userEmail,
        emailType: 'invite',
        status: 'pending',
        provider: 'resend',
        attemptCount: 1,
        metadata: {
          version: version || this.config.defaultVersion,
          inviteCode,
          inviterName,
          companyName,
          language
        },
      });

      console.log(`📧 准备发送邀请邮件到: ${userEmail}`, {
        logId,
        version: version || this.config.defaultVersion,
        inviteCode,
        inviterName,
        companyName
      });

      const templateData: EmailTemplateData = {
        userEmail,
        userName,
        inviteCode,
        inviteUrl,
        inviterName,
        companyName,
        locale: language
      };

      const template = this.templateManager.render({
        type: 'invite',
        version: version || this.config.defaultVersion,
        data: templateData,
        language
      });

      // 发送邮件
      const result = await this.resend.emails.send({
        from: `${this.config.fromName} <${this.config.fromEmail}>`,
        to: [userEmail],
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: this.config.replyTo,
      });

      if (result.error) {
        console.error('❌ 邀请邮件发送失败:', result.error);
        
        // 更新失败状态
        await this.updateEmailStatus(logId, 'failed', result.error.message);
        
        return {
          success: false,
          error: `邮件发送失败: ${result.error.message}`,
        };
      }

      // 更新成功状态
      const messageId = result.data?.id;
      await this.updateEmailStatus(logId, 'sent', undefined, messageId);

      console.log('✅ 邀请邮件发送成功:', {
        logId,
        messageId,
        recipient: userEmail,
        version: version || this.config.defaultVersion,
      });

      return {
        success: true,
        messageId,
        logId,
      };
    } catch (error) {
      console.error('❌ 邀请邮件发送异常:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      // 更新异常状态
      await this.updateEmailStatus(logId, 'failed', errorMessage);
      
      return {
        success: false,
        error: `邮件发送异常: ${errorMessage}`,
      };
    }
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(options: {
    userEmail: string;
    userName?: string;
    resetPasswordUrl: string;
    version?: EmailTemplateVersion;
  }): Promise<EmailSendResult> {
    const { userEmail, userName, resetPasswordUrl, version } = options;

    try {
      console.log(`📧 准备发送密码重置邮件到: ${userEmail}`, {
        version: version || this.config.defaultVersion,
      });

      const templateData: EmailTemplateData = {
        userEmail,
        userName,
        resetPasswordUrl,
      };

      const template = this.templateManager.render({
        type: 'password-reset',
        version: version || this.config.defaultVersion,
        data: templateData,
      });

      const result = await this.resend.emails.send({
        from: `${this.config.fromName} <${this.config.fromEmail}>`,
        to: [userEmail],
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      if (result.error) {
        console.error('❌ 密码重置邮件发送失败:', result.error);
        return {
          success: false,
          error: `邮件发送失败: ${result.error.message}`,
        };
      }

      console.log('✅ 密码重置邮件发送成功:', {
        messageId: result.data?.id,
        recipient: userEmail,
        version: version || this.config.defaultVersion,
      });

      return {
        success: true,
        messageId: result.data?.id,
      };
    } catch (error) {
      console.error('❌ 密码重置邮件发送异常:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        error: `邮件发送异常: ${errorMessage}`,
      };
    }
  }

  /**
   * 测试邮件服务连接
   */
  async testConnection(): Promise<EmailSendResult> {
    try {
      // 发送一个简单的测试邮件给 Resend 的测试地址
      const result = await this.resend.emails.send({
        from: `${this.config.fromName} <${this.config.fromEmail}>`,
        to: ['<EMAIL>'], // Resend 提供的测试地址
        subject: '测试邮件 - EmailService 连接测试',
        html: '<p>这是一封测试邮件，用于验证 EmailService 连接是否正常。</p>',
        text: '这是一封测试邮件，用于验证 EmailService 连接是否正常。',
      });

      if (result.error) {
        return {
          success: false,
          error: `连接测试失败: ${result.error.message}`,
        };
      }

      return {
        success: true,
        messageId: result.data?.id,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        error: `连接测试异常: ${errorMessage}`,
      };
    }
  }

  /**
   * 获取当前配置信息
   */
  getConfig(): Omit<EmailServiceConfig, 'apiKey'> {
    return {
      fromEmail: this.config.fromEmail,
      fromName: this.config.fromName,
      defaultVersion: this.config.defaultVersion,
      replyTo: this.config.replyTo,
    };
  }

  /**
   * 切换默认模板版本
   */
  setDefaultVersion(version: EmailTemplateVersion): void {
    if (!this.templateManager.isVersionAvailable(version)) {
      throw new Error(`不支持的模板版本: ${version}`);
    }
    this.config.defaultVersion = version;
    console.log(`📝 默认邮件模板版本已切换到: ${version}`);
  }

  /**
   * 获取可用的模板版本
   */
  getAvailableVersions(): EmailTemplateVersion[] {
    return this.templateManager.getAvailableVersions();
  }

  /**
   * 记录邮件发送尝试日志
   */
  private async logEmailAttempt(logId: string, data: {
    userId?: string;
    email: string;
    emailType: string;
    status: 'pending' | 'sent' | 'failed' | 'delivered' | 'bounced';
    provider: string;
    attemptCount: number;
    messageId?: string;
    errorMessage?: string;
    metadata?: any;
  }): Promise<void> {
    try {
      const now = new Date();
      const logData = {
        id: logId,
        userId: data.userId || null,
        email: data.email,
        emailType: data.emailType,
        status: data.status,
        provider: data.provider,
        messageId: data.messageId,
        errorMessage: data.errorMessage,
        attemptCount: data.attemptCount,
        sentAt: data.status === 'sent' ? now : null,
        deliveredAt: data.status === 'delivered' ? now : null,
        createdAt: now,
        updatedAt: now,
        metadata: data.metadata ? JSON.stringify(data.metadata) : null,
      };

      await db.insert(emailLog).values(logData);
    } catch (error) {
      console.error('记录邮件发送日志失败:', error);
    }
  }

  /**
   * 更新邮件状态
   */
  private async updateEmailStatus(
    logId: string,
    status: 'pending' | 'sent' | 'failed' | 'delivered' | 'bounced',
    errorMessage?: string,
    messageId?: string
  ): Promise<void> {
    try {
      const now = new Date();
      const updateData: any = {
        status,
        updatedAt: now,
      };

      if (status === 'sent') {
        updateData.sentAt = now;
        if (messageId) {
          updateData.messageId = messageId;
        }
      } else if (status === 'delivered') {
        updateData.deliveredAt = now;
      } else if (status === 'failed' && errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      await db
        .update(emailLog)
        .set(updateData)
        .where(eq(emailLog.id, logId));
    } catch (error) {
      console.error('更新邮件状态失败:', error);
    }
  }

  /**
   * 获取用户邮件发送历史
   */
  async getEmailHistory(userId: string, emailType?: string, limit: number = 10): Promise<any[]> {
    try {
      const whereConditions = [eq(emailLog.userId, userId)];
      
      if (emailType) {
        whereConditions.push(eq(emailLog.emailType, emailType));
      }

      return await db
        .select()
        .from(emailLog)
        .where(and(...whereConditions))
        .orderBy(sql`${emailLog.createdAt} DESC`)
        .limit(limit);
    } catch (error) {
      console.error('获取邮件历史失败:', error);
      return [];
    }
  }

  /**
   * 获取邮件发送统计信息
   */
  async getEmailStats(timeRangeHours: number = 24): Promise<{
    totalSent: number;
    totalFailed: number;
    successRate: number;
    byType: Record<string, { sent: number; failed: number }>;
  }> {
    try {
      const since = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);
      
      const stats = await db
        .select({
          emailType: emailLog.emailType,
          status: emailLog.status,
          count: sql`COUNT(*)`
        })
        .from(emailLog)
        .where(sql`${emailLog.createdAt} > ${since}`)
        .groupBy(emailLog.emailType, emailLog.status);

      const result = {
        totalSent: 0,
        totalFailed: 0,
        successRate: 0,
        byType: {} as Record<string, { sent: number; failed: number }>
      };

      for (const stat of stats) {
        const type = stat.emailType;
        const status = stat.status;
        const count = Number(stat.count);

        if (!result.byType[type]) {
          result.byType[type] = { sent: 0, failed: 0 };
        }

        if (status === 'sent' || status === 'delivered') {
          result.byType[type].sent += count;
          result.totalSent += count;
        } else if (status === 'failed') {
          result.byType[type].failed += count;
          result.totalFailed += count;
        }
      }

      const total = result.totalSent + result.totalFailed;
      result.successRate = total > 0 ? (result.totalSent / total) * 100 : 0;

      return result;
    } catch (error) {
      console.error('获取邮件统计信息失败:', error);
      return {
        totalSent: 0,
        totalFailed: 0,
        successRate: 0,
        byType: {}
      };
    }
  }

  /**
   * 清理过期的邮件日志（保留最近30天）
   */
  async cleanupOldEmailLogs(): Promise<number> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const result = await db
        .delete(emailLog)
        .where(sql`${emailLog.createdAt} < ${thirtyDaysAgo}`);

      console.log(`🧹 已清理 ${result.length || 0} 条过期邮件日志`);
      return result.length || 0;
    } catch (error) {
      console.error('清理过期邮件日志失败:', error);
      return 0;
    }
  }
}

/**
 * 创建邮件服务实例的工厂函数
 */
export function createEmailService(): EmailService {
  const apiKey = process.env.RESEND_API_KEY;
  console.log('----------------RESEND_API_KEY----------------', apiKey);
  if (!apiKey) {
    throw new Error('RESEND_API_KEY environment variable is required');
  }

  const config: EmailServiceConfig = {
    apiKey,
    fromEmail: '<EMAIL>',
    fromName: 'SpecificAI',
    defaultVersion: 'v3', // 升级为 v3 版本，提供更好的用户体验
    // 负责人邮箱
    replyTo: '<EMAIL>',
  };

  return new EmailService(config);
}

// 创建全局单例实例
export const emailService = createEmailService();