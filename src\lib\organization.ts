import { db } from "./drizzle.js";
import { organization, member, subscription } from "./auth-schema.js";
import { getPlanMemberLimit, PlanType } from "./plans.js";
import { eq, count } from "drizzle-orm";
import { randomUUID } from "crypto";

/**
 * 生成UUID v4格式的唯一ID
 */

/**
 * 为用户创建组织
 * @param userId 用户ID
 * @param companyName 公司名称
 * @param userName 用户名称
 * @returns 创建的组织信息
 */
export async function createOrganizationForUser(
  userId: string,
  companyName: string,
  _userName: string
) {
  try {
    return await db.transaction(async (tx) => {
      // 1. 创建组织
      const orgId = randomUUID();
      const [newOrg] = await tx
        .insert(organization)
        .values({
          id: orgId,
          name: companyName,
          slug: generateSlug(companyName),
          createdAt: new Date(),
        })
        .returning();

      // 2. 添加用户为组织的owner
      await tx.insert(member).values({
        id: randomUUID(),
        organizationId: orgId,
        userId: userId,
        role: "owner",
        createdAt: new Date(),
      });

      // 3. 创建默认订阅（基础版）
      await tx.insert(subscription).values({
        id: randomUUID(),
        organizationId: orgId,
        plan: "basic",
        memberLimit: 5, // 基础版默认5个成员
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      return newOrg;
    });
  } catch (error) {
    console.error("创建组织失败:", error);
    throw error;
  }
}

/**
 * 检查组织成员数量限制
 * @param organizationId 组织ID
 * @returns 是否可以添加新成员
 */
export async function checkMembershipLimit(organizationId: string): Promise<{
  canAddMember: boolean;
  currentCount: number;
  limit: number;
  plan: string;
  suggestedPlan?: string;
}> {
  try {
    const [memberCountResult, subscriptionResult] = await Promise.all([
      db
        .select({ count: count() })
        .from(member)
        .where(eq(member.organizationId, organizationId)),
      db
        .select()
        .from(subscription)
        .where(eq(subscription.organizationId, organizationId))
        .limit(1),
    ]);

    const currentCount = memberCountResult[0]?.count ?? 0;
    const subscriptionData = subscriptionResult[0];
    const plan = subscriptionData?.plan ?? "basic";
    const limit =
      subscriptionData?.memberLimit ?? getPlanMemberLimit(plan as PlanType);

    let suggestedPlan: string | undefined;
    if (currentCount >= limit) {
      // 如果已达到限制，建议升级计划
      const plans: PlanType[] = ["basic", "pro", "enterprise"];
      const currentPlanIndex = plans.indexOf(plan as PlanType);
      if (currentPlanIndex < plans.length - 1) {
        suggestedPlan = plans[currentPlanIndex + 1];
      }
    }

    return {
      canAddMember: currentCount < limit,
      currentCount,
      limit,
      plan,
      suggestedPlan,
    };
  } catch (error) {
    console.error("检查成员限制失败:", error);
    return {
      canAddMember: false,
      currentCount: 0,
      limit: 0,
      plan: "basic",
    };
  }
}

/**
 * 生成组织slug
 * @param name 组织名称
 * @returns slug
 */
function generateSlug(name: string): string {
  return (
    name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim()
      .substring(0, 50) + // 限制长度
    "-" +
    randomUUID().substring(0, 8)
  ); // 添加随机后缀确保唯一性
}

/**
 * 获取用户的组织信息
 * @param userId 用户ID
 * @returns 用户的组织列表
 */
export async function getUserOrganizations(userId: string) {
  try {
    const result = await db
      .select({
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
        logo: organization.logo,
        createdAt: organization.createdAt,
        metadata: organization.metadata,
        role: member.role,
        subscription: {
          id: subscription.id,
          plan: subscription.plan,
          memberLimit: subscription.memberLimit,
          createdAt: subscription.createdAt,
          updatedAt: subscription.updatedAt,
        },
      })
      .from(member)
      .innerJoin(organization, eq(member.organizationId, organization.id))
      .leftJoin(subscription, eq(organization.id, subscription.organizationId))
      .where(eq(member.userId, userId));

    return result.map((row) => ({
      id: row.id,
      name: row.name,
      slug: row.slug,
      logo: row.logo,
      createdAt: row.createdAt,
      metadata: row.metadata,
      role: row.role,
      subscription: row.subscription,
    }));
  } catch (error) {
    console.error("获取用户组织失败:", error);
    return [];
  }
}
