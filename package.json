{"name": "better-auth-service", "version": "1.0.0", "description": "基于Fastify + Better Auth的最简邮箱登录注册认证中间服务", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "tsx src/server.ts", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:setup-schema": "echo 'Schema configuration ready'", "db:full-migrate": "npm run db:setup-schema && npm run db:generate && npm run db:migrate", "db:better-auth-migrate": "npx @better-auth/cli migrate", "cleanup:data": "tsx scripts/cleanup-data.ts", "cleanup:dry-run": "tsx scripts/cleanup-data.ts --dry-run --verbose", "cleanup:health": "tsx scripts/cleanup-data.ts --health-check --verbose", "cleanup:expired": "tsx scripts/cleanup-data.ts --only-expired --force", "metrics:export": "tsx scripts/metrics-exporter.ts", "deploy:cleanup": "bash scripts/deploy-cleanup-system.sh deploy", "deploy:cleanup:dry-run": "bash scripts/deploy-cleanup-system.sh deploy --dry-run", "deploy:cleanup:status": "bash scripts/deploy-cleanup-system.sh status", "deploy:cleanup:test": "bash scripts/deploy-cleanup-system.sh test", "deploy:cleanup:uninstall": "bash scripts/deploy-cleanup-system.sh uninstall"}, "keywords": ["fastify", "better-auth", "authentication", "typescript", "api", "postgres"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "dotenv": "^17.0.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "fastify": "^4.26.2", "pg": "^8.11.3", "postgres": "^3.4.7", "resend": "^4.6.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.11.24", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.0", "jest": "^29.5.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "tsx": "^4.7.1", "typescript": "^5.3.3"}}