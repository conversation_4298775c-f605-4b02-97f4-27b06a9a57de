/**
 * 统一响应格式化中间件
 * 将所有API响应格式化为标准结构: {code, data, success, message}
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";

export interface StandardResponse {
  code: number;
  data: any;
  success: boolean;
  message: string;
}

/**
 * 格式化成功响应
 */
export function formatSuccessResponse(
  data: any,
  message: string = "success",
  code: number = 200,
): StandardResponse {
  return {
    code,
    data,
    success: true,
    message,
  };
}

/**
 * 格式化错误响应
 */
export function formatErrorResponse(
  message: string,
  code: number = 400,
  data: any = null,
): StandardResponse {
  return {
    code,
    data,
    success: false,
    message,
  };
}

/**
 * 判断是否为Better Auth的API路径
 * Better Auth的原生API路径，这些路径不需要标准化响应格式
 */
function isBetterAuthPath(url: string): boolean {
  // Better Auth 原生API路径模式
  const betterAuthPaths = [
    "/api/auth/sign-in",
    "/api/auth/sign-up", 
    "/api/auth/sign-out",
    "/api/auth/session",
    "/api/auth/get-session",
    "/api/auth/verify-email",
    "/api/auth/reset-password",
    "/api/auth/callback",
    "/api/auth/jwks",
  ];
  
  // 检查是否是Better Auth原生路径
  const isBetterAuth = betterAuthPaths.some(path => url.startsWith(path));
  console.log(`[ResponseFormatter] Checking if ${url} is Better Auth path: ${isBetterAuth}`);
  return isBetterAuth;
}

/**
 * 判断响应是否已经是标准格式
 */
function isStandardFormat(payload: any): boolean {
  return (
    payload &&
    typeof payload === "object" &&
    "code" in payload &&
    "data" in payload &&
    "success" in payload &&
    "message" in payload
  );
}

/**
 * 响应格式化插件
 */
export async function responseFormatterPlugin(fastify: FastifyInstance) {
  console.log('[ResponseFormatter] Plugin registered successfully!');
  
  // 注册onSend钩子来格式化所有响应
  fastify.addHook(
    "onSend",
    async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      const url = request.url;
      
      // 调试日志
      console.log(`[ResponseFormatter] Processing: ${request.method} ${url}`);

      try {
        // 检查是否为Better Auth原生路径，如果是则不处理
        if (isBetterAuthPath(url)) {
          console.log(`[ResponseFormatter] Skipping Better Auth path: ${url}`);
          return payload;
        }
        
        // 如果响应已经是标准格式，直接返回
        if (isStandardFormat(payload)) {
          console.log(`[ResponseFormatter] Already standard format: ${url}`);
          return payload;
        }

        // 解析payload
        let data;
        try {
          data = typeof payload === "string" ? JSON.parse(payload) : payload;
        } catch {
          data = payload;
        }
        
        console.log(`[ResponseFormatter] Formatting response for: ${url}, status: ${reply.statusCode}`);

        // 根据状态码判断是成功还是错误响应
        const statusCode = reply.statusCode;
        const isSuccess = statusCode >= 200 && statusCode < 300;

        let formattedResponse: StandardResponse;

        if (isSuccess) {
          // 成功响应 - 自定义接口使用标准格式
          formattedResponse = formatSuccessResponse(
            data,
            "success",
            statusCode,
          );
        } else {
          // 错误响应
          let errorMessage = "Internal Server Error";
          let errorData = null;

          if (data && typeof data === "object") {
            if (data.message) {
              errorMessage = data.message;
            } else if (data.error) {
              errorMessage = data.error;
            }
            errorData = data;
          } else if (typeof data === "string") {
            errorMessage = data;
          }

          formattedResponse = formatErrorResponse(
            errorMessage,
            statusCode,
            errorData,
          );
        }
        
        console.log(`[ResponseFormatter] Formatted response:`, formattedResponse);

        return JSON.stringify(formattedResponse);
      } catch (error) {
        // 如果格式化失败，返回默认错误格式
        console.error(`[ResponseFormatter] Error formatting response for ${url}:`, error);
        const errorResponse = formatErrorResponse(
          "Response formatting error",
          500,
        );
        return JSON.stringify(errorResponse);
      }
    },
  );

  // 注册错误处理器
  fastify.setErrorHandler(async (error, request, reply) => {
    const statusCode = error.statusCode || 500;
    const errorResponse = formatErrorResponse(
      error.message || "Internal Server Error",
      statusCode,
      process.env.NODE_ENV === "development" ? { stack: error.stack } : null,
    );

    reply.status(statusCode).send(errorResponse);
  });
}
