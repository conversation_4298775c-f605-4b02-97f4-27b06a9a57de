#!/usr/bin/env node

/**
 * 环境变量检查脚本
 * 验证 .env 文件中的 RESEND_API_KEY 是否正确配置
 */

// 尝试加载 .env 文件
try {
  const dotenv = require('dotenv');
  dotenv.config();
  console.log('✅ 已加载 .env 文件');
} catch (error) {
  console.log('⚠️  无法加载 .env 文件，将直接使用环境变量');
  console.log('   错误:', error.message);
}

console.log('🔍 环境变量检查');
console.log('='.repeat(40));

// 检查 RESEND_API_KEY
const apiKey = process.env.RESEND_API_KEY;

if (!apiKey) {
  console.log('❌ RESEND_API_KEY 未设置');
  console.log('');
  console.log('💡 请通过以下方式之一设置 API Key:');
  console.log('');
  console.log('方法1: 在 .env 文件中添加');
  console.log('echo "RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxx" >> .env');
  console.log('');
  console.log('方法2: 临时环境变量');
  console.log('export RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxx');
  console.log('');
  console.log('方法3: 运行时设置');
  console.log('RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxx node scripts/test-email-service.js');
  process.exit(1);
}

// 检查 API Key 格式
if (!apiKey.startsWith('re_')) {
  console.log('⚠️  RESEND_API_KEY 格式可能不正确');
  console.log('   Resend API Key 通常以 "re_" 开头');
  console.log(`   当前值: ${apiKey.substring(0, 10)}...`);
} else {
  console.log('✅ RESEND_API_KEY 已设置');
  console.log(`   格式: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
}

// 检查 API Key 长度
if (apiKey.length < 20) {
  console.log('⚠️  API Key 长度似乎过短，请检查是否完整');
} else {
  console.log(`✅ API Key 长度: ${apiKey.length} 字符`);
}

// 检查其他相关环境变量
console.log('');
console.log('📋 其他环境变量检查:');

const otherEnvs = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET',
  'BETTER_AUTH_URL',
  'NODE_ENV'
];

otherEnvs.forEach(envName => {
  const value = process.env[envName];
  if (value) {
    console.log(`✅ ${envName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`⚠️  ${envName}: 未设置`);
  }
});

console.log('');
console.log('='.repeat(40));
console.log('🎯 环境检查完成');

if (apiKey && apiKey.startsWith('re_') && apiKey.length >= 20) {
  console.log('✅ 可以运行邮件服务测试');
  console.log('');
  console.log('下一步: node scripts/test-email-service.js');
} else {
  console.log('❌ 请先正确设置 RESEND_API_KEY');
}