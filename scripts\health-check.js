#!/usr/bin/env node

/**
 * 健康检查脚本
 * 检查数据库连接和关键表的存在
 */

const pkg = require('pg');
const { Client } = pkg;

const DB_CONFIG = {
  connectionString: process.env.DATABASE_URL,
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'specific_ai_auth',
  user: process.env.DB_USER || 'specific_ai_auth',
  password: process.env.DB_PASSWORD,
};

async function healthCheck() {
  let client;
  
  try {
    // 连接数据库
    client = new Client(DB_CONFIG);
    await client.connect();
    
    // 检查数据库连接
    await client.query('SELECT 1');
    
    // 检查关键表是否存在
    const result = await client.query(`
      SELECT COUNT(*) as table_count
      FROM information_schema.tables 
      WHERE table_schema = 'auth' 
      AND table_name IN ('user', 'session', 'organization', 'email_log')
    `);
    
    const tableCount = parseInt(result.rows[0].table_count);
    
    if (tableCount < 4) {
      throw new Error(`Only ${tableCount}/4 critical tables found`);
    }
    
    console.log('✅ Database health check passed');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Database health check failed:', error.message);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
    }
  }
}

healthCheck();