version: "3.8"

services:
  better-auth:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: better-auth-service
    restart: unless-stopped
    ports:
      - "10086:10086"
    environment:
      - NODE_ENV=production
      - PORT=10086
      - HOST=0.0.0.0
      # 数据库配置 - 请在 .env 文件中设置实际值
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${BETTER_AUTH_URL:-http://localhost:10086}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
    volumes:
      # 持久化日志目录（可选）
      - ./logs:/app/logs
    networks:
      - better-auth-network
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:10086/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: "0.50"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp

networks:
  better-auth-network:
    driver: bridge

# 开发环境配置（可选）
---
version: "3.8"

services:
  better-auth-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: better-auth-dev
    restart: unless-stopped
    ports:
      - "10086:10086"
    environment:
      - NODE_ENV=development
      - PORT=10086
      - HOST=0.0.0.0
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - better-auth-dev-network
    command: pnpm run dev

networks:
  better-auth-dev-network:
    driver: bridge
