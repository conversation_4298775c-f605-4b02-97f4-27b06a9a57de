import type { User, Session } from "better-auth/types";

export interface JWTTokenResponse {
  success: boolean;
  token?: string;
  expiresIn?: number;
  error?: string;
}

export interface JWTVerifyResponse {
  success: boolean;
  payload?: any;
  error?: string;
}

export interface JWTTokenPayload {
  userId: string;
  email: string;
  role?: string;
  organizationId?: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string | string[];
}

/**
 * JWT Token 服务类
 * 基于Better Auth JWT插件提供统一的Token管理功能
 */
export class JWTTokenService {
  private static instance: JWTTokenService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): JWTTokenService {
    if (!JWTTokenService.instance) {
      JWTTokenService.instance = new JWTTokenService();
    }
    return JWTTokenService.instance;
  }

  /**
   * 创建JWT Token
   * @param user 用户信息
   * @param session 会话信息（可选）
   * @returns JWT Token响应
   */
  async createToken(
    user: User,
    session?: Session & { organization?: any }
  ): Promise<JWTTokenResponse> {
    try {
      // 构建JWT payload
      const payload: JWTTokenPayload = {
        userId: user.id,
        email: user.email,
        role: (user as any).role || "user",
        organizationId: session?.organization?.id,
      };

      // 注意：实际的JWT签名将通过Better Auth的JWT插件API端点完成
      // 这里我们准备payload数据
      return {
        success: true,
        token: JSON.stringify(payload), // 临时返回payload，实际token由API端点生成
        expiresIn: 3600, // 1小时
      };
    } catch (error) {
      console.error("JWT Token创建失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "JWT Token创建失败",
      };
    }
  }

  /**
   * 验证JWT Token
   * @param token JWT Token字符串
   * @returns 验证结果
   */
  async verifyToken(token: string): Promise<JWTVerifyResponse> {
    try {
      // 注意：实际的JWT验证将通过Better Auth的JWT插件API端点完成
      // 这里我们提供基础验证逻辑
      if (!token || token.trim() === "") {
        return {
          success: false,
          error: "Token不能为空",
        };
      }

      // JWT token基本格式验证
      const parts = token.split(".");
      if (parts.length !== 3) {
        return {
          success: false,
          error: "无效的JWT Token格式",
        };
      }

      return {
        success: true,
        payload: { message: "Token格式验证通过，请使用API端点进行完整验证" },
      };
    } catch (error) {
      console.error("JWT Token验证失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "JWT Token验证失败",
      };
    }
  }

  /**
   * 刷新JWT Token
   * @param refreshToken 刷新Token
   * @returns 新的Token响应
   */
  async refreshToken(refreshToken: string): Promise<JWTTokenResponse> {
    try {
      // 验证refresh token
      const verifyResult = await this.verifyToken(refreshToken);
      if (!verifyResult.success) {
        return {
          success: false,
          error: "无效的刷新Token",
        };
      }

      // 注意：实际的token刷新将通过Better Auth的JWT插件API端点完成
      return {
        success: true,
        token: "new-refreshed-token", // 实际token由API端点生成
        expiresIn: 3600,
      };
    } catch (error) {
      console.error("JWT Token刷新失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "JWT Token刷新失败",
      };
    }
  }

  /**
   * 撤销JWT Token
   * @param token JWT Token字符串
   * @returns 撤销结果
   */
  async revokeToken(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 注意：Better Auth JWT插件可能不直接支持token撤销
      // 这里我们提供基础的撤销逻辑框架
      const verifyResult = await this.verifyToken(token);
      if (!verifyResult.success) {
        return {
          success: false,
          error: "无效的Token",
        };
      }

      // 实际撤销逻辑需要维护一个token黑名单
      // 或通过数据库记录撤销的token
      console.log("Token撤销请求:", { token: token.substring(0, 20) + "..." });

      return {
        success: true,
      };
    } catch (error) {
      console.error("JWT Token撤销失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "JWT Token撤销失败",
      };
    }
  }

  /**
   * 解析JWT Token Payload（不验证签名）
   * @param token JWT Token字符串
   * @returns 解析的payload
   */
  parseTokenPayload(token: string): JWTTokenPayload | null {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) {
        return null;
      }

      // 解码payload部分
      const payloadBase64 = parts[1];
      const payloadJson = Buffer.from(payloadBase64 || '', "base64").toString("utf-8");
      const payload = JSON.parse(payloadJson);

      return payload as JWTTokenPayload;
    } catch (error) {
      console.error("Token payload解析失败:", error);
      return null;
    }
  }
}

// 导出单例实例
export const jwtTokenService = JWTTokenService.getInstance();