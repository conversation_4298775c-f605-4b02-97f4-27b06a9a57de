apiVersion: v1
kind: ConfigMap
metadata:
  name: specific-ai-auth-config
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
    environment: prod
data:
  # 服务器配置
  PORT: "10086"
  HOST: "0.0.0.0"
  
  # 环境标识
  NODE_ENV: "production"
  
  # 认证配置（非敏感信息）
  BETTER_AUTH_URL: "http://production-domain.com:10086"
  CORS_ORIGIN: "https://production-frontend.com"
  
  # 生产环境业务服务API配置
  BUSINESS_BASE_API: "https://api.production.com"
  
  # Google OAuth 配置（非敏感部分）
  GOOGLE_CLIENT_ID: "your-prod-google-client-id.apps.googleusercontent.com"
  
  # 生产环境特有配置
  DEBUG_MODE: "false"
  LOG_LEVEL: "info"