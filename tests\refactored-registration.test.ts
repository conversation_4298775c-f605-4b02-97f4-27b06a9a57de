/**
 * 重构后的临时注册功能测试
 * 测试 sign-up-invite 端点和 PendingRegistrationService
 */

import { describe, it, expect, beforeAll, afterAll, jest, beforeEach } from '@jest/globals';

// Mock 模块定义
const mockDb = {
  select: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  transaction: jest.fn(),
  execute: jest.fn(),
};

const mockAuth = {
  api: {
    signUpEmail: jest.fn(),
    sendVerificationEmail: jest.fn(),
    verifyEmail: jest.fn(),
  }
};

const mockInviteCodeService = {
  verifyCode: jest.fn(),
  updateStatus: jest.fn(),
};

const mockEmailService = {
  sendVerificationEmail: jest.fn(),
};

const mockBcrypt = {
  default: {
    hash: jest.fn(),
  }
};

// Mock 所有依赖
jest.unstable_mockModule('../src/lib/drizzle.js', () => ({
  db: mockDb
}));

jest.unstable_mockModule('../src/auth.js', () => ({
  auth: mockAuth
}));

jest.unstable_mockModule('../src/services/invite-code.js', () => ({
  inviteCodeService: mockInviteCodeService
}));

jest.unstable_mockModule('../src/services/email-service.js', () => ({
  emailService: mockEmailService
}));

jest.unstable_mockModule('bcryptjs', () => mockBcrypt);

describe('重构后的临时注册功能测试', () => {
  let pendingRegistrationService: any;

  beforeAll(async () => {
    // 动态导入被测试的模块
    const module = await import('../src/services/pending-registration.js');
    pendingRegistrationService = module.pendingRegistrationService;
  });

  beforeEach(() => {
    // 重置所有 mock
    jest.clearAllMocks();
    
    // 设置默认的 mock 返回值
    mockBcrypt.default.hash.mockResolvedValue('hashed-password');
    mockInviteCodeService.verifyCode.mockResolvedValue({ valid: true });
    mockDb.select.mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([]) // 默认返回空数组，表示用户不存在
        })
      })
    });
    mockDb.insert.mockReturnValue({
      values: jest.fn().mockResolvedValue({ rowCount: 1 })
    });
    mockAuth.api.sendVerificationEmail.mockResolvedValue({
      success: true,
      token: 'mock-verification-token'
    });
  });

  describe('PendingRegistrationService.createPendingRegistration', () => {
    it('应该成功创建临时注册记录', async () => {
      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE',
        language: 'chinese',
        ipAddress: '127.0.0.1',
        userAgent: 'Jest Test Agent'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(true);
      expect(result.pendingId).toBeDefined();
      expect(result.verificationSent).toBe(true);
      expect(result.nextSteps).toBeDefined();
      expect(result.nextSteps.requireEmailVerification).toBe(true);
      
      // 验证邀请码验证被调用
      expect(mockInviteCodeService.verifyCode).toHaveBeenCalledWith('VALID_CODE');
      
      // 验证数据库操作被调用
      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalled();
      
      // 验证发送验证邮件被调用
      expect(mockAuth.api.sendVerificationEmail).toHaveBeenCalled();
    });

    it('应该在邀请码无效时返回错误', async () => {
      mockInviteCodeService.verifyCode.mockResolvedValue({
        valid: false,
        error: '邀请码已过期'
      });

      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'INVALID_CODE',
        language: 'chinese'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('邀请码无效');
      expect(mockDb.insert).not.toHaveBeenCalled();
    });

    it('应该在用户已存在时返回错误', async () => {
      // 模拟用户已存在
      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([{ id: 'existing-user' }])
          })
        })
      });

      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE',
        language: 'chinese'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('该邮箱已注册');
    });

    it('应该处理邮件发送失败的情况', async () => {
      mockAuth.api.sendVerificationEmail.mockRejectedValue(new Error('邮件服务不可用'));

      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE',
        language: 'chinese'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(true); // 仍然成功创建记录
      expect(result.verificationSent).toBe(false); // 但邮件发送失败
      expect(result.nextSteps.verificationEmailSent).toBe(false);
      expect(result.nextSteps.instructions).toContain('邮件发送失败');
    });
  });

  describe('PendingRegistrationService.verifyEmailAndCompleteRegistration', () => {
    it('应该成功验证邮箱并完成注册', async () => {
      const mockToken = 'valid-verification-token';
      const mockPendingRecord = {
        id: 'pending-id',
        email: '<EMAIL>',
        password: 'hashed-password',
        name: 'Test User',
        companyName: 'Test Company',
        language: 'chinese',
        pendingOrganizationId: 'org-id',
        pendingOrganizationSlug: 'test-company-xyz',
        inviteCode: 'VALID_CODE',
        status: 'pending',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 未过期
      };

      // Mock 查找临时注册记录
      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockPendingRecord])
          })
        })
      });

      // Mock Better Auth 验证
      mockAuth.api.verifyEmail.mockResolvedValue({
        user: { id: 'auth-user-id' }
      });

      // Mock Better Auth 用户创建
      mockAuth.api.signUpEmail.mockResolvedValue({
        user: { id: 'new-user-id' }
      });

      // Mock 数据库事务
      mockDb.transaction.mockImplementation(async (callback) => {
        return callback({
          update: jest.fn().mockReturnValue({
            set: jest.fn().mockReturnValue({
              where: jest.fn().mockResolvedValue({ rowCount: 1 })
            })
          }),
          insert: jest.fn().mockReturnValue({
            values: jest.fn().mockResolvedValue({ rowCount: 1 })
          })
        });
      });

      const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration(mockToken);

      expect(result.success).toBe(true);
      expect(result.userId).toBe('new-user-id');
      expect(result.organizationId).toBe('org-id');
      
      // 验证 Better Auth 验证被调用
      expect(mockAuth.api.verifyEmail).toHaveBeenCalledWith({
        body: { token: mockToken }
      });
      
      // 验证用户创建被调用
      expect(mockAuth.api.signUpEmail).toHaveBeenCalled();
      
      // 验证事务被调用
      expect(mockDb.transaction).toHaveBeenCalled();
    });

    it('应该在验证令牌无效时返回错误', async () => {
      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]) // 没找到记录
          })
        })
      });

      const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration('invalid-token');

      expect(result.success).toBe(false);
      expect(result.error).toBe('验证令牌无效或已过期');
    });

    it('应该在验证链接过期时返回错误', async () => {
      const expiredRecord = {
        id: 'pending-id',
        expiresAt: new Date(Date.now() - 1000), // 已过期
        status: 'pending'
      };

      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([expiredRecord])
          })
        })
      });

      mockDb.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue({ rowCount: 1 })
        })
      });

      const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration('expired-token');

      expect(result.success).toBe(false);
      expect(result.error).toContain('验证链接已过期');
      
      // 验证记录状态被更新为过期
      expect(mockDb.update).toHaveBeenCalled();
    });
  });

  describe('PendingRegistrationService.resendVerificationEmail', () => {
    it('应该成功重发验证邮件', async () => {
      const mockPendingRecord = {
        id: 'pending-id',
        email: '<EMAIL>',
        name: 'Test User',
        inviteCode: 'VALID_CODE',
        verificationToken: 'existing-token',
        verificationAttempts: 1,
        lastVerificationSentAt: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
        status: 'pending'
      };

      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockPendingRecord])
          })
        })
      });

      mockEmailService.sendVerificationEmail.mockResolvedValue({
        success: true
      });

      mockDb.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue({ rowCount: 1 })
        })
      });

      const result = await pendingRegistrationService.resendVerificationEmail('<EMAIL>');

      expect(result.success).toBe(true);
      expect(result.verificationSent).toBe(true);
      expect(mockEmailService.sendVerificationEmail).toHaveBeenCalled();
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('应该在频率限制内阻止重发', async () => {
      const recentRecord = {
        id: 'pending-id',
        email: '<EMAIL>',
        lastVerificationSentAt: new Date(Date.now() - 1 * 60 * 1000), // 1分钟前
        status: 'pending'
      };

      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([recentRecord])
          })
        })
      });

      const result = await pendingRegistrationService.resendVerificationEmail('<EMAIL>');

      expect(result.success).toBe(false);
      expect(result.error).toContain('请稍后再重发');
      expect(mockEmailService.sendVerificationEmail).not.toHaveBeenCalled();
    });

    it('应该在没有待验证记录时返回错误', async () => {
      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]) // 没找到记录
          })
        })
      });

      const result = await pendingRegistrationService.resendVerificationEmail('<EMAIL>');

      expect(result.success).toBe(false);
      expect(result.error).toBe('未找到待验证的注册记录');
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该处理数据库连接错误', async () => {
      mockDb.select.mockRejectedValue(new Error('数据库连接失败'));

      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('数据库连接失败');
    });

    it('应该处理事务回滚', async () => {
      const mockPendingRecord = {
        id: 'pending-id',
        email: '<EMAIL>',
        password: 'hashed-password',
        name: 'Test User',
        companyName: 'Test Company',
        language: 'chinese',
        pendingOrganizationId: 'org-id',
        pendingOrganizationSlug: 'test-company-xyz',
        inviteCode: 'VALID_CODE',
        status: 'pending',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      mockDb.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockPendingRecord])
          })
        })
      });

      mockAuth.api.verifyEmail.mockResolvedValue({
        user: { id: 'auth-user-id' }
      });

      // 模拟事务失败
      mockDb.transaction.mockRejectedValue(new Error('事务提交失败'));

      const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration('valid-token');

      expect(result.success).toBe(false);
      expect(result.error).toContain('事务提交失败');
    });
  });

  describe('数据一致性测试', () => {
    it('应该确保组织信息的一致性', async () => {
      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: 'Test User',
        companyName: 'Test Company Inc.',
        inviteCode: 'VALID_CODE',
        language: 'chinese'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(true);
      
      // 验证数据库插入被调用，并检查传入的数据
      expect(mockDb.insert).toHaveBeenCalled();
      const insertCall = mockDb.insert.mock.calls[0];
      
      // 检查插入的数据结构
      expect(insertCall).toBeDefined();
    });

    it('应该正确处理中文公司名称的slug生成', async () => {
      const testData = {
        email: '<EMAIL>',
        password: 'hashedPassword123',
        name: '张三',
        companyName: '北京科技有限公司',
        inviteCode: 'VALID_CODE',
        language: 'chinese'
      };

      const result = await pendingRegistrationService.createPendingRegistration(testData);

      expect(result.success).toBe(true);
      // slug 应该被正确生成（移除中文字符，添加随机后缀）
    });
  });
});