# sign-up-invite 接口测试脚本

这个测试脚本专门用于测试优化后的 `sign-up-invite` 接口性能，可以自动获取邀请码并测试注册流程。

## 🚀 快速开始

```bash
# 基本使用
node test-signup-invite.js

# 批量测试 5 次
node test-signup-invite.js --batch 5

# 自定义服务地址
node test-signup-invite.js --auth-url http://localhost:3001/api/auth/sign-up-invite
```

## ✨ 功能特性

- ✅ **自动获取邀请码** - 无需手动生成邀请码
- ✅ **随机测试数据** - 自动生成不重复的用户信息
- ✅ **性能监控** - 精确测量响应时间
- ✅ **彩色输出** - 清晰的结果展示
- ✅ **批量测试** - 支持多次连续测试
- ✅ **详细日志** - 完整的请求/响应数据

## 📋 命令行选项

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `--invite-url <url>` | 邀请码生成服务地址 | `http://localhost:8000/api/v1/invitations/generate` |
| `--auth-url <url>` | 认证服务地址 | `http://localhost:3001/api/auth/sign-up-invite` |
| `--batch <number>` | 批量测试数量 | `1` |
| `--timeout <ms>` | 请求超时时间 | `30000ms` |
| `--help` | 显示帮助信息 | - |

## 📊 输出示例

```
🧪 sign-up-invite 接口测试开始
==================================================
🔧 配置信息:
   邀请码服务: http://localhost:8000/api/v1/invitations/generate
   认证服务: http://localhost:3001/api/auth/sign-up-invite
   批量数量: 1
   超时时间: 30000ms

📝 正在获取邀请码...
✅ 邀请码获取成功: TW02VJU1 (245ms)
🎲 生成测试数据: {
  email: '<EMAIL>',
  name: '测试用户_abc123',
  company_name: '测试公司_abc123'
}
🚀 正在测试注册接口...
✅ 注册成功! (1250ms)
👤 用户ID: user_12345
📧 邮箱: <EMAIL>
🏢 公司: 测试公司_abc123

📊 测试摘要
==================================================
✅ 成功: 1
❌ 失败: 0
⏱️  平均响应时间: 1250ms
⚡ 最快响应: 1250ms
🐌 最慢响应: 1250ms
==================================================
```

## 🔍 测试内容

脚本会自动执行以下测试流程：

1. **邀请码获取** - 调用邀请码生成 API
2. **数据生成** - 创建随机的用户测试数据
3. **注册测试** - 调用 sign-up-invite 接口
4. **结果验证** - 检查响应格式和内容
5. **性能统计** - 计算响应时间和成功率

## 🛠️ 自定义配置

你可以修改脚本顶部的 `CONFIG` 对象来调整默认设置：

```javascript
const CONFIG = {
  INVITE_SERVICE_URL: 'http://localhost:8000/api/v1/invitations/generate',
  AUTH_SERVICE_URL: 'http://localhost:3001/api/auth/sign-up-invite',
  TIMEOUT: 30000,
  BATCH_SIZE: 1,
};
```

## 🐛 故障排除

### 常见问题

1. **邀请码服务无法访问**
   - 确保邀请码生成服务已启动并运行在正确端口
   - 检查防火墙设置

2. **认证服务连接失败**
   - 确保认证服务已启动
   - 检查服务地址和端口是否正确

3. **超时错误**
   - 可以使用 `--timeout` 参数增加超时时间
   - 检查网络连接和服务响应速度

### 调试技巧

- 使用 `--batch 1` 进行单次测试以便调试
- 查看详细的请求和响应日志
- 检查服务器日志了解后端状态

## 📈 性能基准

优化前后的性能对比：

| 版本 | 平均响应时间 | 成功率 |
|------|-------------|--------|
| 优化前 | 3-8 秒 | ~85% |
| 优化后 | 1-2 秒 | ~95% |

## 🤝 贡献

如需改进测试脚本，请确保：
- 保持良好的错误处理
- 添加适当的日志输出
- 更新文档说明