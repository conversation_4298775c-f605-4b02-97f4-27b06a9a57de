/**
 * 环境变量验证和配置
 */

export interface EnvConfig {
  // 数据库配置
  DATABASE_URL: string;

  // Better Auth配置
  BETTER_AUTH_SECRET: string;
  BETTER_AUTH_URL: string;

  // 服务器配置
  PORT: number;
  NODE_ENV: string;

  // CORS配置
  CORS_ORIGIN: string;

  // 前端应用配置
  FRONTEND_URL: string;

  // 业务服务API配置
  BUSINESS_BASE_API: string;
}

/**
 * 验证并获取环境变量配置
 */
export function getEnvConfig(): EnvConfig {
  const requiredEnvVars = [
    "DATABASE_URL",
    "BETTER_AUTH_SECRET",
    "BETTER_AUTH_URL",
    "BUSINESS_BASE_API",
  ];

  // 检查必需的环境变量
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Required environment variable ${envVar} is not set`);
    }
  }

  return {
    DATABASE_URL: process.env.DATABASE_URL!,
    BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET!,
    BETTER_AUTH_URL: process.env.BETTER_AUTH_URL!,
    PORT: parseInt(process.env.PORT || "10086", 10),
    NODE_ENV: process.env.NODE_ENV || "development",
    CORS_ORIGIN: process.env.CORS_ORIGIN || "*",
    FRONTEND_URL: process.env.FRONTEND_URL || "http://localhost:3000",
    BUSINESS_BASE_API: process.env.BUSINESS_BASE_API!,
  };
}

/**
 * 验证环境配置
 */
export function validateEnvConfig(): void {
  try {
    const config = getEnvConfig();

    // 验证端口号
    if (isNaN(config.PORT) || config.PORT < 1 || config.PORT > 65535) {
      throw new Error("PORT must be a valid port number (1-65535)");
    }

    // 验证DATABASE_URL格式
    if (!config.DATABASE_URL.startsWith("postgresql://")) {
      throw new Error(
        "DATABASE_URL must be a valid PostgreSQL connection string",
      );
    }

    // 验证API URL格式
    try {
      new URL(config.BUSINESS_BASE_API);
    } catch {
      throw new Error("BUSINESS_BASE_API must be a valid URL");
    }

    console.log("✅ Environment configuration validated successfully");
  } catch (error) {
    console.error("❌ Environment configuration validation failed:", error);
    throw error;
  }
}
