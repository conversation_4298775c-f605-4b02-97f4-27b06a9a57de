/**
 * 邮件服务类型定义
 */

export type EmailTemplateVersion = 'v1' | 'v2' | 'v3';

export interface EmailTemplateData {
  userName?: string;
  userEmail: string;
  verificationUrl?: string;
  resetPasswordUrl?: string;
  inviteCode?: string;
  inviteUrl?: string; // 邀请链接
  inviterName?: string; // 邀请人姓名
  companyName?: string;
  expiresIn?: number; // 过期时间（分钟）
  supportUrl?: string; // 支持链接
  unsubscribeUrl?: string; // 取消订阅链接
  locale?: string; // 语言设置
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
  preheader?: string; // 邮件预览文本
}

export interface EmailSendOptions {
  to: string[];
  from?: string;
  subject: string;
  html: string;
  text: string;
  replyTo?: string;
  headers?: Record<string, string>;
}

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
  retryCount?: number;
  deliveryStatus?: 'sent' | 'delivered' | 'bounced' | 'failed';
  // 新增字段用于增强功能
  logId?: string; // 邮件日志ID
  rateLimited?: boolean; // 是否受频率限制
  retryAfter?: Date; // 建议重试时间
  cacheHit?: boolean; // 是否来自缓存
  stats?: {
    attemptCount: number;
    lastAttemptAt: Date;
    nextRetryAt?: Date;
  };
}

export type EmailType = 'verification' | 'password-reset' | 'welcome' | 'notification' | 'invite';

export interface EmailServiceConfig {
  apiKey: string;
  fromEmail: string;
  fromName: string;
  defaultVersion: EmailTemplateVersion;
  replyTo: string;
  maxRetries?: number;
  retryDelay?: number; // 重试延迟（毫秒）
  rateLimitPerHour?: number; // 每小时发送限制
  enableTracking?: boolean; // 启用邮件跟踪
}

export interface TemplateRenderOptions {
  type: EmailType;
  version: EmailTemplateVersion;
  data: EmailTemplateData;
}

// 邮件发送状态跟踪
export interface EmailSendRecord {
  id: string;
  email: string;
  type: EmailType;
  sentAt: Date;
  status: 'pending' | 'sent' | 'delivered' | 'bounced' | 'failed';
  retryCount: number;
  messageId?: string;
  error?: string;
}

// 频率限制相关
export interface RateLimitInfo {
  email: string;
  count: number;
  resetTime: Date;
}

// 邮件统计
export interface EmailStats {
  sent: number;
  delivered: number;
  bounced: number;
  failed: number;
  retries: number;
}