# 邮箱验证 API 文档

## 概述

邮箱验证功能确保用户能够通过验证邮箱地址来激活账户。本文档详细说明了所有与邮箱验证相关的API接口，包括验证状态查询、验证邮件重发、验证回调处理等功能。

## API 接口列表

### 1. 重新发送验证邮件

**端点**: `POST /api/auth/resend-verification`

**描述**: 重新发送邮箱验证邮件给当前登录用户

**认证**: 需要用户登录（Bearer Token 或 Cookie）

**频率限制**: 每用户每2分钟最多1次请求

#### 请求

```http
POST /api/auth/resend-verification HTTP/1.1
Host: localhost:10086
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**: 无

#### 响应

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "验证邮件已重新发送，请检查您的邮箱",
  "data": {
    "email": "<EMAIL>",
    "resendCount": 1
  }
}
```

**错误响应**:

- **401 Unauthorized** - 用户未登录
```json
{
  "success": false,
  "error": "UNAUTHORIZED",
  "message": "请先登录"
}
```

- **400 Bad Request** - 邮箱已验证
```json
{
  "success": false,
  "error": "ALREADY_VERIFIED",
  "message": "邮箱已经验证过了"
}
```

- **429 Too Many Requests** - 频率限制
```json
{
  "success": false,
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "验证邮件发送过于频繁，请稍后重试",
  "retryAfter": 120
}
```

- **500 Internal Server Error** - 邮件发送失败
```json
{
  "success": false,
  "error": "EMAIL_SEND_FAILED",
  "message": "发送验证邮件失败，请稍后重试"
}
```

### 2. 查询邮箱验证状态

**端点**: `GET /api/auth/verify-email-status`

**描述**: 查询当前用户的邮箱验证状态

**认证**: 需要用户登录（Bearer Token 或 Cookie）

#### 请求

```http
GET /api/auth/verify-email-status HTTP/1.1
Host: localhost:10086
Authorization: Bearer <token>
```

#### 响应

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "emailVerified": false,
    "email": "<EMAIL>",
    "userId": "cm123456",
    "verificationRequired": true
  }
}
```

**错误响应**:

- **401 Unauthorized** - 用户未登录
```json
{
  "success": false,
  "error": "UNAUTHORIZED",
  "message": "请先登录"
}
```

- **500 Internal Server Error** - 服务器错误
```json
{
  "success": false,
  "error": "INTERNAL_ERROR",
  "message": "查询验证状态失败，请稍后重试"
}
```

### 3. 邮箱验证回调处理

**端点**: `GET /api/auth/verify-email-callback`

**描述**: 处理用户点击验证邮件中链接后的回调，完成邮箱验证流程

**认证**: 无需认证（通过token参数验证）

#### 请求

```http
GET /api/auth/verify-email-callback?token=<verification_token>&redirect=<redirect_url> HTTP/1.1
Host: localhost:10086
```

**查询参数**:
- `token` (必需): 验证令牌
- `redirect` (可选): 验证成功后的重定向URL，默认为 `/dashboard`

#### 响应

**成功响应 (200)** - 返回HTML页面:
```html
<!DOCTYPE html>
<html>
<head>
    <title>邮箱验证成功</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>邮箱验证成功！</h1>
        <p>恭喜 <strong>用户名</strong>，您的邮箱 <strong><EMAIL></strong> 已验证成功。</p>
        <p>页面将在 <span id="countdown">3</span> 秒后自动跳转...</p>
        <a href="/dashboard" class="btn">立即进入应用</a>
    </div>
    <script>
        // 自动跳转逻辑
    </script>
</body>
</html>
```

**错误响应 (200)** - 返回错误HTML页面:
```html
<!DOCTYPE html>
<html>
<head>
    <title>邮箱验证失败</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div class="container">
        <div class="error-icon">❌</div>
        <h1>邮箱验证失败</h1>
        <p>验证令牌无效或已过期，请重新发送验证邮件。</p>
        <a href="/resend-verification" class="btn">重新发送验证邮件</a>
    </div>
</body>
</html>
```

## 中间件和安全功能

### 邮箱验证状态检查中间件

**功能**: `requireEmailVerification`

**描述**: 确保用户已验证邮箱才能访问特定功能的中间件

**使用场景**: 保护需要邮箱验证的API端点

**错误响应 (403)**:
```json
{
  "success": false,
  "error": "EMAIL_NOT_VERIFIED",
  "message": "请先验证您的邮箱地址",
  "data": {
    "requireEmailVerification": true,
    "userId": "cm123456",
    "email": "<EMAIL>"
  }
}
```

### 频率限制中间件

**功能**: `emailVerificationRateLimit`

**描述**: 防止用户频繁重发验证邮件的中间件

**限制规则**:
- 每用户每2分钟最多1次验证邮件请求
- 连续失败超过5次后临时阻止10分钟

**响应头**:
```http
X-RateLimit-Limit: 1
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1735814400
Retry-After: 120
```

## 邮件模板系统

### 验证邮件模板

邮件服务支持多版本模板系统：

**v1版本** (默认):
- 简洁的设计风格
- 基础验证功能
- 支持邀请码显示

**v2版本** (增强):
- 现代化界面设计
- 更好的移动端适配
- 增强的品牌元素

### 模板数据结构

```typescript
interface EmailTemplateData {
  userEmail: string;           // 用户邮箱
  userName?: string;           // 用户姓名
  verificationUrl: string;     // 验证链接
  inviteCode?: string;         // 邀请码（如果有）
}
```

## 集成示例

### JavaScript/TypeScript 前端集成

```typescript
// 检查邮箱验证状态
async function checkEmailVerificationStatus() {
  try {
    const response = await fetch('/api/auth/verify-email-status', {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      return {
        isVerified: data.data.emailVerified,
        email: data.data.email,
        needsVerification: data.data.verificationRequired
      };
    }
    
    throw new Error(data.message);
  } catch (error) {
    console.error('检查验证状态失败:', error);
    throw error;
  }
}

// 重新发送验证邮件
async function resendVerificationEmail() {
  try {
    const response = await fetch('/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      return {
        message: data.message,
        email: data.data.email,
        resendCount: data.data.resendCount
      };
    }
    
    throw new Error(data.message);
  } catch (error) {
    console.error('重发验证邮件失败:', error);
    throw error;
  }
}

// 验证状态监听器
function setupEmailVerificationMonitor() {
  const checkInterval = setInterval(async () => {
    try {
      const status = await checkEmailVerificationStatus();
      
      if (status.isVerified) {
        clearInterval(checkInterval);
        // 验证成功，更新UI或重定向
        onEmailVerified();
      }
    } catch (error) {
      console.error('监听验证状态失败:', error);
    }
  }, 5000); // 每5秒检查一次
  
  return checkInterval;
}
```

### React 组件示例

```jsx
import React, { useState, useEffect } from 'react';

const EmailVerificationBanner = ({ user }) => {
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');

  useEffect(() => {
    checkVerificationStatus();
  }, []);

  const checkVerificationStatus = async () => {
    try {
      const status = await checkEmailVerificationStatus();
      setVerificationStatus(status);
    } catch (error) {
      console.error('检查验证状态失败:', error);
    }
  };

  const handleResendEmail = async () => {
    setIsResending(true);
    setResendMessage('');
    
    try {
      const result = await resendVerificationEmail();
      setResendMessage(result.message);
      
      // 开始监听验证状态
      const monitor = setupEmailVerificationMonitor();
      
      // 30秒后停止监听
      setTimeout(() => clearInterval(monitor), 30000);
    } catch (error) {
      setResendMessage(error.message);
    } finally {
      setIsResending(false);
    }
  };

  if (!verificationStatus || verificationStatus.isVerified) {
    return null;
  }

  return (
    <div className="email-verification-banner">
      <div className="banner-content">
        <div className="banner-icon">📧</div>
        <div className="banner-text">
          <h4>请验证您的邮箱</h4>
          <p>
            我们已向 <strong>{verificationStatus.email}</strong> 发送了验证邮件。
            请点击邮件中的链接完成验证。
          </p>
          {resendMessage && (
            <p className="resend-message">{resendMessage}</p>
          )}
        </div>
        <div className="banner-actions">
          <button 
            onClick={handleResendEmail}
            disabled={isResending}
            className="resend-btn"
          >
            {isResending ? '发送中...' : '重新发送'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationBanner;
```

## 错误处理和故障排除

### 常见错误

1. **EMAIL_NOT_VERIFIED (403)**
   - 原因: 用户邮箱未验证
   - 解决: 引导用户完成邮箱验证

2. **RATE_LIMIT_EXCEEDED (429)**
   - 原因: 验证邮件发送过于频繁
   - 解决: 等待指定时间后重试

3. **EMAIL_SEND_FAILED (500)**
   - 原因: 邮件服务异常
   - 解决: 检查邮件服务配置，稍后重试

4. **INVALID_TOKEN**
   - 原因: 验证令牌无效或过期
   - 解决: 重新发送验证邮件

### 调试建议

1. **检查邮件服务配置**
   ```bash
   # 验证环境变量
   echo $RESEND_API_KEY
   echo $BETTER_AUTH_URL
   ```

2. **查看邮件发送日志**
   ```sql
   SELECT * FROM email_log 
   WHERE email_type = 'verification' 
   ORDER BY created_at DESC 
   LIMIT 10;
   ```

3. **测试邮件服务连接**
   ```bash
   curl -X POST http://localhost:10086/api/auth/resend-verification \
        -H "Authorization: Bearer <token>" \
        -H "Content-Type: application/json"
   ```

## 最佳实践

### 前端集成

1. **状态管理**: 使用全局状态管理验证状态
2. **用户体验**: 提供清晰的验证状态提示
3. **错误处理**: 友好地处理各种错误情况
4. **自动检测**: 实现验证状态的自动检测和更新

### 后端配置

1. **频率限制**: 合理设置频率限制防止滥用
2. **邮件模板**: 根据品牌需求选择合适的模板版本
3. **监控告警**: 监控邮件发送成功率和失败原因
4. **日志记录**: 详细记录邮件验证相关的操作日志

### 安全考虑

1. **令牌过期**: 设置合理的验证令牌过期时间
2. **防重放**: 验证令牌只能使用一次
3. **IP限制**: 考虑添加基于IP的频率限制
4. **审计日志**: 记录所有验证相关的安全事件

## 版本历史

- **v1.0.0**: 基础邮箱验证功能
- **v1.1.0**: 增加频率限制和安全防护
- **v1.2.0**: 添加HTML模板和回调页面
- **v1.3.0**: 增强错误处理和监控功能