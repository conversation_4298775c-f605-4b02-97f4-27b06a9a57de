import { FastifyRequest, FastifyReply } from "fastify";
import { randomUUID } from "crypto";
import { auth } from "../auth.js";
import { db } from "../lib/drizzle.js";
import { user, emailLog, rateLimiter, securityEvent } from "../lib/auth-schema.js";
import { eq, and, sql } from "drizzle-orm";
import { rateLimiterService, DEFAULT_RATE_LIMITS } from "./rate-limiter.js";

/**
 * 增强的邮箱验证状态检查中间件
 * 确保用户已验证邮箱才能访问特定功能
 * 集成了频率限制、日志记录和安全监控
 */
export async function requireEmailVerification(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 获取用户session
    const headers = new Headers();
    Object.entries(request.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    const session = await auth.api.getSession({ headers });

    if (!session?.user) {
      return reply.status(401).send({
        success: false,
        error: "UNAUTHORIZED",
        message: "请先登录",
      });
    }

    // 检查邮箱验证状态
    const userData = await db
      .select({ emailVerified: user.emailVerified })
      .from(user)
      .where(eq(user.id, session.user.id))
      .limit(1);

    if (userData.length === 0) {
      return reply.status(404).send({
        success: false,
        error: "USER_NOT_FOUND",
        message: "用户不存在",
      });
    }

    if (!userData[0]?.emailVerified) {
      // 记录未验证邮箱的访问尝试
      await logEmailVerificationAttempt(
        session.user.id,
        session.user.email,
        'failed',
        request,
        { reason: 'email_not_verified', requiredAction: 'verify_email' }
      );
      
      return reply.status(403).send({
        success: false,
        error: "EMAIL_NOT_VERIFIED",
        message: "请先验证您的邮箱地址",
        data: {
          requireEmailVerification: true,
          userId: session.user.id,
          email: session.user.email,
        },
      });
    }

    // 邮箱已验证，继续处理请求
    // 记录验证成功的日志
    await logEmailVerificationAttempt(session.user.id, session.user.email, 'success', request);
    
  } catch (error) {
    console.error("邮箱验证中间件错误:", error);
    
    // 记录验证失败的安全事件
    await recordSecurityEvent(
      null,
      'email_verification_error',
      'medium',
      request,
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );
    
    return reply.status(500).send({
      success: false,
      error: "INTERNAL_ERROR",
      message: "验证邮箱状态时出错",
    });
  }
}

/**
 * 可选的邮箱验证检查
 * 不会阻止请求，但会在响应中添加验证状态信息
 */
export async function optionalEmailVerificationCheck(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    const headers = new Headers();
    Object.entries(request.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    const session = await auth.api.getSession({ headers });

    if (session?.user) {
      const userData = await db
        .select({ emailVerified: user.emailVerified })
        .from(user)
        .where(eq(user.id, session.user.id))
        .limit(1);

      // 将验证状态添加到请求对象中，供后续处理使用
      (request as any).emailVerificationStatus = {
        isVerified: userData[0]?.emailVerified || false,
        userId: session.user.id,
        email: session.user.email,
      };
    }
  } catch (error) {
    console.error("可选邮箱验证检查错误:", error);
    // 不阻止请求继续执行
  }
}

/**
 * 邮箱验证提醒工具函数
 * 为需要邮箱验证的API响应添加提醒信息
 */
export function addEmailVerificationReminder(
  response: any,
  emailVerificationStatus?: {
    isVerified: boolean;
    userId: string;
    email: string;
  }
) {
  if (
    emailVerificationStatus &&
    !emailVerificationStatus.isVerified
  ) {
    return {
      ...response,
      emailVerification: {
        required: true,
        message: "建议验证邮箱以获得完整功能访问权限",
        userId: emailVerificationStatus.userId,
        email: emailVerificationStatus.email,
      },
    };
  }
  return response;
}

/**
 * 邮件验证频率限制中间件
 * 防止用户频繁重发验证邮件
 */
export async function emailVerificationRateLimit(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 获取用户会话
    const headers = new Headers();
    Object.entries(request.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    const session = await auth.api.getSession({ headers });
    const identifier = session?.user?.email || getClientIP(request) || 'anonymous';
    
    // 检查频率限制
    const rateLimitConfig = DEFAULT_RATE_LIMITS.verification_request;
    if (!rateLimitConfig) {
      throw new Error('Rate limit configuration not found for verification_request');
    }
    
    const result = await rateLimiterService.checkRateLimit(
      identifier,
      rateLimitConfig,
      request
    );

    // 设置响应头
    reply.header('X-RateLimit-Limit', rateLimitConfig.maxRequests);
    reply.header('X-RateLimit-Remaining', result.remaining);
    reply.header('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000));

    if (!result.allowed) {
      if (result.retryAfter) {
        reply.header('Retry-After', result.retryAfter);
      }

      // 记录频率限制事件
      await recordSecurityEvent(
        session?.user?.id || null,
        'rate_limit_exceeded',
        'medium',
        request,
        {
          action: 'verification_request',
          identifier,
          requestCount: result.remaining + 1,
          maxRequests: rateLimitConfig.maxRequests,
        }
      );

      return reply.status(429).send({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: result.blocked 
          ? `验证邮件发送过于频繁，已被暂时阻止。请在 ${result.retryAfter} 秒后重试。`
          : '验证邮件发送过于频繁，请稍后重试。',
        retryAfter: result.retryAfter,
        resetTime: result.resetTime.toISOString(),
      });
    }

    // 将频率限制信息附加到请求对象
    (request as any).emailVerificationRateLimit = result;
    
  } catch (error) {
    console.error('邮件验证频率限制中间件错误:', error);
    // 错误时允许请求继续，但记录错误
    await recordSecurityEvent(
      null,
      'middleware_error',
      'low',
      request,
      { middleware: 'emailVerificationRateLimit', error: error instanceof Error ? error.message : 'Unknown error' }
    );
  }
}

/**
 * 记录邮件验证尝试日志
 */
export async function logEmailVerificationAttempt(
  userId: string,
  email: string,
  status: 'success' | 'failed' | 'rate_limited',
  request?: FastifyRequest,
  additionalInfo?: any
): Promise<void> {
  try {
    const logData = {
      id: randomUUID(),
      userId,
      email,
      emailType: 'verification' as const,
      status: status === 'success' ? 'delivered' : 'failed',
      provider: 'internal',
      attemptCount: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: JSON.stringify({
        verificationAttempt: true,
        status,
        ipAddress: getClientIP(request),
        userAgent: request?.headers['user-agent'],
        timestamp: new Date().toISOString(),
        ...additionalInfo,
      }),
    };

    await db.insert(emailLog).values(logData);
  } catch (error) {
    console.error('记录邮件验证尝试日志失败:', error);
  }
}

/**
 * 记录安全事件
 */
export async function recordSecurityEvent(
  userId: string | null,
  eventType: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  request?: FastifyRequest,
  details?: any
): Promise<void> {
  try {
    const eventData = {
      id: randomUUID(),
      userId,
      eventType,
      severity,
      ipAddress: getClientIP(request),
      userAgent: request?.headers['user-agent'] as string,
      details: JSON.stringify({
        timestamp: new Date().toISOString(),
        method: request?.method,
        path: request?.url,
        ...details,
      }),
      resolved: false,
      createdAt: new Date(),
    };

    await db.insert(securityEvent).values(eventData);
  } catch (error) {
    console.error('记录安全事件失败:', error);
  }
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request?: FastifyRequest): string | undefined {
  if (!request) return undefined;
  
  return (
    (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (request.headers['x-real-ip'] as string) ||
    request.ip ||
    request.socket.remoteAddress
  );
}

/**
 * 邮件发送状态跟踪中间件
 * 用于跟踪邮件发送的整个生命周期
 */
export async function trackEmailSending(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 在请求开始时初始化邮件跟踪
    (request as any).emailTracking = {
      startTime: Date.now(),
      emailLogId: randomUUID(),
    };
    
    // 添加响应钩子用于记录邮件发送结果
    (reply as any).addHook?.('onSend', async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      const emailTracking = (request as any).emailTracking;
      if (emailTracking && reply.statusCode === 200) {
        // 邮件发送成功，更新日志状态
        await updateEmailLogStatus(emailTracking.emailLogId, 'sent');
      }
      return payload;
    });
    
  } catch (error) {
    console.error('邮件发送跟踪中间件错误:', error);
  }
}

/**
 * 更新邮件日志状态
 */
export async function updateEmailLogStatus(
  emailLogId: string,
  status: 'pending' | 'sent' | 'failed' | 'delivered' | 'bounced',
  errorMessage?: string
): Promise<void> {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };
    
    if (status === 'sent') {
      updateData.sentAt = new Date();
    } else if (status === 'delivered') {
      updateData.deliveredAt = new Date();
    } else if (status === 'failed' && errorMessage) {
      updateData.errorMessage = errorMessage;
    }
    
    await db
      .update(emailLog)
      .set(updateData)
      .where(eq(emailLog.id, emailLogId));
      
  } catch (error) {
    console.error('更新邮件日志状态失败:', error);
  }
}