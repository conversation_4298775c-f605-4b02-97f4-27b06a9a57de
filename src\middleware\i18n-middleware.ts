/**
 * 国际化中间件
 * 处理语言检测、设置和上下文管理
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { i18nService, SupportedLanguage } from '../services/i18n-service.js';

/**
 * 将常见的语言名称转换为标准的语言代码
 */
export function normalizeLanguageCode(language: string): string | null {
  const normalizedInput = language.toLowerCase().trim();

  const languageMap: Record<string, string> = {
    // 中文相关
    'chinese': 'zh-CN',
    'zh': 'zh-CN',
    'cn': 'zh-CN',
    'zh-cn': 'zh-CN',
    'zh_cn': 'zh-CN',
    'simplified chinese': 'zh-CN',
    '中文': 'zh-CN',
    '简体中文': 'zh-CN',

    // 英文相关
    'english': 'en-US',
    'en': 'en-US',
    'us': 'en-US',
    'en-us': 'en-US',
    'en_us': 'en-US',
    'american english': 'en-US',
    'american': 'en-US',

    // 日文相关
    'japanese': 'ja-JP',
    'ja': 'ja-JP',
    'jp': 'ja-JP',
    'ja-jp': 'ja-JP',
    'ja_jp': 'ja-JP',
    '日本語': 'ja-JP',
    '日语': 'ja-JP',

    // 韩文相关
    'korean': 'ko-KR',
    'ko': 'ko-KR',
    'kr': 'ko-KR',
    'ko-kr': 'ko-KR',
    'ko_kr': 'ko-KR',
    '한국어': 'ko-KR',
    '韩语': 'ko-KR',
    '朝鲜语': 'ko-KR',
  };

  return languageMap[normalizedInput] || null;
}

// 扩展 FastifyRequest 类型以包含语言信息
declare module 'fastify' {
  interface FastifyRequest {
    language: SupportedLanguage;
    t: (key: string, params?: Record<string, string | number>) => string;
    getMultiLanguageResponse: (
      key: string,
      params?: Record<string, string | number>
    ) => {
      message: string;
      messageKey: string;
      language: SupportedLanguage;
    };
  }
}

/**
 * 语言检测和设置中间件
 * 完全基于请求参数，不依赖数据库查询
 */
export async function i18nMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  try {
    let detectedLanguage: SupportedLanguage = 'zh-CN'; // 默认语言

    // 严格按照优先级检测语言，不依赖数据库

    // 第一优先级：检查请求头中的语言设置
    const headerLanguage = request.headers['language'] as string;
    if (headerLanguage) {
      // 将 chinese/english 格式转换为标准语言代码
      const normalizedLanguage = normalizeLanguageCode(headerLanguage);
      if (normalizedLanguage && i18nService.isSupportedLanguage(normalizedLanguage)) {
        detectedLanguage = normalizedLanguage as SupportedLanguage;
      }
    }

    if (detectedLanguage === 'zh-CN') { // 如果还是默认语言，继续检查其他参数
      // 检查 Accept-Language 头
      const acceptLanguage = request.headers['accept-language'];
      if (acceptLanguage) {
        const parsedLanguage = i18nService.detectLanguage(undefined, acceptLanguage);
        if (parsedLanguage !== 'zh-CN') { // 如果检测到非默认语言
          detectedLanguage = parsedLanguage;
        } else {
          // 第二优先级：检查请求体中的语言参数（适用于POST请求）
          const bodyLanguage = (request.body as any)?.language;
          if (bodyLanguage) {
            const normalizedBodyLanguage = normalizeLanguageCode(bodyLanguage);
            if (normalizedBodyLanguage && i18nService.isSupportedLanguage(normalizedBodyLanguage)) {
              detectedLanguage = normalizedBodyLanguage as SupportedLanguage;
            }
          }

          if (detectedLanguage === 'zh-CN') {
            // 第三优先级：检查查询参数中的语言设置（适用于GET请求）
            const queryLanguage = (request.query as { language?: string })?.language;
            if (queryLanguage) {
              const normalizedQueryLanguage = normalizeLanguageCode(queryLanguage);
              if (normalizedQueryLanguage && i18nService.isSupportedLanguage(normalizedQueryLanguage)) {
                detectedLanguage = normalizedQueryLanguage as SupportedLanguage;
              }
            }
            // 如果以上都没有，使用默认语言 zh-CN（已在开头设置）
          }
        }
      } else {
        // 没有 Accept-Language 头，继续检查其他参数

        // 第二优先级：检查请求体中的语言参数（适用于POST请求）
        const bodyLanguage = (request.body as any)?.language;
        if (bodyLanguage) {
          const normalizedBodyLanguage = normalizeLanguageCode(bodyLanguage);
          if (normalizedBodyLanguage && i18nService.isSupportedLanguage(normalizedBodyLanguage)) {
            detectedLanguage = normalizedBodyLanguage as SupportedLanguage;
          }
        }

        if (detectedLanguage === 'zh-CN') {
          // 第三优先级：检查查询参数中的语言设置（适用于GET请求）
          const queryLanguage = (request.query as { language?: string })?.language;
          if (queryLanguage) {
            const normalizedQueryLanguage = normalizeLanguageCode(queryLanguage);
            if (normalizedQueryLanguage && i18nService.isSupportedLanguage(normalizedQueryLanguage)) {
              detectedLanguage = normalizedQueryLanguage as SupportedLanguage;
            }
          }
          // 如果以上都没有，使用默认语言 zh-CN（已在开头设置）
        }
      }
    }

    // 3. 设置请求的语言上下文
    request.language = detectedLanguage;

    // 4. 添加翻译函数到请求对象
    request.t = (key: string, params?: Record<string, string | number>) => {
      return i18nService.translate(key, detectedLanguage, params);
    };

    // 5. 添加多语言响应函数
    request.getMultiLanguageResponse = (
      key: string,
      params?: Record<string, string | number>
    ) => {
      return i18nService.getMultiLanguageResponse(key, detectedLanguage, params);
    };

    // 6. 设置响应头
    reply.header('Content-Language', detectedLanguage);

  } catch (error) {
    console.error('I18n middleware error:', error);
    
    // 出错时使用默认语言
    request.language = 'zh-CN';
    request.t = (key: string, params?: Record<string, string | number>) => {
      return i18nService.translate(key, 'zh-CN', params);
    };
    request.getMultiLanguageResponse = (
      key: string,
      params?: Record<string, string | number>
    ) => {
      return i18nService.getMultiLanguageResponse(key, 'zh-CN', params);
    };
  }
}

/**
 * 语言验证中间件
 * 验证请求中的语言参数是否有效
 */
export async function validateLanguageMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
  
  // 检查查询参数中的语言
  const queryLanguage = (request.query as any)?.language;
  if (queryLanguage) {
    const normalizedQueryLanguage = normalizeLanguageCode(queryLanguage);
    if (!normalizedQueryLanguage || !supportedLanguages.includes(normalizedQueryLanguage as SupportedLanguage)) {
      return reply.status(400).send({
        error: 'INVALID_LANGUAGE',
        message: request.t('validation.invalidFormat'),
        supportedLanguages,
        supportedFormats: ['chinese', 'english', 'japanese', 'korean', 'zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
      });
    }
  }

  // 检查请求体中的语言
  const bodyLanguage = (request.body as any)?.language;
  if (bodyLanguage) {
    const normalizedBodyLanguage = normalizeLanguageCode(bodyLanguage);
    if (!normalizedBodyLanguage || !supportedLanguages.includes(normalizedBodyLanguage as SupportedLanguage)) {
      return reply.status(400).send({
        error: 'INVALID_LANGUAGE',
        message: request.t('validation.invalidFormat'),
        supportedLanguages,
        supportedFormats: ['chinese', 'english', 'japanese', 'korean', 'zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
      });
    }
  }
}

/**
 * 创建多语言错误响应
 */
export function createI18nErrorResponse(
  request: FastifyRequest,
  errorType: string,
  messageKey: string,
  statusCode: number = 500,
  params?: Record<string, string | number>
) {
  const response = request.getMultiLanguageResponse(messageKey, params);
  
  return {
    status: statusCode,
    body: {
      success: false,
      error: errorType,
      ...response,
    },
  };
}

/**
 * 创建多语言成功响应
 */
export function createI18nSuccessResponse(
  request: FastifyRequest,
  messageKey: string,
  data?: any,
  params?: Record<string, string | number>
) {
  const response = request.getMultiLanguageResponse(messageKey, params);
  
  return {
    success: true,
    ...response,
    ...(data && { data }),
  };
}

/**
 * 语言切换处理函数
 * 仅更新当前请求的语言上下文，不涉及数据库操作
 */
export async function handleLanguageSwitch(
  request: FastifyRequest,
  reply: FastifyReply,
  newLanguage: SupportedLanguage
): Promise<void> {
  try {
    // 更新当前请求的语言上下文
    request.language = newLanguage;
    request.t = (key: string, params?: Record<string, string | number>) => {
      return i18nService.translate(key, newLanguage, params);
    };
    request.getMultiLanguageResponse = (
      key: string,
      params?: Record<string, string | number>
    ) => {
      return i18nService.getMultiLanguageResponse(key, newLanguage, params);
    };

    // 设置响应头
    reply.header('Content-Language', newLanguage);

  } catch (error) {
    console.error('Language switch error:', error);
    throw error;
  }
}

/**
 * 获取支持的语言列表
 */
export function getSupportedLanguages(): {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
}[] {
  return [
    { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
    { code: 'en-US', name: 'English (US)', nativeName: 'English' },
    { code: 'ja-JP', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko-KR', name: 'Korean', nativeName: '한국어' },
  ];
}

/**
 * 预热国际化服务
 * 在应用启动时调用，预加载所有语言资源
 */
export function warmupI18nService(): void {
  try {
    i18nService.preloadAllLanguages();
    console.log('I18n service warmed up successfully');
  } catch (error) {
    console.error('Failed to warm up I18n service:', error);
  }
}
