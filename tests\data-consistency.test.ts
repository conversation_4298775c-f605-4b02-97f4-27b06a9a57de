/**
 * 数据一致性和事务安全测试
 * 测试重构后的注册功能在各种情况下的数据完整性
 */

import { describe, it, expect, jest } from '@jest/globals';

describe('数据一致性和事务安全测试', () => {

  describe('临时注册数据一致性', () => {
    /**
     * 模拟数据库事务操作
     */
    class MockTransaction {
      operations: Array<{ type: string; table: string; data: any }> = [];
      rolledBack = false;
      committed = false;

      insert(table: string) {
        return {
          values: (data: any) => {
            this.operations.push({ type: 'insert', table, data });
            return Promise.resolve({ rowCount: 1 });
          }
        };
      }

      update(table: string) {
        return {
          set: (data: any) => ({
            where: (condition: any) => {
              this.operations.push({ type: 'update', table, data });
              return Promise.resolve({ rowCount: 1 });
            }
          })
        };
      }

      select() {
        return {
          from: () => ({
            where: () => ({
              limit: () => Promise.resolve([])
            })
          })
        };
      }

      rollback() {
        this.rolledBack = true;
        this.operations = [];
      }

      commit() {
        this.committed = true;
      }
    }

    /**
     * 模拟临时注册创建的事务逻辑
     */
    async function simulatePendingRegistrationCreation(
      data: any,
      shouldFail: boolean = false
    ): Promise<{ success: boolean; transaction: MockTransaction; error?: string }> {
      const transaction = new MockTransaction();

      try {
        // 1. 检查用户是否已存在
        await transaction.select().from('user').where({ email: data.email }).limit(1);
        
        // 2. 检查临时注册记录是否已存在
        await transaction.select().from('pending_registration').where({ email: data.email }).limit(1);

        // 3. 创建临时注册记录
        await transaction.insert('pending_registration').values({
          id: data.pendingId,
          email: data.email, 
          password: data.password,
          name: data.name,
          companyName: data.companyName,
          inviteCode: data.inviteCode,
          status: 'pending',
          pendingOrganizationId: data.orgId,
          pendingOrganizationSlug: data.orgSlug,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        });

        // 模拟失败情况
        if (shouldFail) {
          throw new Error('模拟事务失败');
        }

        transaction.commit();
        return { success: true, transaction };

      } catch (error) {
        transaction.rollback();
        return { 
          success: false, 
          transaction, 
          error: error instanceof Error ? error.message : String(error) 
        };
      }
    }

    it('应该在事务成功时正确创建临时注册记录', async () => {
      const testData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        password: 'hashed-password',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE',
        orgId: 'org-123',
        orgSlug: 'test-company-abc'
      };

      const result = await simulatePendingRegistrationCreation(testData);

      expect(result.success).toBe(true);
      expect(result.transaction.committed).toBe(true);
      expect(result.transaction.rolledBack).toBe(false);
      expect(result.transaction.operations).toHaveLength(1);
      
      const insertOp = result.transaction.operations[0];
      expect(insertOp?.type).toBe('insert');
      expect(insertOp?.table).toBe('pending_registration');
      expect(insertOp?.data.email).toBe('<EMAIL>');
      expect(insertOp?.data.status).toBe('pending');
    });

    it('应该在事务失败时正确回滚', async () => {
      const testData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        password: 'hashed-password',
        name: 'Test User',
        companyName: 'Test Company',
        inviteCode: 'VALID_CODE',
        orgId: 'org-123',
        orgSlug: 'test-company-abc'
      };

      const result = await simulatePendingRegistrationCreation(testData, true);

      expect(result.success).toBe(false);
      expect(result.transaction.committed).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.transaction.operations).toHaveLength(0); // 回滚后清空
      expect(result.error).toBe('模拟事务失败');
    });
  });

  describe('邮箱验证完成的数据一致性', () => {
    /**
     * 模拟数据库事务操作（重复定义以解决作用域问题）
     */
    class MockTransaction {
      operations: Array<{ type: string; table: string; data: any }> = [];
      rolledBack = false;
      committed = false;

      insert(table: string) {
        return {
          values: (data: any) => {
            this.operations.push({ type: 'insert', table, data });
            return Promise.resolve({ rowCount: 1 });
          }
        };
      }

      update(table: string) {
        return {
          set: (data: any) => ({
            where: (condition: any) => {
              this.operations.push({ type: 'update', table, data });
              return Promise.resolve({ rowCount: 1 });
            }
          })
        };
      }

      select() {
        return {
          from: () => ({
            where: () => ({
              limit: () => Promise.resolve([])
            })
          })
        };
      }

      rollback() {
        this.rolledBack = true;
        this.operations = [];
      }

      commit() {
        this.committed = true;
      }
    }

    /**
     * 模拟完整的用户注册事务
     */
    async function simulateUserRegistrationCompletion(
      pendingData: any,
      shouldFailAt?: 'auth' | 'user_update' | 'org_create' | 'member_create' | 'subscription_create'
    ): Promise<{ success: boolean; transaction: MockTransaction; error?: string }> {
      const transaction = new MockTransaction();

      try {
        // 1. Better Auth 用户创建（模拟）
        if (shouldFailAt === 'auth') {
          throw new Error('Better Auth 用户创建失败');
        }
        const authUserId = 'auth-user-' + Math.random().toString(36).substring(7);

        // 2. 更新用户扩展信息
        if (shouldFailAt === 'user_update') {
          throw new Error('用户信息更新失败');
        }
        await transaction.update('user').set({
          companyName: pendingData.companyName,
          language: pendingData.language,
          emailVerified: true,
        }).where({ id: authUserId });

        // 3. 创建组织
        if (shouldFailAt === 'org_create') {
          throw new Error('组织创建失败');
        }
        await transaction.insert('organization').values({
          id: pendingData.pendingOrganizationId,
          name: pendingData.companyName,
          slug: pendingData.pendingOrganizationSlug,
          createdAt: new Date(),
        });

        // 4. 创建成员关系
        if (shouldFailAt === 'member_create') {
          throw new Error('成员关系创建失败');
        }
        await transaction.insert('member').values({
          id: 'member-' + Math.random().toString(36).substring(7),
          organizationId: pendingData.pendingOrganizationId,
          userId: authUserId,
          role: 'owner',
          createdAt: new Date(),
        });

        // 5. 创建默认订阅
        if (shouldFailAt === 'subscription_create') {
          throw new Error('订阅创建失败');
        }
        await transaction.insert('subscription').values({
          id: 'sub-' + Math.random().toString(36).substring(7),
          organizationId: pendingData.pendingOrganizationId,
          plan: 'basic',
          memberLimit: 5,
          createdAt: new Date(),
        });

        // 6. 更新临时注册状态
        await transaction.update('pending_registration').set({
          status: 'verified',
          registrationStep: 'completed',
          verifiedAt: new Date(),
        }).where({ id: pendingData.id });

        transaction.commit();
        return { success: true, transaction };

      } catch (error) {
        transaction.rollback();
        return { 
          success: false, 
          transaction, 
          error: error instanceof Error ? error.message : String(error) 
        };
      }
    }

    const mockPendingData = {
      id: 'pending-123',
      email: '<EMAIL>',
      companyName: 'Test Company',
      language: 'chinese',
      pendingOrganizationId: 'org-123',
      pendingOrganizationSlug: 'test-company-abc'
    };

    it('应该成功完成完整的用户注册流程', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData);

      expect(result.success).toBe(true);
      expect(result.transaction.committed).toBe(true);
      expect(result.transaction.rolledBack).toBe(false);
      
      // 验证所有必要的操作都被执行
      expect(result.transaction.operations).toHaveLength(5);
      
      const operations = result.transaction.operations;
      expect(operations.find(op => op.table === 'user' && op.type === 'update')).toBeDefined();
      expect(operations.find(op => op.table === 'organization' && op.type === 'insert')).toBeDefined();
      expect(operations.find(op => op.table === 'member' && op.type === 'insert')).toBeDefined();
      expect(operations.find(op => op.table === 'subscription' && op.type === 'insert')).toBeDefined();
      expect(operations.find(op => op.table === 'pending_registration' && op.type === 'update')).toBeDefined();
    });

    it('应该在 Better Auth 失败时回滚整个事务', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData, 'auth');

      expect(result.success).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.transaction.operations).toHaveLength(0);
      expect(result.error).toBe('Better Auth 用户创建失败');
    });

    it('应该在用户信息更新失败时回滚', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData, 'user_update');

      expect(result.success).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.error).toBe('用户信息更新失败');
    });

    it('应该在组织创建失败时回滚', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData, 'org_create');

      expect(result.success).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.error).toBe('组织创建失败');
    });

    it('应该在成员关系创建失败时回滚', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData, 'member_create');

      expect(result.success).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.error).toBe('成员关系创建失败');
    });

    it('应该在订阅创建失败时回滚', async () => {
      const result = await simulateUserRegistrationCompletion(mockPendingData, 'subscription_create');

      expect(result.success).toBe(false);
      expect(result.transaction.rolledBack).toBe(true);
      expect(result.error).toBe('订阅创建失败');
    });
  });

  describe('数据一致性验证', () => {
    /**
     * 验证注册数据的一致性规则
     */
    function validateRegistrationDataConsistency(data: any): { valid: boolean; issues: string[] } {
      const issues: string[] = [];

      // 1. 基础字段一致性
      if (!data.pendingId || !data.email || !data.name || !data.companyName) {
        issues.push('缺少必要的基础字段');
      }

      // 2. 组织 ID 和 Slug 一致性
      if (!data.pendingOrganizationId || !data.pendingOrganizationSlug) {
        issues.push('缺少组织标识符');
      }

      // 3. 组织名称和 Slug 的一致性检查
      if (data.companyName && data.pendingOrganizationSlug) {
        const expectedSlugPrefix = data.companyName
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-")
          .trim()
          .substring(0, 50);
        
        if (!data.pendingOrganizationSlug.startsWith(expectedSlugPrefix)) {
          issues.push('组织 Slug 与公司名称不一致');
        }
      }

      // 4. 时间戳一致性
      if (data.createdAt && data.expiresAt) {
        const created = new Date(data.createdAt);
        const expires = new Date(data.expiresAt);
        
        if (expires <= created) {
          issues.push('过期时间不能早于或等于创建时间');
        }

        const expectedExpiry = new Date(created.getTime() + 24 * 60 * 60 * 1000);
        const timeDiff = Math.abs(expires.getTime() - expectedExpiry.getTime());
        
        if (timeDiff > 1000) { // 允许1秒误差
          issues.push('过期时间不符合24小时规则');
        }
      }

      // 5. 状态一致性
      if (data.status && data.registrationStep) {
        const validCombinations = [
          { status: 'pending', step: 'email_verification' },
          { status: 'verified', step: 'completed' },
          { status: 'expired', step: 'email_verification' },
          { status: 'failed', step: 'failed' }
        ];

        const isValidCombination = validCombinations.some(
          combo => combo.status === data.status && combo.step === data.registrationStep
        );

        if (!isValidCombination) {
          issues.push(`无效的状态组合: ${data.status}/${data.registrationStep}`);
        }
      }

      // 6. 语言代码有效性
      if (data.language) {
        const validLanguages = ['chinese', 'english', 'japanese', 'korean'];
        if (!validLanguages.includes(data.language)) {
          issues.push('无效的语言代码');
        }
      }

      return {
        valid: issues.length === 0,
        issues
      };
    }

    it('应该验证有效的注册数据', () => {
      const validData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        name: 'Test User',
        companyName: 'Test Company',
        pendingOrganizationId: 'org-123',
        pendingOrganizationSlug: 'test-company-abc123',
        status: 'pending',
        registrationStep: 'email_verification',
        language: 'chinese',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      const result = validateRegistrationDataConsistency(validData);
      expect(result.valid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('应该检测缺少必要字段', () => {
      const incompleteData = {
        email: '<EMAIL>',
        // 缺少其他必要字段
      };

      const result = validateRegistrationDataConsistency(incompleteData);
      expect(result.valid).toBe(false);
      expect(result.issues).toContain('缺少必要的基础字段');
    });

    it('应该检测无效的状态组合', () => {
      const invalidData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        name: 'Test User',
        companyName: 'Test Company',
        pendingOrganizationId: 'org-123',
        pendingOrganizationSlug: 'test-company-abc123',
        status: 'verified',
        registrationStep: 'email_verification', // 无效组合
        language: 'chinese'
      };

      const result = validateRegistrationDataConsistency(invalidData);
      expect(result.valid).toBe(false);
      expect(result.issues).toContain('无效的状态组合: verified/email_verification');
    });

    it('应该检测无效的时间戳', () => {
      const now = new Date();
      const invalidData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        name: 'Test User',
        companyName: 'Test Company',
        pendingOrganizationId: 'org-123',
        pendingOrganizationSlug: 'test-company-abc123',
        status: 'pending',
        registrationStep: 'email_verification',
        language: 'chinese',
        createdAt: now,
        expiresAt: new Date(now.getTime() - 1000) // 过期时间早于创建时间
      };

      const result = validateRegistrationDataConsistency(invalidData);
      expect(result.valid).toBe(false);
      expect(result.issues).toContain('过期时间不能早于或等于创建时间');
    });

    it('应该检测24小时过期规则违反', () => {
      const now = new Date();
      const invalidData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        name: 'Test User',
        companyName: 'Test Company',
        pendingOrganizationId: 'org-123',
        pendingOrganizationSlug: 'test-company-abc123',
        status: 'pending',
        registrationStep: 'email_verification',
        language: 'chinese',
        createdAt: now,
        expiresAt: new Date(now.getTime() + 48 * 60 * 60 * 1000) // 48小时后过期
      };

      const result = validateRegistrationDataConsistency(invalidData);
      expect(result.valid).toBe(false);
      expect(result.issues).toContain('过期时间不符合24小时规则');
    });

    it('应该检测无效的语言代码', () => {
      const invalidData = {
        pendingId: 'pending-123',
        email: '<EMAIL>',
        name: 'Test User',
        companyName: 'Test Company',
        pendingOrganizationId: 'org-123',
        pendingOrganizationSlug: 'test-company-abc123',
        status: 'pending',
        registrationStep: 'email_verification',
        language: 'invalid-lang'
      };

      const result = validateRegistrationDataConsistency(invalidData);
      expect(result.valid).toBe(false);
      expect(result.issues).toContain('无效的语言代码');
    });
  });

  describe('并发处理和竞态条件', () => {
    /**
     * 模拟并发注册请求
     */
    async function simulateConcurrentRegistrations(
      emails: string[],
      sameInviteCode: boolean = false
    ): Promise<Array<{ email: string; success: boolean; error?: string }>> {
      const results: Array<{ email: string; success: boolean; error?: string }> = [];
      
      // 模拟数据库中已存在的邮箱
      const existingEmails = new Set<string>();
      
      // 模拟邀请码使用状态
      let inviteCodeUsed = false;
      
      for (const email of emails) {
        try {
          // 检查邮箱是否已存在
          if (existingEmails.has(email)) {
            results.push({
              email,
              success: false,
              error: '该邮箱已注册'
            });
            continue;
          }

          // 如果使用相同邀请码且邀请码只能使用一次
          if (sameInviteCode && inviteCodeUsed) {
            results.push({
              email,
              success: false,
              error: '邀请码已被使用'
            });
            continue;
          }

          // 模拟成功注册
          existingEmails.add(email);
          if (sameInviteCode) {
            inviteCodeUsed = true;
          }

          results.push({
            email,
            success: true
          });

        } catch (error) {
          results.push({
            email,
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return results;
    }

    it('应该正确处理不同邮箱的并发注册', async () => {
      const emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const results = await simulateConcurrentRegistrations(emails);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    it('应该阻止相同邮箱的重复注册', async () => {
      const emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const results = await simulateConcurrentRegistrations(emails);

      expect(results).toHaveLength(3);
      expect(results[0]?.success).toBe(true);
      expect(results[1]?.success).toBe(false);
      expect(results[2]?.success).toBe(false);
      expect(results[1]?.error).toBe('该邮箱已注册');
      expect(results[2]?.error).toBe('该邮箱已注册');
    });

    it('应该正确处理一次性邀请码的并发使用', async () => {
      const emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const results = await simulateConcurrentRegistrations(emails, true);

      expect(results).toHaveLength(3);
      expect(results[0]?.success).toBe(true);
      expect(results[1]?.success).toBe(false);
      expect(results[2]?.success).toBe(false);
      expect(results[1]?.error).toBe('邀请码已被使用');
      expect(results[2]?.error).toBe('邀请码已被使用');
    });
  });

  describe('数据清理和过期处理', () => {
    /**
     * 模拟清理过期注册记录
     */
    function simulateCleanupExpiredRegistrations(
      pendingRegistrations: Array<{
        id: string;
        email: string;
        status: 'pending' | 'verified' | 'expired';
        createdAt: Date;
        expiresAt: Date;
      }>
    ): { cleanedCount: number; remainingRecords: typeof pendingRegistrations } {
      const now = new Date();
      let cleanedCount = 0;
      
      const remainingRecords = pendingRegistrations.filter(record => {
        if (record.status === 'pending' && record.expiresAt < now) {
          cleanedCount++;
          return false; // 删除过期记录
        }
        return true; // 保留有效记录
      });

      return { cleanedCount, remainingRecords };
    }

    it('应该清理过期的待验证记录', () => {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

      const testRecords = [
        {
          id: 'pending-1',
          email: '<EMAIL>',
          status: 'pending' as const,
          createdAt: oneDayAgo,
          expiresAt: oneHourAgo // 已过期
        },
        {
          id: 'pending-2',
          email: '<EMAIL>',
          status: 'pending' as const,
          createdAt: now,
          expiresAt: oneHourLater // 未过期
        },
        {
          id: 'pending-3',
          email: '<EMAIL>',
          status: 'verified' as const,
          createdAt: oneDayAgo,
          expiresAt: oneHourAgo // 已验证，不应被清理
        }
      ];

      const result = simulateCleanupExpiredRegistrations(testRecords);

      expect(result.cleanedCount).toBe(1);
      expect(result.remainingRecords).toHaveLength(2);
      expect(result.remainingRecords.find(r => r.email === '<EMAIL>')).toBeDefined();
      expect(result.remainingRecords.find(r => r.email === '<EMAIL>')).toBeDefined();
      expect(result.remainingRecords.find(r => r.email === '<EMAIL>')).toBeUndefined();
    });

    it('应该保留所有有效记录', () => {
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

      const testRecords = [
        {
          id: 'pending-1',
          email: '<EMAIL>',
          status: 'pending' as const,
          createdAt: now,
          expiresAt: oneHourLater
        },
        {
          id: 'pending-2',
          email: '<EMAIL>',
          status: 'pending' as const,
          createdAt: now,
          expiresAt: oneHourLater
        }
      ];

      const result = simulateCleanupExpiredRegistrations(testRecords);

      expect(result.cleanedCount).toBe(0);
      expect(result.remainingRecords).toHaveLength(2);
    });
  });
});