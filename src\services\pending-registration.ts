/**
 * 临时注册服务
 * 处理邮箱验证前的用户注册信息暂存和管理
 */

import { randomUUID } from "crypto";
import { db } from "../lib/drizzle.js";
import { 
  pendingRegistration, 
  registrationVerificationLog, 
  user, 
  organization, 
  member, 
  subscription 
} from "../lib/auth-schema.js";
import { eq, and, lt, sql } from "drizzle-orm";
import { auth } from "../auth.js";
import { inviteCodeService } from "./invite-code.js";
import { emailService } from "./email-service.js";

// 类型定义
export interface PendingRegistrationData {
  email: string;
  password: string;
  name: string;
  companyName: string;
  inviteCode: string;
  language?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface RegistrationResult {
  success: boolean;
  pendingId?: string;
  error?: string;
  verificationSent?: boolean;
  nextSteps?: {
    requireEmailVerification: boolean;
    verificationEmailSent: boolean;
    instructions: string;
    canResendEmail: boolean;
    troubleshooting?: string;
  };
}

export interface VerificationResult {
  success: boolean;
  userId?: string;
  organizationId?: string;
  error?: string;
}

/**
 * 临时注册服务类
 */
export class PendingRegistrationService {
  
  /**
   * 创建临时注册记录
   */
  async createPendingRegistration(data: PendingRegistrationData): Promise<RegistrationResult> {
    const startTime = Date.now();
    const pendingId = randomUUID();
    
    try {
      // 1. 验证邀请码（不记录日志，因为 pending_registration 记录还不存在）
      const inviteCodeValidation = await inviteCodeService.verifyCode(data.inviteCode);
      
      if (!inviteCodeValidation.valid) {
        return {
          success: false,
          error: `邀请码无效: ${inviteCodeValidation.error}`,
        };
      }
      
      // 2. 检查邮箱是否已存在（在正式用户表和临时注册表中）
      const [existingUser, existingPending] = await Promise.all([
        db.select().from(user).where(eq(user.email, data.email)).limit(1),
        db.select().from(pendingRegistration).where(
          and(
            eq(pendingRegistration.email, data.email),
            eq(pendingRegistration.status, 'pending')
          )
        ).limit(1)
      ]);
      
      if (existingUser.length > 0) {
        return {
          success: false,
          error: "该邮箱已注册",
        };
      }
      
      // 如果存在待验证的记录，清理它
      if (existingPending.length > 0) {
        await db.delete(pendingRegistration)
          .where(eq(pendingRegistration.id, existingPending[0]!.id));
      }
      
      // 3. 生成组织相关信息
      const orgId = randomUUID();
      const orgSlug = this.generateOrgSlug(data.companyName);
      
      // 4. 使用 Better Auth 创建验证令牌（但不创建用户）
      let verificationToken: string | undefined;
      let verificationSent = false;
      let verificationError: string | undefined;
      
      try {
        // 创建 Better Auth 验证记录
        const verifyResult = await auth.api.sendVerificationEmail({
          body: {
            email: data.email,
            callbackURL: `${process.env.BETTER_AUTH_URL}/api/auth/verify-pending-registration`,
          },
        });
        
        // 提取验证令牌（需要从 Better Auth 的内部状态获取）
        // 注意：这里需要根据 Better Auth 的实际实现调整
        verificationToken = this.extractVerificationToken(verifyResult);
        verificationSent = true;
        
      } catch (emailError) {
        console.warn("Better Auth发送验证邮件失败，使用自定义邮件服务:", emailError);
        verificationError = emailError instanceof Error ? emailError.message : String(emailError);
      }

      // 6. 使用自定义邮件服务发送验证邮件（确保链接指向前端）
      try {
        const { emailService } = await import('../services/email-service.js');
        const customEmailResult = await emailService.sendVerificationEmail({
          userEmail: data.email,
          userName: data.name,
          verificationUrl: `${process.env.FRONTEND_URL || "http://localhost:3000"}/verify-email?token=${verificationToken}`,
          inviteCode: data.inviteCode,
        });

        if (customEmailResult.success) {
          verificationSent = true;
          verificationError = undefined; // 清除之前的错误
        } else {
          throw new Error(customEmailResult.error || "自定义邮件服务发送失败");
        }
      } catch (customEmailError) {
        console.warn("自定义邮件服务也发送失败:", customEmailError);
        if (!verificationError) { // 如果之前Better Auth成功了，这里失败就记录这个错误
          verificationError = customEmailError instanceof Error ? customEmailError.message : String(customEmailError);
        }
      }
      
      // 7. 创建临时注册记录
      const pendingData = {
        id: pendingId,
        email: data.email,
        password: data.password, // 应该已经是加密后的密码
        name: data.name,
        companyName: data.companyName,
        language: data.language || 'chinese',
        inviteCode: data.inviteCode,
        inviteCodeVerified: true,
        verificationToken,
        verificationSent,
        status: 'pending' as const,
        registrationStep: 'email_verification' as const,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        pendingOrganizationId: orgId,
        pendingOrganizationSlug: orgSlug,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
        metadata: JSON.stringify({
          registrationSource: 'invite_code',
          inviteCodeValidation: inviteCodeValidation,
          emailSendAttempts: verificationSent ? 1 : 0,
          verificationError,
        }),
      };
      
      // 7. 创建临时注册记录
      await db.insert(pendingRegistration).values(pendingData);
      
      // 8. 现在可以安全地记录验证日志了（pending_registration 记录已存在）
      try {
        // 记录邀请码验证成功
        await this.logVerificationAttempt(pendingId, 'invite_code_verify', 'success', {
          processingTime: Date.now() - startTime
        });
        
        // 记录邮件发送结果
        if (verificationSent) {
          await this.logVerificationAttempt(pendingId, 'email_send', 'success', {
            method: 'custom_email_service',
            processingTime: Date.now() - startTime
          });
        } else if (verificationError) {
          await this.logVerificationAttempt(pendingId, 'email_send', 'failed', {
            error: verificationError,
            method: 'custom_email_service',
            processingTime: Date.now() - startTime
          });
        }
      } catch (logError) {
        console.warn("记录验证日志失败（但不影响注册流程）:", logError);
      }
      
      return {
        success: true,
        pendingId,
        verificationSent,
        nextSteps: {
          requireEmailVerification: true,
          verificationEmailSent: verificationSent,
          instructions: verificationSent 
            ? "请检查您的邮箱并点击验证链接完成注册"
            : "邮件发送失败，请联系管理员或稍后重试",
          canResendEmail: true,
          ...(verificationError ? { troubleshooting: `邮件发送问题: ${verificationError}` } : {}),
        },
      };
      
    } catch (error) {
      console.error("创建临时注册记录失败:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "创建注册记录失败",
      };
    }
  }
  
  /**
   * 检测密码是否已加密（bcrypt格式）
   */
  private isPasswordHashed(password: string): boolean {
    // 检测bcrypt格式: $2a$, $2b$, $2x$, $2y$
    return /^\$2[abxy]\$\d{2}\$/.test(password);
  }

  /**
   * 处理已加密密码的记录（需要重置流程）
   */
  private async handleEncryptedPasswordRecord(record: any): Promise<VerificationResult> {
    console.warn('[PendingRegistration] 检测到已加密密码记录:', {
      email: record.email,
      recordId: record.id
    });
    
    // 更新记录状态为需要密码重置
    await db.update(pendingRegistration)
      .set({
        status: 'password_reset_required',
        registrationStep: 'password_migration',
        updatedAt: new Date(),
        metadata: JSON.stringify({
          needsPasswordReset: true,
          reason: 'bcrypt_migration',
          originalError: 'encrypted_password_detected'
        })
      })
      .where(eq(pendingRegistration.id, record.id));
    
    return {
      success: false,
      error: '检测到旧的密码格式，请重新注册或联系客服重置密码'
    };
  }

  /**
   * 验证邮箱并完成注册
   */
  async verifyEmailAndCompleteRegistration(token: string): Promise<VerificationResult> {
    const startTime = Date.now();
    
    try {
      // 1. 查找对应的临时注册记录
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(
          and(
            eq(pendingRegistration.verificationToken, token),
            eq(pendingRegistration.status, 'pending')
          )
        )
        .limit(1);
      
      if (pendingRecords.length === 0) {
        return {
          success: false,
          error: "验证令牌无效或已过期",
        };
      }
      
      const pendingRecord = pendingRecords[0]!;
      
      // 2. 检查密码格式（关键修复）
      if (this.isPasswordHashed(pendingRecord.password)) {
        console.error('[PendingRegistration] 检测到已加密密码，无法传递给Better Auth:', {
          email: pendingRecord.email,
          passwordPrefix: pendingRecord.password.substring(0, 10) + '...'
        });
        
        return await this.handleEncryptedPasswordRecord(pendingRecord);
      }
      
      // 3. 检查是否过期
      if (new Date() > pendingRecord.expiresAt) {
        await db.update(pendingRegistration)
          .set({ status: 'expired', updatedAt: new Date() })
          .where(eq(pendingRegistration.id, pendingRecord.id));
          
        return {
          success: false,
          error: "验证链接已过期，请重新注册",
        };
      }
      
      // 3. 跳过 Better Auth 验证（我们使用自定义token）
      // Better Auth 验证不适用于我们的自定义临时注册流程
      
      // 3. 完成用户创建和组织设置
      const result = await db.transaction(async (tx) => {
        // 3a. 创建正式用户
        console.log('[PendingRegistration] 准备调用 Better Auth signUpEmail:', {
          email: pendingRecord.email,
          name: pendingRecord.name,
          passwordFormat: this.isPasswordHashed(pendingRecord.password) ? 'hashed' : 'plaintext',
          passwordPrefix: pendingRecord.password.substring(0, 10) + '...'
        });
        
        const authResponse = await auth.api.signUpEmail({
          body: {
            email: pendingRecord.email,
            password: pendingRecord.password,
            name: pendingRecord.name,
          },
        });
        
        if (!authResponse?.user?.id) {
          throw new Error("用户创建失败");
        }
        
        const userId = authResponse.user.id;
        const now = new Date();
        
        // 3b. 更新用户扩展信息
        await tx.update(user)
          .set({
            companyName: pendingRecord.companyName,
            language: pendingRecord.language,
            emailVerified: true, // 已验证
            updatedAt: now,
          })
          .where(eq(user.id, userId));
        
        // 3c. 创建组织
        await tx.insert(organization).values({
          id: pendingRecord.pendingOrganizationId!,
          name: pendingRecord.companyName,
          slug: pendingRecord.pendingOrganizationSlug!,
          createdAt: now,
        });
        
        // 3d. 创建成员关系
        await tx.insert(member).values({
          id: randomUUID(),
          organizationId: pendingRecord.pendingOrganizationId!,
          userId: userId,
          role: "owner",
          createdAt: now,
        });
        
        // 3e. 创建默认订阅
        await tx.insert(subscription).values({
          id: randomUUID(),
          organizationId: pendingRecord.pendingOrganizationId!,
          plan: "basic",
          memberLimit: 5,
          createdAt: now,
          updatedAt: now,
        });
        
        // 3f. 更新临时注册状态
        await tx.update(pendingRegistration)
          .set({
            status: 'verified',
            registrationStep: 'completed',
            verifiedAt: now,
            updatedAt: now,
          })
          .where(eq(pendingRegistration.id, pendingRecord.id));
        
        return {
          userId,
          organizationId: pendingRecord.pendingOrganizationId!,
        };
      });
      
      // 4. 记录成功验证
      await this.logVerificationAttempt(pendingRecord.id, 'email_verify', 'success', {
        userId: result.userId,
        organizationId: result.organizationId,
        processingTime: Date.now() - startTime
      });
      
      // 5. 异步更新邀请码状态
      if (pendingRecord.inviteCode !== "TEST2025") {
        setImmediate(async () => {
          try {
            await inviteCodeService.updateStatus(pendingRecord.inviteCode);
          } catch (error) {
            console.warn("更新邀请码状态失败:", error);
          }
        });
      }
      
      return {
        success: true,
        userId: result.userId,
        organizationId: result.organizationId,
      };
      
    } catch (error) {
      console.error("邮箱验证和注册完成失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证过程失败",
      };
    }
  }
  
  /**
   * 重发验证邮件
   */
  async resendVerificationEmail(email: string): Promise<RegistrationResult> {
    try {
      // 查找待验证的记录
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(
          and(
            eq(pendingRegistration.email, email),
            eq(pendingRegistration.status, 'pending')
          )
        )
        .limit(1);
      
      if (pendingRecords.length === 0) {
        return {
          success: false,
          error: "未找到待验证的注册记录",
        };
      }
      
      const pendingRecord = pendingRecords[0]!;
      
      // 检查频率限制
      if (pendingRecord.lastVerificationSentAt) {
        const timeSinceLastSend = Date.now() - new Date(pendingRecord.lastVerificationSentAt).getTime();
        if (timeSinceLastSend < 2 * 60 * 1000) { // 2分钟内不能重发
          return {
            success: false,
            error: "请稍后再重发验证邮件",
          };
        }
      }
      
      // 重新发送验证邮件
      const emailResult = await emailService.sendVerificationEmail({
        userEmail: email,
        userName: pendingRecord.name,
        verificationUrl: `${process.env.FRONTEND_URL || "http://localhost:3000"}/verify-email?token=${pendingRecord.verificationToken}`,
        inviteCode: pendingRecord.inviteCode,
      });
      
      // 更新发送状态
      await db.update(pendingRegistration)
        .set({
          verificationAttempts: pendingRecord.verificationAttempts + 1,
          lastVerificationSentAt: new Date(),
          verificationSent: emailResult.success,
          updatedAt: new Date(),
        })
        .where(eq(pendingRegistration.id, pendingRecord.id));
      
      // 记录重发日志
      await this.logVerificationAttempt(pendingRecord.id, 'email_send', 
        emailResult.success ? 'success' : 'failed', {
          attemptNumber: pendingRecord.verificationAttempts + 1,
          error: emailResult.error,
        });
      
      return {
        success: emailResult.success,
        pendingId: pendingRecord.id,
        verificationSent: emailResult.success,
        error: emailResult.error,
        nextSteps: {
          requireEmailVerification: true,
          verificationEmailSent: emailResult.success,
          instructions: emailResult.success 
            ? "验证邮件已重新发送，请检查您的邮箱"
            : "重发失败，请稍后重试",
          canResendEmail: true,
        },
      };
      
    } catch (error) {
      console.error("重发验证邮件失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "重发失败",
      };
    }
  }
  
  /**
   * 清理过期的临时注册记录
   */
  async cleanupExpiredRegistrations(): Promise<number> {
    try {
      const result = await db
        .delete(pendingRegistration)
        .where(
          and(
            lt(pendingRegistration.expiresAt, new Date()),
            eq(pendingRegistration.status, 'pending')
          )
        );
      
      console.log(`清理了 ${result.length || 0} 条过期的临时注册记录`);
      return result.length || 0;
    } catch (error) {
      console.error("清理过期记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 获取注册统计信息
   */
  async getRegistrationStats(): Promise<any> {
    try {
      const stats = await db.execute(sql`
        SELECT 
          status,
          registration_step,
          COUNT(*) as count,
          AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/60) as avg_duration_minutes
        FROM auth.pending_registration 
        WHERE created_at >= NOW() - INTERVAL '24 hours'
        GROUP BY status, registration_step
      `);
      
      return (stats as any).rows;
    } catch (error) {
      console.error("获取注册统计失败:", error);
      return [];
    }
  }
  
  // 私有辅助方法
  
  /**
   * 记录验证尝试日志
   */
  private async logVerificationAttempt(
    pendingId: string,
    type: 'email_send' | 'email_verify' | 'invite_code_verify',
    status: 'success' | 'failed' | 'pending',
    details?: any
  ): Promise<void> {
    try {
      await db.insert(registrationVerificationLog).values({
        id: randomUUID(),
        pendingRegistrationId: pendingId,
        verificationType: type,
        verificationStatus: status,
        attemptNumber: details?.attemptNumber || 1,
        resultCode: details?.resultCode,
        resultMessage: details?.error || details?.message,
        ipAddress: details?.ipAddress,
        userAgent: details?.userAgent,
        processingTimeMs: details?.processingTime,
        createdAt: new Date(),
        metadata: JSON.stringify(details || {}),
      });
    } catch (error) {
      console.error("记录验证日志失败:", error);
    }
  }
  
  /**
   * 生成组织 slug
   */
  private generateOrgSlug(name: string): string {
    return (
      name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "") // 移除特殊字符
        .replace(/\s+/g, "-") // 空格替换为连字符
        .replace(/-+/g, "-") // 多个连字符合并为一个
        .trim()
        .substring(0, 50) + // 限制长度
      "-" +
      Math.random().toString(36).substring(2, 10)
    ); // 添加随机后缀确保唯一性
  }
  
  /**
   * 从 Better Auth 结果中提取验证令牌
   * 注意：这个方法需要根据 Better Auth 的实际实现调整
   */
  private extractVerificationToken(verifyResult: any): string | undefined {
    // 这里需要根据 Better Auth 的实际响应结构来实现
    // 可能需要从数据库的 verification 表中查询最新的令牌
    return verifyResult?.token || randomUUID();
  }
}

// 创建单例实例
export const pendingRegistrationService = new PendingRegistrationService();