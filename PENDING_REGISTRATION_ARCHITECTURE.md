# 临时注册表架构方案

## 概述

本文档描述了用于重构邮箱注册验证逻辑的 `pending_registration` 表架构方案。该方案解决了当前注册流程中用户在邮箱未验证时就被创建的问题，提供了更好的数据一致性和用户体验。

## 问题分析

### 当前架构问题

1. **数据不一致**：用户在邮箱验证前就被创建到 `user` 表
2. **验证失败处理**：邮箱验证失败时，用户记录已存在但不可用
3. **状态管理缺失**：缺乏注册过程的中间状态跟踪
4. **回滚困难**：注册失败时很难清理已创建的相关数据

### 解决方案

引入临时注册表系统，将注册过程分为两个阶段：
1. **临时注册阶段**：验证邀请码，创建临时记录，发送验证邮件
2. **正式注册阶段**：验证邮箱后，创建正式用户和组织记录

## 数据库架构设计

### 1. 核心表结构

#### `pending_registration` 表

```sql
CREATE TABLE auth.pending_registration (
    -- 主键
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- 用户基本信息
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,  -- 加密后的密码
    name TEXT NOT NULL,
    company_name TEXT NOT NULL,
    language TEXT DEFAULT 'chinese' NOT NULL,
    
    -- 邀请码信息
    invite_code TEXT NOT NULL,
    invite_code_verified BOOLEAN DEFAULT false NOT NULL,
    
    -- 验证状态
    verification_token TEXT UNIQUE,
    verification_sent BOOLEAN DEFAULT false NOT NULL,
    verification_attempts INTEGER DEFAULT 0 NOT NULL,
    last_verification_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- 注册状态管理
    status TEXT DEFAULT 'pending' NOT NULL, -- pending, verified, expired, failed
    registration_step TEXT DEFAULT 'email_verification' NOT NULL,
    
    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours') NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- 审计信息
    ip_address TEXT,
    user_agent TEXT,
    metadata TEXT, -- JSON格式
    
    -- 预创建组织信息
    pending_organization_id TEXT,
    pending_organization_slug TEXT
);
```

#### `registration_verification_log` 表

```sql
CREATE TABLE auth.registration_verification_log (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    pending_registration_id TEXT NOT NULL,
    verification_type TEXT NOT NULL, -- email_send, email_verify, invite_code_verify
    verification_status TEXT NOT NULL, -- success, failed, pending
    attempt_number INTEGER DEFAULT 1 NOT NULL,
    result_code TEXT,
    result_message TEXT,
    ip_address TEXT,
    user_agent TEXT,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    metadata TEXT,
    
    FOREIGN KEY (pending_registration_id) 
        REFERENCES auth.pending_registration(id) ON DELETE CASCADE
);
```

### 2. 索引优化

```sql
-- 核心查询索引
CREATE INDEX idx_pending_registration_email ON auth.pending_registration(email);
CREATE INDEX idx_pending_registration_status ON auth.pending_registration(status);
CREATE INDEX idx_pending_registration_expires_at ON auth.pending_registration(expires_at);
CREATE INDEX idx_pending_registration_verification_token ON auth.pending_registration(verification_token);

-- 复合索引
CREATE INDEX idx_pending_registration_status_expires ON auth.pending_registration(status, expires_at);
CREATE INDEX idx_pending_registration_email_status ON auth.pending_registration(email, status);
```

### 3. 自动化功能

#### 触发器
- 自动更新 `updated_at` 时间戳
- 自动设置过期时间

#### 清理函数
```sql
CREATE FUNCTION auth.cleanup_expired_pending_registrations() RETURNS INTEGER;
```

#### 统计视图
- `pending_registration_stats`：注册状态统计
- `registration_verification_stats`：验证成功率统计

## 服务层架构

### 1. PendingRegistrationService

核心服务类，提供以下功能：

#### 主要方法
- `createPendingRegistration()`: 创建临时注册记录
- `verifyEmailAndCompleteRegistration()`: 验证邮箱并完成注册
- `resendVerificationEmail()`: 重发验证邮件
- `cleanupExpiredRegistrations()`: 清理过期记录
- `getRegistrationStats()`: 获取统计信息

#### 关键特性
- **事务安全**：使用数据库事务确保数据一致性
- **错误处理**：详细的错误分类和处理
- **日志记录**：完整的验证过程日志
- **频率限制**：防止邮件重发滥用

### 2. 新的API端点

#### 注册流程端点
- `POST /api/auth/register-pending`: 创建临时注册
- `GET /api/auth/verify-pending-registration`: 完成邮箱验证
- `POST /api/auth/resend-pending-verification`: 重发验证邮件

#### 管理端点
- `POST /api/auth/check-pending-registration`: 查询注册状态
- `GET /api/auth/admin/pending-registration-stats`: 管理员统计

## 注册流程设计

### 1. 临时注册阶段

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant DB as 数据库
    participant Email as 邮件服务
    participant InviteAPI as 邀请码服务

    U->>API: POST /api/auth/register-pending
    API->>InviteAPI: 验证邀请码
    InviteAPI-->>API: 验证结果
    API->>DB: 检查邮箱是否存在
    API->>DB: 创建pending_registration记录
    API->>Email: 发送验证邮件
    Email-->>API: 发送结果
    API->>DB: 记录验证日志
    API-->>U: 返回注册结果
```

### 2. 邮箱验证阶段

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant DB as 数据库
    participant Auth as Better Auth

    U->>API: GET /api/auth/verify-pending-registration?token=xxx
    API->>DB: 查找pending_registration记录
    API->>Auth: 验证邮箱令牌
    Auth-->>API: 验证结果
    API->>DB: 开始事务
    API->>Auth: 创建正式用户
    API->>DB: 更新用户信息
    API->>DB: 创建组织和成员关系
    API->>DB: 创建默认订阅
    API->>DB: 更新pending状态为verified
    API->>DB: 提交事务
    API-->>U: 返回成功页面
```

## 性能优化

### 1. 数据库优化
- **索引策略**：为常用查询创建复合索引
- **分区策略**：按时间分区历史数据
- **清理策略**：定期清理过期记录

### 2. 缓存策略
- **验证状态缓存**：缓存频繁查询的验证状态
- **邮件发送限制**：使用 Redis 实现频率限制

### 3. 监控指标
- 注册成功率
- 验证邮件发送成功率
- 平均验证完成时间
- 过期记录清理效果

## 安全考虑

### 1. 数据保护
- **密码加密**：使用 bcrypt 加密存储
- **敏感信息脱敏**：日志中隐藏敏感信息
- **数据过期**：自动清理过期数据

### 2. 防滥用机制
- **频率限制**：限制邮件重发频率
- **IP追踪**：记录注册来源IP
- **异常监控**：监控异常注册行为

### 3. 审计跟踪
- **完整日志**：记录所有验证步骤
- **状态追踪**：跟踪注册状态变化
- **错误记录**：详细记录失败原因

## 部署和迁移

### 1. 迁移步骤

1. **运行数据库迁移**
   ```bash
   ./scripts/run-pending-registration-migration.sh
   ```

2. **验证迁移结果**
   - 检查表结构
   - 验证索引创建
   - 测试函数功能

3. **更新应用代码**
   - 部署新的服务代码
   - 更新路由配置
   - 配置监控

### 2. 回滚方案

如果需要回滚到原来的架构：
1. 停用新的注册端点
2. 迁移 pending_registration 中的verified记录到user表
3. 删除临时注册表
4. 恢复原有注册逻辑

### 3. 监控和维护

- **定期清理**：设置定时任务清理过期记录
- **性能监控**：监控查询性能和响应时间
- **容量规划**：根据注册量规划存储容量

## 测试策略

### 1. 单元测试
- 服务方法测试
- 数据验证测试
- 错误处理测试

### 2. 集成测试
- 完整注册流程测试
- 邮件发送集成测试
- 数据库事务测试

### 3. 性能测试
- 并发注册测试
- 大量数据查询测试
- 清理操作性能测试

## 最佳实践

### 1. 开发建议
- 使用类型安全的数据库操作
- 实现详细的错误处理
- 保持代码的可测试性

### 2. 运维建议
- 定期监控数据库性能
- 设置告警规则
- 备份重要配置和数据

### 3. 扩展建议
- 支持多种验证方式
- 添加更多统计维度
- 优化邮件模板系统

## 总结

临时注册表架构方案通过引入中间状态管理，有效解决了原有注册流程的数据一致性问题。该方案具有以下优势：

1. **数据一致性**：避免未验证用户的创建
2. **状态可控**：完整的注册过程状态管理
3. **错误恢复**：更好的错误处理和恢复机制
4. **监控完善**：详细的日志和统计信息
5. **扩展性强**：易于添加新的验证步骤和功能

该架构为后续的用户管理功能提供了坚实的基础，同时保持了系统的可维护性和可扩展性。