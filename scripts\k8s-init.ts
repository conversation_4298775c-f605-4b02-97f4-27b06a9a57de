#!/usr/bin/env tsx
/**
 * 统一的K8s环境初始化脚本
 * 整合数据库初始化、健康检查、清理服务部署等功能
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import * as pkg from 'pg';

const { Client } = pkg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const PROJECT_ROOT = dirname(__dirname);

// 配置接口
interface InitConfig {
  checkDatabase: boolean;
  runMigrations: boolean;
  deployCleanup: boolean;
  healthCheck: boolean;
  verbose: boolean;
  skipConfirmation: boolean;
}

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
} as const;

type ColorKey = keyof typeof colors;

function log(message: string, color: ColorKey = 'reset'): void {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

/**
 * 解析命令行参数
 */
function parseArgs(): InitConfig {
  const args = process.argv.slice(2);
  const config: InitConfig = {
    checkDatabase: true,
    runMigrations: true,
    deployCleanup: false,
    healthCheck: true,
    verbose: false,
    skipConfirmation: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--skip-db':
        config.checkDatabase = false;
        break;
      case '--skip-migrations':
        config.runMigrations = false;
        break;
      case '--deploy-cleanup':
        config.deployCleanup = true;
        break;
      case '--skip-health':
        config.healthCheck = false;
        break;
      case '--verbose':
      case '-v':
        config.verbose = true;
        break;
      case '--yes':
      case '-y':
        config.skipConfirmation = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      default:
        if (arg.startsWith('-')) {
          log(`未知参数: ${arg}`, 'red');
          showHelp();
          process.exit(1);
        }
    }
  }

  return config;
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
specific-ai-auth K8s 环境初始化脚本

用法: tsx scripts/k8s-init.ts [选项]

选项:
  --skip-db              跳过数据库连接检查
  --skip-migrations      跳过数据库迁移
  --deploy-cleanup       部署数据清理系统
  --skip-health          跳过健康检查
  --verbose, -v          详细输出
  --yes, -y              跳过确认提示
  --help, -h             显示此帮助信息

环境变量:
  DATABASE_URL           数据库连接字符串
  BETTER_AUTH_SECRET     认证密钥
  BETTER_AUTH_URL        服务基础URL
  K8S_NAMESPACE          K8s命名空间 (默认: ovs)

示例:
  # 完整初始化
  tsx scripts/k8s-init.ts

  # 仅数据库迁移
  tsx scripts/k8s-init.ts --skip-health

  # 部署包含清理系统
  tsx scripts/k8s-init.ts --deploy-cleanup

  # 详细输出
  tsx scripts/k8s-init.ts --verbose
`);
}

/**
 * 检查环境变量
 */
function checkEnvironment(): boolean {
  log('检查环境配置...', 'blue');
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log(`缺少必要的环境变量: ${missingVars.join(', ')}`, 'red');
    return false;
  }
  
  log('环境配置检查通过', 'green');
  return true;
}

/**
 * 检查必要文件
 */
function checkRequiredFiles(): boolean {
  log('检查必要文件...', 'blue');
  
  const requiredFiles = [
    'drizzle.config.ts',
    'src/lib/auth-schema.ts',
    'package.json',
    'scripts/init-database.sql'
  ];
  
  const missingFiles = requiredFiles.filter(file => 
    !existsSync(join(PROJECT_ROOT, file))
  );
  
  if (missingFiles.length > 0) {
    log(`缺少必要文件: ${missingFiles.join(', ')}`, 'red');
    return false;
  }
  
  log('必要文件检查通过', 'green');
  return true;
}

/**
 * 测试数据库连接
 */
async function testDatabaseConnection(): Promise<boolean> {
  log('测试数据库连接...', 'blue');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });
  
  try {
    await client.connect();
    await client.query('SELECT 1');
    await client.end();
    
    log('数据库连接正常', 'green');
    return true;
  } catch (error) {
    log(`数据库连接失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    return false;
  }
}

/**
 * 检查数据库表结构
 */
async function checkDatabaseSchema(): Promise<{
  schemaExists: boolean;
  tablesCount: number;
  missingTables: string[];
}> {
  log('检查数据库表结构...', 'blue');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });
  
  try {
    await client.connect();
    
    // 检查auth schema
    const schemaResult = await client.query(
      "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'auth'"
    );
    const schemaExists = schemaResult.rows.length > 0;
    
    if (!schemaExists) {
      await client.end();
      return { schemaExists: false, tablesCount: 0, missingTables: [] };
    }
    
    // 检查关键表
    const requiredTables = [
      'user', 'session', 'account', 'verification',
      'organization', 'member', 'invitation', 'subscription',
      'email_log', 'rate_limit', 'request_log', 'security_event'
    ];
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'auth' 
      AND table_type = 'BASE TABLE'
    `);
    
    const existingTables = tablesResult.rows.map(row => row.table_name);
    const missingTables = requiredTables.filter(table => 
      !existingTables.includes(table)
    );
    
    await client.end();
    
    log(`找到 ${existingTables.length} 个数据库表`, 'cyan');
    
    return {
      schemaExists: true,
      tablesCount: existingTables.length,
      missingTables
    };
    
  } catch (error) {
    log(`检查数据库结构失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    return { schemaExists: false, tablesCount: 0, missingTables: [] };
  }
}

/**
 * 运行数据库迁移
 */
async function runDatabaseMigrations(): Promise<boolean> {
  log('开始数据库迁移...', 'blue');
  
  try {
    process.chdir(PROJECT_ROOT);
    
    // 安装依赖
    if (!existsSync(join(PROJECT_ROOT, 'node_modules'))) {
      log('安装项目依赖...', 'cyan');
      execSync('pnpm install --frozen-lockfile', {
        stdio: 'inherit',
        timeout: 300000
      });
    }
    
    // 生成迁移文件
    log('生成迁移文件...', 'cyan');
    execSync('pnpm db:generate', {
      stdio: 'inherit',
      timeout: 60000
    });
    
    // 执行迁移
    log('执行数据库迁移...', 'cyan');
    execSync('pnpm db:migrate', {
      stdio: 'inherit',
      timeout: 60000
    });
    
    log('数据库迁移完成', 'green');
    return true;
    
  } catch (error) {
    log(`数据库迁移失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    return false;
  }
}

/**
 * 执行健康检查
 */
async function performHealthCheck(): Promise<boolean> {
  log('执行系统健康检查...', 'blue');
  
  try {
    // 检查数据库连接
    const dbHealthy = await testDatabaseConnection();
    
    // 检查数据库结构
    const schemaInfo = await checkDatabaseSchema();
    
    console.log('\n=== 健康检查报告 ===');
    console.log(`数据库连接: ${dbHealthy ? '✅ 正常' : '❌ 异常'}`);
    console.log(`数据库结构: ${schemaInfo.schemaExists ? '✅ 存在' : '❌ 缺失'}`);
    console.log(`数据库表数量: ${schemaInfo.tablesCount}`);
    
    if (schemaInfo.missingTables.length > 0) {
      console.log(`缺失的表: ${schemaInfo.missingTables.join(', ')}`);
    }
    
    const healthy = dbHealthy && schemaInfo.schemaExists && schemaInfo.missingTables.length === 0;
    
    log(`系统健康状态: ${healthy ? '✅ 健康' : '❌ 需要修复'}`, healthy ? 'green' : 'red');
    
    return healthy;
    
  } catch (error) {
    log(`健康检查失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    return false;
  }
}

/**
 * 部署清理系统
 */
async function deployCleanupSystem(): Promise<boolean> {
  log('部署数据清理系统...', 'blue');
  
  try {
    const deployScript = join(__dirname, 'deploy-cleanup-system.sh');
    
    if (!existsSync(deployScript)) {
      log('清理系统部署脚本不存在', 'red');
      return false;
    }
    
    execSync(`bash ${deployScript} deploy`, {
      stdio: 'inherit',
      timeout: 300000
    });
    
    log('数据清理系统部署完成', 'green');
    return true;
    
  } catch (error) {
    log(`部署清理系统失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    return false;
  }
}

/**
 * 确认用户操作
 */
async function confirmAction(message: string): Promise<boolean> {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    rl.question(`${message} (y/N): `, (answer: string) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const config = parseArgs();
  
  log('K8s环境初始化开始', 'magenta');
  log('='.repeat(50), 'magenta');
  
  if (config.verbose) {
    console.log('配置:', JSON.stringify(config, null, 2));
  }
  
  try {
    // 1. 环境检查
    if (!checkEnvironment()) {
      log('环境检查失败，请设置必要的环境变量', 'red');
      process.exit(1);
    }
    
    // 2. 文件检查
    if (!checkRequiredFiles()) {
      log('文件检查失败，请确保项目完整', 'red');
      process.exit(1);
    }
    
    // 3. 数据库检查和迁移
    if (config.checkDatabase) {
      const dbHealthy = await testDatabaseConnection();
      if (!dbHealthy) {
        log('数据库连接失败，请检查配置', 'red');
        process.exit(1);
      }
      
      if (config.runMigrations) {
        const schemaInfo = await checkDatabaseSchema();
        
        if (!schemaInfo.schemaExists || schemaInfo.missingTables.length > 0) {
          log('检测到数据库结构不完整，需要运行迁移', 'yellow');
          
          if (!config.skipConfirmation) {
            const confirmed = await confirmAction('是否运行数据库迁移？');
            if (!confirmed) {
              log('用户取消操作', 'yellow');
              process.exit(0);
            }
          }
          
          const migrationSuccess = await runDatabaseMigrations();
          if (!migrationSuccess) {
            log('数据库迁移失败', 'red');
            process.exit(1);
          }
        } else {
          log('数据库结构完整，跳过迁移', 'green');
        }
      }
    }
    
    // 4. 健康检查
    if (config.healthCheck) {
      const healthy = await performHealthCheck();
      if (!healthy) {
        log('健康检查未通过，请检查系统状态', 'yellow');
      }
    }
    
    // 5. 部署清理系统
    if (config.deployCleanup) {
      if (!config.skipConfirmation) {
        const confirmed = await confirmAction('是否部署数据清理系统？');
        if (!confirmed) {
          log('跳过清理系统部署', 'yellow');
        } else {
          await deployCleanupSystem();
        }
      } else {
        await deployCleanupSystem();
      }
    }
    
    log('K8s环境初始化完成', 'magenta');
    console.log('\n✅ 系统准备就绪，可以启动应用！');
    
  } catch (error) {
    log(`初始化过程中发生错误: ${error instanceof Error ? error.message : String(error)}`, 'red');
    process.exit(1);
  }
}

// 设置进程退出处理
process.on('SIGINT', () => {
  log('初始化被中断', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('初始化被终止', 'yellow');
  process.exit(1);
});

// 处理未捕获的异常
process.on('unhandledRejection', (reason) => {
  log(`未处理的Promise拒绝: ${reason}`, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`未捕获的异常: ${error.message}`, 'red');
  process.exit(1);
});

// 运行主函数
// 检查是否直接运行此脚本
if (typeof import.meta !== 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    log(`执行失败: ${error instanceof Error ? error.message : String(error)}`, 'red');
    process.exit(1);
  });
}

export { main as initializeK8sEnvironment };