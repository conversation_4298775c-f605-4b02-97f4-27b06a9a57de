/**
 * 国际化服务
 * 提供多语言支持的核心服务
 */

import { readFileSync } from 'fs';
import { join } from 'path';

// 支持的语言类型
export type SupportedLanguage = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR';

// 翻译键的类型定义
export interface TranslationKeys {
  // 通用消息
  common: {
    success: string;
    error: string;
    loading: string;
    confirm: string;
    cancel: string;
    save: string;
    delete: string;
    edit: string;
    create: string;
    update: string;
    search: string;
    filter: string;
    reset: string;
    submit: string;
    back: string;
    next: string;
    previous: string;
    close: string;
    open: string;
    yes: string;
    no: string;
  };

  // 认证相关
  auth: {
    // 注册
    register: {
      success: string;
      emailSent: string;
      emailSendFailed: string;
      userExists: string;
      invalidInviteCode: string;
      validationError: string;
      createFailed: string;
      instructions: {
        checkEmail: string;
        emailSendFailed: string;
        troubleshooting: string;
        alreadyVerified: string;
        canLogin: string;
      };
    };

    // 登录
    login: {
      success: string;
      failed: string;
      unauthorized: string;
      emailNotVerified: string;
      accountLocked: string;
      invalidCredentials: string;
    };

    // 邮箱验证
    emailVerification: {
      sent: string;
      resent: string;
      success: string;
      failed: string;
      expired: string;
      invalid: string;
      alreadyVerified: string;
      required: string;
      tokenMissing: string;
      tokenInvalid: string;
      sendFailed: string;
    };

    // 邀请码
    inviteCode: {
      valid: string;
      invalid: string;
      expired: string;
      used: string;
      notFound: string;
      verificationFailed: string;
    };

    // 用户管理
    user: {
      created: string;
      updated: string;
      deleted: string;
      notFound: string;
      exists: string;
      createFailed: string;
      updateFailed: string;
      deleteFailed: string;
      languageUpdated: string;
      languageUpdateFailed: string;
      insufficientPermissions: string;
    };

    // 管理员
    admin: {
      superAdminCreated: string;
      adminCreated: string;
      createFailed: string;
      invalidSecretKey: string;
      onlySuperAdminCanCreate: string;
      organizationNotFound: string;
    };

    // 会话
    session: {
      expired: string;
      invalid: string;
      required: string;
      extended: string;
      getFailed: string;
    };
  };

  // 组织相关
  organization: {
    created: string;
    updated: string;
    deleted: string;
    notFound: string;
    getFailed: string;
    memberLimitReached: string;
    subscriptionRequired: string;
  };

  // 验证错误
  validation: {
    required: string;
    email: string;
    minLength: string;
    maxLength: string;
    password: string;
    confirmPassword: string;
    invalidFormat: string;
  };

  // 错误类型
  errors: {
    internal: string;
    network: string;
    timeout: string;
    notFound: string;
    unauthorized: string;
    forbidden: string;
    badRequest: string;
    conflict: string;
    tooManyRequests: string;
    serviceUnavailable: string;
  };
}

// 翻译资源缓存
const translationCache = new Map<SupportedLanguage, TranslationKeys>();

/**
 * 国际化服务类
 */
export class I18nService {
  private static instance: I18nService;
  private defaultLanguage: SupportedLanguage = 'zh-CN';
  private fallbackLanguage: SupportedLanguage = 'en-US';

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): I18nService {
    if (!I18nService.instance) {
      I18nService.instance = new I18nService();
    }
    return I18nService.instance;
  }

  /**
   * 设置默认语言
   */
  public setDefaultLanguage(language: SupportedLanguage): void {
    this.defaultLanguage = language;
  }

  /**
   * 设置回退语言
   */
  public setFallbackLanguage(language: SupportedLanguage): void {
    this.fallbackLanguage = language;
  }

  /**
   * 检测用户语言偏好
   */
  public detectLanguage(
    userLanguage?: string,
    acceptLanguage?: string
  ): SupportedLanguage {
    // 1. 优先使用用户设置的语言
    if (userLanguage && this.isSupportedLanguage(userLanguage)) {
      return userLanguage as SupportedLanguage;
    }

    // 2. 解析 Accept-Language 头
    if (acceptLanguage) {
      const languages = this.parseAcceptLanguage(acceptLanguage);
      for (const lang of languages) {
        if (this.isSupportedLanguage(lang)) {
          return lang as SupportedLanguage;
        }
      }
    }

    // 3. 返回默认语言
    return this.defaultLanguage;
  }

  /**
   * 加载翻译资源
   */
  public loadTranslations(language: SupportedLanguage): TranslationKeys {
    // 检查缓存
    if (translationCache.has(language)) {
      return translationCache.get(language)!;
    }

    try {
      // 加载翻译文件
      const translationPath = join(process.cwd(), 'src', 'locales', `${language}.json`);
      const translationData = readFileSync(translationPath, 'utf-8');
      const translations: TranslationKeys = JSON.parse(translationData);

      // 缓存翻译资源
      translationCache.set(language, translations);
      return translations;
    } catch (error) {
      console.warn(`Failed to load translations for ${language}, falling back to ${this.fallbackLanguage}`);
      
      // 如果不是回退语言，尝试加载回退语言
      if (language !== this.fallbackLanguage) {
        return this.loadTranslations(this.fallbackLanguage);
      }

      // 如果连回退语言都加载失败，返回空的翻译对象
      throw new Error(`Failed to load translations for ${language}`);
    }
  }

  /**
   * 获取翻译文本
   */
  public translate(
    key: string,
    language: SupportedLanguage,
    params?: Record<string, string | number>
  ): string {
    try {
      const translations = this.loadTranslations(language);
      const text = this.getNestedValue(translations, key);

      if (!text) {
        console.warn(`Translation key not found: ${key} for language: ${language}`);
        return key; // 返回键名作为回退
      }

      // 替换参数
      if (params) {
        return this.interpolate(text, params);
      }

      return text;
    } catch (error) {
      console.error(`Translation error for key: ${key}, language: ${language}`, error);
      return key; // 返回键名作为回退
    }
  }

  /**
   * 获取多语言响应对象
   */
  public getMultiLanguageResponse(
    key: string,
    language: SupportedLanguage,
    params?: Record<string, string | number>
  ): { message: string; messageKey: string; language: SupportedLanguage } {
    return {
      message: this.translate(key, language, params),
      messageKey: key,
      language,
    };
  }

  /**
   * 检查是否为支持的语言
   */
  public isSupportedLanguage(language: string): boolean {
    const supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
    return supportedLanguages.includes(language as SupportedLanguage);
  }

  /**
   * 解析 Accept-Language 头
   */
  private parseAcceptLanguage(acceptLanguage: string): string[] {
    return acceptLanguage
      .split(',')
      .map(lang => {
        const [language, quality] = lang.trim().split(';q=');
        return {
          language: language?.trim() || '',
          quality: quality ? parseFloat(quality) : 1.0,
        };
      })
      .sort((a, b) => b.quality - a.quality)
      .map(item => item.language);
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): string | undefined {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 字符串插值
   */
  private interpolate(text: string, params: Record<string, string | number>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key]?.toString() || match;
    });
  }

  /**
   * 清除翻译缓存
   */
  public clearCache(): void {
    translationCache.clear();
  }

  /**
   * 预加载所有支持的语言
   */
  public preloadAllLanguages(): void {
    const supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
    supportedLanguages.forEach(lang => {
      try {
        this.loadTranslations(lang);
      } catch (error) {
        console.warn(`Failed to preload language: ${lang}`);
      }
    });
  }
}

// 导出单例实例
export const i18nService = I18nService.getInstance();
