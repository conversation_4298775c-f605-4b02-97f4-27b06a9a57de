#!/usr/bin/env tsx
/**
 * 数据清理指标导出器
 * 为 Prometheus 提供清理任务相关的指标数据
 */

import { createServer } from 'http';
import { db } from '../src/lib/drizzle.js';
import { sql } from 'drizzle-orm';
import { dataCleanupService } from '../src/services/data-cleanup.js';

// 配置接口
interface MetricsConfig {
  port: number;
  path: string;
  scrapeInterval: number;
  enableDetailedMetrics: boolean;
}

// 指标数据接口
interface MetricsData {
  // 数据库指标
  pendingRegistrationsTotal: number;
  expiredRegistrationsTotal: number;
  completedRegistrationsTotal: number;
  failedRegistrationsTotal: number;
  verificationLogsTotal: number;
  emailLogsTotal: number;
  rateLimiterRecordsTotal: number;
  
  // 清理任务指标
  lastCleanupTimestamp: number;
  totalCleanupsRun: number;
  totalRecordsCleaned: number;
  averageCleanupTimeMs: number;
  cleanupSuccessRate: number;
  recentErrorsCount: number;
  
  // 系统健康指标
  databaseHealthy: boolean;
  cleanupServiceHealthy: boolean;
  lastHealthCheckTimestamp: number;
}

/**
 * 指标导出器类
 */
class MetricsExporter {
  private config: MetricsConfig;
  private server: import('http').Server | null = null;
  private metricsCache: MetricsData | null = null;
  private lastCacheUpdate = 0;
  
  constructor(config?: Partial<MetricsConfig>) {
    this.config = {
      port: parseInt(process.env.METRICS_PORT || '9090'),
      path: process.env.METRICS_PATH || '/metrics',
      scrapeInterval: parseInt(process.env.SCRAPE_INTERVAL || '30'),
      enableDetailedMetrics: process.env.ENABLE_DETAILED_METRICS === 'true',
      ...config,
    };
  }
  
  /**
   * 启动指标导出服务器
   */
  async start(): Promise<void> {
    this.server = createServer(async (req, res) => {
      const url = new URL(req.url || '', `http://${req.headers.host}`);
      
      // 设置 CORS 头
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
      
      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }
      
      try {
        if (url.pathname === this.config.path) {
          await this.handleMetricsRequest(req, res);
        } else if (url.pathname === '/health') {
          await this.handleHealthRequest(req, res);
        } else if (url.pathname === '/') {
          await this.handleIndexRequest(req, res);
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      } catch (error) {
        console.error('处理请求失败:', error);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
      }
    });
    
    this.server.listen(this.config.port, () => {
      console.log(`指标导出器已启动，监听端口 ${this.config.port}`);
      console.log(`指标端点: http://localhost:${this.config.port}${this.config.path}`);
      console.log(`健康检查: http://localhost:${this.config.port}/health`);
    });
    
    // 定期更新指标缓存
    setInterval(() => {
      this.updateMetricsCache().catch(error => {
        console.error('更新指标缓存失败:', error);
      });
    }, this.config.scrapeInterval * 1000);
    
    // 首次更新缓存
    await this.updateMetricsCache();
  }
  
  /**
   * 停止指标导出服务器
   */
  async stop(): Promise<void> {
    if (this.server) {
      this.server.close();
      console.log('指标导出器已停止');
    }
  }
  
  /**
   * 处理指标请求
   */
  private async handleMetricsRequest(req: import('http').IncomingMessage, res: import('http').ServerResponse): Promise<void> {
    try {
      // 确保缓存是最新的
      if (Date.now() - this.lastCacheUpdate > this.config.scrapeInterval * 1000) {
        await this.updateMetricsCache();
      }
      
      const metrics = this.formatPrometheusMetrics(this.metricsCache!);
      
      res.writeHead(200, { 
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8'
      });
      res.end(metrics);
      
    } catch (error) {
      console.error('获取指标失败:', error);
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end('Error collecting metrics');
    }
  }
  
  /**
   * 处理健康检查请求
   */
  private async handleHealthRequest(req: import('http').IncomingMessage, res: import('http').ServerResponse): Promise<void> {
    try {
      const health = await this.checkHealth();
      
      res.writeHead(health.healthy ? 200 : 503, { 
        'Content-Type': 'application/json' 
      });
      res.end(JSON.stringify(health, null, 2));
      
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        healthy: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      }));
    }
  }
  
  /**
   * 处理首页请求
   */
  private async handleIndexRequest(req: import('http').IncomingMessage, res: import('http').ServerResponse): Promise<void> {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>specific-ai-auth 数据清理指标导出器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 1px solid #ccc; padding-bottom: 20px; }
        .section { margin: 20px 0; }
        .metrics-link { display: inline-block; margin: 10px 0; padding: 10px; background: #f5f5f5; }
        pre { background: #f8f8f8; padding: 15px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>specific-ai-auth 数据清理指标导出器</h1>
        <p>状态: <strong style="color: green;">运行中</strong></p>
        <p>启动时间: ${new Date().toISOString()}</p>
    </div>
    
    <div class="section">
        <h2>可用端点</h2>
        <div class="metrics-link">
            <a href="${this.config.path}">📊 ${this.config.path}</a> - Prometheus 指标
        </div>
        <div class="metrics-link">
            <a href="/health">❤️ /health</a> - 健康检查
        </div>
    </div>
    
    <div class="section">
        <h2>配置信息</h2>
        <pre>${JSON.stringify(this.config, null, 2)}</pre>
    </div>
    
    <div class="section">
        <h2>最新指标摘要</h2>
        <pre>${this.metricsCache ? JSON.stringify(this.metricsCache, null, 2) : '暂无数据'}</pre>
    </div>
</body>
</html>`;
    
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(html);
  }
  
  /**
   * 更新指标缓存
   */
  private async updateMetricsCache(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // 并行获取各种指标
      const [
        dbMetrics,
        cleanupStats,
        healthCheck,
      ] = await Promise.all([
        this.collectDatabaseMetrics(),
        dataCleanupService.getStats(),
        dataCleanupService.healthCheck(),
      ]);
      
      // 组合指标数据
      this.metricsCache = {
        // 数据库指标
        pendingRegistrationsTotal: dbMetrics.pendingRegistrations,
        expiredRegistrationsTotal: dbMetrics.expiredRegistrations,
        completedRegistrationsTotal: dbMetrics.completedRegistrations,
        failedRegistrationsTotal: dbMetrics.failedRegistrations,
        verificationLogsTotal: dbMetrics.verificationLogs,
        emailLogsTotal: dbMetrics.emailLogs,
        rateLimiterRecordsTotal: dbMetrics.rateLimiterRecords,
        
        // 清理任务指标
        lastCleanupTimestamp: cleanupStats.lastCleanupAt.getTime(),
        totalCleanupsRun: cleanupStats.totalCleanupsRun,
        totalRecordsCleaned: cleanupStats.totalRecordsCleaned,
        averageCleanupTimeMs: cleanupStats.averageProcessingTimeMs,
        cleanupSuccessRate: cleanupStats.successRate,
        recentErrorsCount: cleanupStats.recentErrors.length,
        
        // 系统健康指标
        databaseHealthy: healthCheck.healthy,
        cleanupServiceHealthy: true, // 如果能执行到这里说明服务是健康的
        lastHealthCheckTimestamp: Date.now(),
      };
      
      this.lastCacheUpdate = Date.now();
      
      const updateTime = Date.now() - startTime;
      if (updateTime > 5000) { // 超过5秒警告
        console.warn(`指标更新耗时过长: ${updateTime}ms`);
      }
      
    } catch (error) {
      console.error('更新指标缓存失败:', error);
      // 保持旧缓存，但标记服务为不健康
      if (this.metricsCache) {
        this.metricsCache.cleanupServiceHealthy = false;
        this.metricsCache.lastHealthCheckTimestamp = Date.now();
      }
    }
  }
  
  /**
   * 收集数据库指标
   */
  private async collectDatabaseMetrics(): Promise<{
    pendingRegistrations: number;
    expiredRegistrations: number;
    completedRegistrations: number;
    failedRegistrations: number;
    verificationLogs: number;
    emailLogs: number;
    rateLimiterRecords: number;
  }> {
    try {
      const result = await db.execute(sql`
        SELECT 
          (SELECT COUNT(*) FROM auth.pending_registration WHERE status = 'pending') AS pending_registrations,
          (SELECT COUNT(*) FROM auth.pending_registration WHERE status = 'pending' AND expires_at < NOW()) AS expired_registrations,
          (SELECT COUNT(*) FROM auth.pending_registration WHERE status = 'verified') AS completed_registrations,
          (SELECT COUNT(*) FROM auth.pending_registration WHERE status = 'failed') AS failed_registrations,
          (SELECT COUNT(*) FROM auth.registration_verification_log) AS verification_logs,
          (SELECT COUNT(*) FROM auth.email_log) AS email_logs,
          (SELECT COUNT(*) FROM auth.rate_limiter) AS rate_limiter_records
      `);
      
      const row = (result as any).rows[0] as Record<string, any>;
      
      return {
        pendingRegistrations: Number(row.pending_registrations || 0),
        expiredRegistrations: Number(row.expired_registrations || 0),
        completedRegistrations: Number(row.completed_registrations || 0),
        failedRegistrations: Number(row.failed_registrations || 0),
        verificationLogs: Number(row.verification_logs || 0),
        emailLogs: Number(row.email_logs || 0),
        rateLimiterRecords: Number(row.rate_limiter_records || 0),
      };
      
    } catch (error) {
      console.error('收集数据库指标失败:', error);
      return {
        pendingRegistrations: 0,
        expiredRegistrations: 0,
        completedRegistrations: 0,
        failedRegistrations: 0,
        verificationLogs: 0,
        emailLogs: 0,
        rateLimiterRecords: 0,
      };
    }
  }
  
  /**
   * 格式化 Prometheus 指标
   */
  private formatPrometheusMetrics(data: MetricsData): string {
    const timestamp = Date.now();
    const labels = 'service="specific-ai-auth",component="data-cleanup"';
    
    return `
# HELP auth_pending_registrations_total 临时注册记录总数
# TYPE auth_pending_registrations_total gauge
auth_pending_registrations_total{${labels},status="pending"} ${data.pendingRegistrationsTotal}
auth_pending_registrations_total{${labels},status="expired"} ${data.expiredRegistrationsTotal}
auth_pending_registrations_total{${labels},status="completed"} ${data.completedRegistrationsTotal}
auth_pending_registrations_total{${labels},status="failed"} ${data.failedRegistrationsTotal}

# HELP auth_verification_logs_total 验证日志记录总数
# TYPE auth_verification_logs_total gauge
auth_verification_logs_total{${labels}} ${data.verificationLogsTotal}

# HELP auth_email_logs_total 邮件日志记录总数
# TYPE auth_email_logs_total gauge
auth_email_logs_total{${labels}} ${data.emailLogsTotal}

# HELP auth_rate_limiter_records_total 频率限制记录总数
# TYPE auth_rate_limiter_records_total gauge
auth_rate_limiter_records_total{${labels}} ${data.rateLimiterRecordsTotal}

# HELP auth_cleanup_runs_total 清理任务执行总次数
# TYPE auth_cleanup_runs_total counter
auth_cleanup_runs_total{${labels}} ${data.totalCleanupsRun}

# HELP auth_cleanup_records_cleaned_total 清理的记录总数
# TYPE auth_cleanup_records_cleaned_total counter
auth_cleanup_records_cleaned_total{${labels}} ${data.totalRecordsCleaned}

# HELP auth_cleanup_duration_average_seconds 平均清理时间
# TYPE auth_cleanup_duration_average_seconds gauge
auth_cleanup_duration_average_seconds{${labels}} ${data.averageCleanupTimeMs / 1000}

# HELP auth_cleanup_success_rate 清理任务成功率
# TYPE auth_cleanup_success_rate gauge
auth_cleanup_success_rate{${labels}} ${data.cleanupSuccessRate}

# HELP auth_cleanup_errors_recent_total 最近错误数量
# TYPE auth_cleanup_errors_recent_total gauge
auth_cleanup_errors_recent_total{${labels}} ${data.recentErrorsCount}

# HELP auth_cleanup_last_run_timestamp_seconds 上次清理任务时间戳
# TYPE auth_cleanup_last_run_timestamp_seconds gauge
auth_cleanup_last_run_timestamp_seconds{${labels}} ${data.lastCleanupTimestamp / 1000}

# HELP auth_database_healthy 数据库健康状态
# TYPE auth_database_healthy gauge
auth_database_healthy{${labels}} ${data.databaseHealthy ? 1 : 0}

# HELP auth_cleanup_service_healthy 清理服务健康状态
# TYPE auth_cleanup_service_healthy gauge
auth_cleanup_service_healthy{${labels}} ${data.cleanupServiceHealthy ? 1 : 0}

# HELP auth_metrics_last_update_timestamp_seconds 指标最后更新时间戳
# TYPE auth_metrics_last_update_timestamp_seconds gauge
auth_metrics_last_update_timestamp_seconds{${labels}} ${this.lastCacheUpdate / 1000}
`.trim();
  }
  
  /**
   * 执行健康检查
   */
  private async checkHealth(): Promise<{
    healthy: boolean;
    services: Record<string, boolean>;
    metrics: MetricsData | null;
    timestamp: string;
  }> {
    const services: Record<string, boolean> = {};
    
    try {
      // 检查数据库连接
      await db.execute(sql`SELECT 1`);
      services.database = true;
    } catch {
      services.database = false;
    }
    
    try {
      // 检查清理服务
      const healthCheck = await dataCleanupService.healthCheck();
      services.cleanup = healthCheck.healthy;
    } catch {
      services.cleanup = false;
    }
    
    // 检查指标缓存
    services.metrics = this.metricsCache !== null && 
                      Date.now() - this.lastCacheUpdate < 120000; // 2分钟内更新过
    
    const healthy = Object.values(services).every(status => status);
    
    return {
      healthy,
      services,
      metrics: this.metricsCache,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('启动 specific-ai-auth 数据清理指标导出器...');
  
  const exporter = new MetricsExporter();
  
  // 处理优雅关闭
  const gracefulShutdown = async (signal: string) => {
    console.log(`收到 ${signal} 信号，正在优雅关闭...`);
    await exporter.stop();
    process.exit(0);
  };
  
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  
  // 处理未捕获的错误
  process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
  });
  
  process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
  });
  
  try {
    await exporter.start();
  } catch (error) {
    console.error('启动指标导出器失败:', error);
    process.exit(1);
  }
}

// 运行主函数
// 检查是否直接运行此脚本
if (typeof import.meta !== 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('执行失败:', error);
    process.exit(1);
  });
}

export { MetricsExporter };