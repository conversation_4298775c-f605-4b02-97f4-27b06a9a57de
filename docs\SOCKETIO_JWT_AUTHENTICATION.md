# Socket.IO JWT认证集成指南

## 🎯 问题场景

Socket.IO连接建立时，浏览器无法自动传递Cookie到WebSocket连接，但后端需要进行用户认证。JWT Token是解决这个问题的完美方案。

## 🔌 认证流程设计

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant Auth as 认证服务
    participant Socket as Socket.IO服务
    participant Backend as 后端服务

    Client->>Auth: 1. 用户登录 (Cookie Session)
    Auth-->>Client: 2. 返回登录成功
    
    Client->>Auth: 3. 创建JWT Token
    Note over Client,Auth: POST /api/auth/jwt/create<br/>(使用Cookie认证)
    Auth-->>Client: 4. 返回JWT Token
    
    Client->>Socket: 5. 建立Socket连接
    Note over Client,Socket: 传递JWT Token作为认证
    Socket->>Auth: 6. 验证JWT Token
    Auth-->>Socket: 7. 返回用户信息
    Socket-->>Client: 8. 连接建立成功
    
    Client->>Socket: 9. 发送消息
    Socket->>Backend: 10. 调用业务API (带JWT)
    Backend-->>Socket: 11. 返回业务数据
    Socket-->>Client: 12. 推送消息给客户端
```

## 🚀 实现方案

### 方案一：连接时认证（推荐）

#### 前端实现

```typescript
// frontend/socket-client.ts
import { io, Socket } from 'socket.io-client';

class SocketAuthClient {
  private socket: Socket | null = null;
  private jwtToken: string | null = null;

  // 1. 获取JWT Token
  async getJWTToken(): Promise<string> {
    const response = await fetch('/api/auth/jwt/create', {
      method: 'POST',
      credentials: 'include', // 使用Cookie Session创建JWT
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        expiresIn: '24h',
        customClaims: {
          service: 'socket-client',
          features: ['real-time-chat', 'notifications']
        }
      })
    });

    if (!response.ok) {
      throw new Error('获取JWT Token失败');
    }

    const { token } = await response.json();
    this.jwtToken = token;
    return token;
  }

  // 2. 建立Socket连接并认证
  async connect(): Promise<Socket> {
    try {
      // 获取JWT Token
      const token = await this.getJWTToken();

      // 建立Socket连接，在握手时传递JWT
      this.socket = io('http://localhost:3001', {
        auth: {
          token: token // 方式1：在auth对象中传递
        },
        // 或者使用query参数
        query: {
          token: token // 方式2：在query中传递
        },
        // 或者使用extraHeaders
        extraHeaders: {
          Authorization: `Bearer ${token}` // 方式3：在header中传递
        }
      });

      // 监听连接事件
      this.socket.on('connect', () => {
        console.log('✅ Socket连接成功', this.socket?.id);
      });

      // 监听认证成功事件
      this.socket.on('authenticated', (userData) => {
        console.log('🔐 用户认证成功:', userData);
      });

      // 监听认证失败事件
      this.socket.on('authentication_error', (error) => {
        console.error('❌ 认证失败:', error);
        this.disconnect();
      });

      // 监听Token过期事件
      this.socket.on('token_expired', async () => {
        console.log('🔄 Token过期，重新获取...');
        await this.refreshToken();
      });

      return this.socket;
    } catch (error) {
      console.error('Socket连接失败:', error);
      throw error;
    }
  }

  // 3. 刷新Token
  async refreshToken() {
    try {
      const newToken = await this.getJWTToken();
      
      // 重新连接
      this.socket?.disconnect();
      await this.connect();
    } catch (error) {
      console.error('Token刷新失败:', error);
      // 重定向到登录页面
      window.location.href = '/login';
    }
  }

  // 4. 发送认证消息
  sendMessage(event: string, data: any) {
    if (!this.socket) {
      throw new Error('Socket未连接');
    }

    this.socket.emit(event, data);
  }

  // 5. 断开连接
  disconnect() {
    this.socket?.disconnect();
    this.socket = null;
    this.jwtToken = null;
  }
}

// 使用示例
const socketClient = new SocketAuthClient();

export default socketClient;
```

#### 后端Socket.IO服务实现

```typescript
// backend/socket-server.ts
import { Server } from 'socket.io';
import { createServer } from 'http';
import { jwtVerify, createRemoteJWKSet } from 'jose';

const JWKS = createRemoteJWKSet(
  new URL('http://auth-service:10086/api/auth/jwks')
);

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  organizationId?: string;
}

class SocketIOServer {
  private io: Server;
  private server: any;

  constructor() {
    this.server = createServer();
    this.io = new Server(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.setupAuthentication();
    this.setupEventHandlers();
  }

  // 1. 设置JWT认证中间件
  private setupAuthentication() {
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        // 从多个来源提取JWT Token
        let token: string | undefined;

        // 方式1：从auth对象获取
        token = socket.handshake.auth?.token;

        // 方式2：从query参数获取
        if (!token) {
          token = socket.handshake.query?.token as string;
        }

        // 方式3：从headers获取
        if (!token) {
          const authHeader = socket.handshake.headers?.authorization;
          if (authHeader && authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7);
          }
        }

        if (!token) {
          throw new Error('缺少JWT Token');
        }

        // 验证JWT Token
        const { payload } = await jwtVerify(token, JWKS, {
          issuer: 'http://auth-service:10086',
          audience: 'http://auth-service:10086'
        });

        // 将用户信息附加到socket对象
        socket.userId = payload.userId as string;
        socket.userEmail = payload.email as string;
        socket.userRole = payload.role as string;
        socket.organizationId = payload.organizationId as string;

        console.log(`🔐 用户认证成功: ${socket.userEmail} (${socket.userId})`);
        
        // 通知客户端认证成功
        socket.emit('authenticated', {
          userId: socket.userId,
          email: socket.userEmail,
          role: socket.userRole
        });

        next(); // 允许连接
      } catch (error) {
        console.error('Socket认证失败:', error.message);
        
        // 通知客户端认证失败
        socket.emit('authentication_error', {
          message: '认证失败',
          error: error.message
        });

        next(new Error('认证失败'));
      }
    });
  }

  // 2. 设置事件处理器
  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`✅ 用户连接: ${socket.userEmail} (${socket.id})`);

      // 加入用户专属房间
      socket.join(`user_${socket.userId}`);
      socket.join(`org_${socket.organizationId}`);

      // 处理聊天消息
      socket.on('chat_message', async (data) => {
        try {
          // 验证消息权限
          if (!this.canSendMessage(socket, data)) {
            socket.emit('error', { message: '无权发送消息' });
            return;
          }

          // 调用业务API处理消息
          const processedMessage = await this.processMessage(socket, data);

          // 广播消息给相关用户
          this.io.to(`org_${socket.organizationId}`).emit('new_message', {
            id: processedMessage.id,
            content: processedMessage.content,
            sender: {
              id: socket.userId,
              email: socket.userEmail,
              role: socket.userRole
            },
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          console.error('处理消息失败:', error);
          socket.emit('error', { message: '消息发送失败' });
        }
      });

      // 处理实时通知
      socket.on('subscribe_notifications', () => {
        socket.join(`notifications_${socket.userId}`);
        console.log(`📢 用户订阅通知: ${socket.userEmail}`);
      });

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        console.log(`❌ 用户断开连接: ${socket.userEmail} (${reason})`);
      });

      // Token过期处理
      socket.on('refresh_token', async (newToken) => {
        try {
          // 验证新Token
          const { payload } = await jwtVerify(newToken, JWKS);
          
          // 更新socket用户信息
          socket.userId = payload.userId as string;
          socket.userEmail = payload.email as string;
          
          socket.emit('token_refreshed', { success: true });
        } catch (error) {
          socket.emit('token_expired');
          socket.disconnect();
        }
      });
    });
  }

  // 3. 权限检查
  private canSendMessage(socket: AuthenticatedSocket, data: any): boolean {
    // 检查用户角色权限
    if (socket.userRole === 'banned') {
      return false;
    }

    // 检查组织权限
    if (data.organizationId && data.organizationId !== socket.organizationId) {
      return false;
    }

    return true;
  }

  // 4. 调用业务API
  private async processMessage(socket: AuthenticatedSocket, data: any) {
    // 使用用户的身份信息调用业务API
    const response = await fetch('http://business-api/api/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 传递用户信息到业务API
        'X-User-ID': socket.userId!,
        'X-User-Email': socket.userEmail!,
        'X-Organization-ID': socket.organizationId!,
      },
      body: JSON.stringify({
        content: data.content,
        type: data.type,
        metadata: data.metadata
      })
    });

    return response.json();
  }

  // 5. 推送通知给特定用户
  public pushNotification(userId: string, notification: any) {
    this.io.to(`notifications_${userId}`).emit('notification', notification);
  }

  // 6. 推送消息给组织
  public pushToOrganization(organizationId: string, event: string, data: any) {
    this.io.to(`org_${organizationId}`).emit(event, data);
  }

  // 7. 启动服务器
  public start(port: number = 3001) {
    this.server.listen(port, () => {
      console.log(`🚀 Socket.IO服务器启动在端口 ${port}`);
    });
  }
}

// 启动Socket.IO服务器
const socketServer = new SocketIOServer();
socketServer.start();

export default socketServer;
```

### 方案二：连接后认证

```typescript
// 前端：连接后发送认证消息
const socket = io('http://localhost:3001');

socket.on('connect', async () => {
  try {
    const token = await getJWTToken();
    socket.emit('authenticate', { token });
  } catch (error) {
    console.error('认证失败:', error);
  }
});

// 后端：处理认证消息
io.on('connection', (socket) => {
  let isAuthenticated = false;
  let userId: string;

  socket.on('authenticate', async (data) => {
    try {
      const { payload } = await jwtVerify(data.token, JWKS);
      
      isAuthenticated = true;
      userId = payload.userId as string;
      
      socket.emit('authenticated', { success: true });
    } catch (error) {
      socket.emit('authentication_error', { error: error.message });
      socket.disconnect();
    }
  });

  // 所有业务事件都需要检查认证状态
  socket.on('chat_message', (data) => {
    if (!isAuthenticated) {
      socket.emit('error', { message: '未认证' });
      return;
    }
    
    // 处理消息...
  });
});
```

## 🔧 前端完整实现示例

### React Hook集成

```typescript
// hooks/useSocket.ts
import { useEffect, useState, useCallback } from 'react';
import { Socket } from 'socket.io-client';
import socketClient from '../services/socket-client';

interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  sendMessage: (event: string, data: any) => void;
}

export const useSocket = (): UseSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const connect = useCallback(async () => {
    try {
      const connectedSocket = await socketClient.connect();
      setSocket(connectedSocket);
      
      connectedSocket.on('connect', () => setIsConnected(true));
      connectedSocket.on('disconnect', () => setIsConnected(false));
      
    } catch (error) {
      console.error('Socket连接失败:', error);
    }
  }, []);

  const disconnect = useCallback(() => {
    socketClient.disconnect();
    setSocket(null);
    setIsConnected(false);
  }, []);

  const sendMessage = useCallback((event: string, data: any) => {
    if (socket && isConnected) {
      socket.emit(event, data);
    }
  }, [socket, isConnected]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    sendMessage
  };
};
```

### 聊天组件示例

```typescript
// components/ChatRoom.tsx
import React, { useEffect, useState } from 'react';
import { useSocket } from '../hooks/useSocket';

interface Message {
  id: string;
  content: string;
  sender: {
    id: string;
    email: string;
    role: string;
  };
  timestamp: string;
}

const ChatRoom: React.FC = () => {
  const { socket, isConnected, connect, sendMessage } = useSocket();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');

  useEffect(() => {
    // 自动连接
    connect();
  }, [connect]);

  useEffect(() => {
    if (!socket) return;

    // 监听新消息
    socket.on('new_message', (message: Message) => {
      setMessages(prev => [...prev, message]);
    });

    // 监听错误
    socket.on('error', (error) => {
      console.error('Socket错误:', error);
      alert(`错误: ${error.message}`);
    });

    return () => {
      socket.off('new_message');
      socket.off('error');
    };
  }, [socket]);

  const handleSendMessage = () => {
    if (!inputMessage.trim() || !isConnected) return;

    sendMessage('chat_message', {
      content: inputMessage,
      type: 'text',
      timestamp: new Date().toISOString()
    });

    setInputMessage('');
  };

  return (
    <div className="chat-room">
      <div className="connection-status">
        状态: {isConnected ? '🟢 已连接' : '🔴 未连接'}
      </div>
      
      <div className="messages">
        {messages.map(message => (
          <div key={message.id} className="message">
            <strong>{message.sender.email}:</strong>
            <span>{message.content}</span>
            <small>{new Date(message.timestamp).toLocaleTimeString()}</small>
          </div>
        ))}
      </div>
      
      <div className="input-area">
        <input
          type="text"
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          placeholder="输入消息..."
          disabled={!isConnected}
        />
        <button 
          onClick={handleSendMessage}
          disabled={!isConnected || !inputMessage.trim()}
        >
          发送
        </button>
      </div>
    </div>
  );
};

export default ChatRoom;
```

## 🛡️ 安全考虑

### 1. Token安全传输

```typescript
// 仅在HTTPS环境下传输JWT Token
const socket = io('wss://your-domain.com', {
  secure: true,
  auth: { token }
});
```

### 2. Token刷新机制

```typescript
// 定期检查Token是否即将过期
setInterval(async () => {
  const token = localStorage.getItem('jwt_token');
  if (token) {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const exp = payload.exp * 1000;
    const now = Date.now();
    
    // 如果Token在5分钟内过期，则刷新
    if (exp - now < 5 * 60 * 1000) {
      await socketClient.refreshToken();
    }
  }
}, 60000); // 每分钟检查一次
```

### 3. 权限验证

```typescript
// 后端：细粒度权限控制
socket.on('admin_action', async (data) => {
  if (socket.userRole !== 'admin') {
    socket.emit('error', { message: '需要管理员权限' });
    return;
  }
  
  // 执行管理员操作...
});
```

## 📊 性能优化

### 1. 连接池管理

```typescript
// 限制同一用户的连接数
const userConnections = new Map<string, Set<string>>();

io.on('connection', (socket) => {
  const userId = socket.userId!;
  
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set());
  }
  
  const connections = userConnections.get(userId)!;
  
  if (connections.size >= 5) { // 限制每用户最多5个连接
    socket.emit('error', { message: '连接数超限' });
    socket.disconnect();
    return;
  }
  
  connections.add(socket.id);
  
  socket.on('disconnect', () => {
    connections.delete(socket.id);
  });
});
```

### 2. 消息去重

```typescript
// 避免重复消息
const messageCache = new Set<string>();

socket.on('chat_message', (data) => {
  const messageHash = `${socket.userId}_${data.content}_${data.timestamp}`;
  
  if (messageCache.has(messageHash)) {
    return; // 重复消息，忽略
  }
  
  messageCache.add(messageHash);
  
  // 处理消息...
  
  // 清理旧缓存
  setTimeout(() => {
    messageCache.delete(messageHash);
  }, 30000);
});
```

## 🎯 总结

Socket.IO + JWT的完美组合：

1. ✅ **解决Cookie限制**：WebSocket连接无法传递Cookie的问题
2. ✅ **用户身份识别**：JWT包含完整用户信息，无需额外查询
3. ✅ **权限控制**：基于JWT中的角色和组织信息进行权限验证
4. ✅ **跨域支持**：JWT Token完全支持跨域传输
5. ✅ **实时认证**：连接建立时立即验证用户身份
6. ✅ **Token刷新**：支持在线Token刷新，无需重新连接

**推荐流程**：
1. 用户通过Cookie Session登录
2. 创建JWT Token用于Socket.IO连接
3. 在Socket握手时传递JWT Token
4. 后端验证Token并提取用户信息
5. 基于用户信息进行房间管理和权限控制
6. 定期刷新Token保持连接活跃

这种方案完美解决了实时通信的认证难题！🚀