/**
 * 邀请邮件模板
 * 提供多版本、多语言的邮件邀请模板
 */

import { EmailTemplate, EmailTemplateData, EmailTemplateVersion } from '../types/email.js';
import { i18nService, SupportedLanguage } from './i18n-service.js';

/**
 * 邀请邮件模板基类
 */
export class InviteEmailTemplate {
  protected readonly supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
  
  /**
   * 获取语言相关的文本
   */
  protected getLocalizedText(key: string, language: string = 'zh-CN', params?: Record<string, any>): string {
    try {
      const normalizedLanguage = this.normalizeLanguage(language);
      return i18nService.translate(key, normalizedLanguage, params);
    } catch (error) {
      console.warn(`Failed to get localized text for key: ${key}, language: ${language}`);
      return key;
    }
  }
  
  /**
   * 标准化语言代码
   */
  protected normalizeLanguage(language: string): SupportedLanguage {
    const langMap: Record<string, SupportedLanguage> = {
      'chinese': 'zh-CN',
      'english': 'en-US',
      'japanese': 'ja-JP',
      'korean': 'ko-KR',
      'zh': 'zh-CN',
      'en': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR'
    };
    
    return langMap[language.toLowerCase()] || 'zh-CN';
  }

  /**
   * 获取基础样式
   */
  protected getBaseStyles(): string {
    return `
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 0;
      background-color: #f7f9fc;
      line-height: 1.6;
    `;
  }

  /**
   * 获取响应式样式
   */
  protected getResponsiveStyles(): string {
    return `
      @media only screen and (max-width: 600px) {
        .email-container { width: 100% !important; margin: 0 !important; }
        .email-content { padding: 20px !important; }
        .email-header { padding: 30px 20px !important; }
        .email-button { width: 100% !important; padding: 15px !important; font-size: 16px !important; }
        .feature-grid { display: block !important; }
        .feature-item { margin-bottom: 12px !important; }
      }
      
      @media (prefers-color-scheme: dark) {
        .email-container { background-color: #1a1a1a !important; }
        .email-content { background-color: #2d2d2d !important; color: #ffffff !important; }
        .email-text { color: #e0e0e0 !important; }
        .email-footer { background-color: #1a1a1a !important; color: #888888 !important; }
      }
    `;
  }

  /**
   * 包装在容器中
   */
  protected wrapInContainer(content: string, language: string = 'zh-CN'): string {
    return `
      <!DOCTYPE html>
      <html lang="${language}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>SpecificAI Invitation</title>
        <style>
          ${this.getResponsiveStyles()}
        </style>
      </head>
      <body style="margin: 0; padding: 20px; ${this.getBaseStyles()}">
        <div class="email-container" style="
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        ">
          ${content}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 渲染邀请邮件模板
   */
  render(version: EmailTemplateVersion, data: EmailTemplateData, language?: string): EmailTemplate {
    switch (version) {
      case 'v1':
        return this.renderV1(data, language);
      case 'v2':
        return this.renderV2(data, language);
      case 'v3':
        return this.renderV3(data, language);
      default:
        return this.renderV3(data, language);
    }
  }

  /**
   * V1版本 - 简洁经典设计
   */
  private renderV1(data: EmailTemplateData, language?: string): EmailTemplate {
    const lang = this.normalizeLanguage(language || data.locale || 'zh-CN');
    const subject = this.getLocalizedText('email.invite.subject', lang, { 
      company: data.companyName || 'SpecificAI' 
    });
    
    const html = this.wrapInContainer(`
      <div style="background: white; padding: 40px; border-radius: 8px;">
        <h1 style="color: #2d3748; font-size: 28px; font-weight: 600; margin-bottom: 24px; text-align: center;">
          ${this.getLocalizedText('email.invite.welcome', lang)}
        </h1>
        
        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 16px;">
          ${this.getLocalizedText('email.invite.greeting', lang, { 
            name: data.userName || this.getLocalizedText('common.user', lang),
            inviter: data.inviterName || this.getLocalizedText('common.team', lang),
            company: data.companyName || 'SpecificAI'
          })}
        </p>
        
        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 32px;">
          ${this.getLocalizedText('email.invite.description', lang)}
        </p>
        
        <div style="text-align: center; margin: 32px 0;">
          <div style="background: #f7fafc; border: 2px dashed #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
            <p style="color: #4a5568; font-size: 14px; margin-bottom: 8px;">
              ${this.getLocalizedText('email.invite.codeLabel', lang)}
            </p>
            <span style="font-family: 'Monaco', 'Menlo', monospace; font-size: 24px; font-weight: bold; color: #667eea; letter-spacing: 2px;">
              ${data.inviteCode}
            </span>
          </div>
          
          <a href="${data.inviteUrl}" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; display: inline-block;">
            ${this.getLocalizedText('email.invite.joinNow', lang)}
          </a>
        </div>
        
        <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; line-height: 1.5;">
            ${this.getLocalizedText('email.invite.footer', lang)}
          </p>
        </div>
      </div>
    `, lang);
    
    const text = `
${this.getLocalizedText('email.invite.welcome', lang)}

${this.getLocalizedText('email.invite.greeting', lang, {
  name: data.userName || this.getLocalizedText('common.user', lang),
  inviter: data.inviterName || this.getLocalizedText('common.team', lang),
  company: data.companyName || 'SpecificAI'
})}

${this.getLocalizedText('email.invite.description', lang)}

${this.getLocalizedText('email.invite.codeLabel', lang)}: ${data.inviteCode}

${this.getLocalizedText('email.invite.joinLink', lang)}: ${data.inviteUrl}

${this.getLocalizedText('email.invite.footer', lang)}
    `.trim();
    
    return { subject, html, text };
  }

  /**
   * V2版本 - 现代卡片设计
   */
  private renderV2(data: EmailTemplateData, language?: string): EmailTemplate {
    const lang = this.normalizeLanguage(language || data.locale || 'zh-CN');
    const subject = this.getLocalizedText('email.invite.subjectV2', lang, { 
      company: data.companyName || 'SpecificAI' 
    });
    
    const html = this.wrapInContainer(`
      <div style="background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 32px; text-align: center;">
          <h1 style="margin: 0; font-size: 32px; font-weight: 700;">
            🎉 ${this.getLocalizedText('email.invite.welcomeV2', lang)}
          </h1>
          <p style="margin: 8px 0 0 0; font-size: 18px; opacity: 0.9;">
            ${data.companyName || 'SpecificAI'}
          </p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px;">
          <p style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 16px;">
            ${this.getLocalizedText('email.invite.personalGreeting', lang, {
              name: data.userName || this.getLocalizedText('common.user', lang)
            })}
          </p>
          
          <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 24px;">
            ${this.getLocalizedText('email.invite.inviterMessage', lang, {
              inviter: data.inviterName || this.getLocalizedText('common.team', lang),
              company: data.companyName || 'SpecificAI'
            })}
          </p>
          
          <!-- Features Preview -->
          <div style="background: #f8fafc; border-radius: 12px; padding: 24px; margin: 24px 0;">
            <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 16px;">
              🚀 ${this.getLocalizedText('email.invite.featuresTitle', lang)}
            </h3>
            <div class="feature-grid" style="display: grid; gap: 12px;">
              <div class="feature-item" style="display: flex; align-items: center; gap: 12px;">
                <span style="background: #e6f3ff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">🤖</span>
                <span style="color: #4a5568; font-size: 14px;">${this.getLocalizedText('email.invite.feature1', lang)}</span>
              </div>
              <div class="feature-item" style="display: flex; align-items: center; gap: 12px;">
                <span style="background: #e6f3ff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">📊</span>
                <span style="color: #4a5568; font-size: 14px;">${this.getLocalizedText('email.invite.feature2', lang)}</span>
              </div>
              <div class="feature-item" style="display: flex; align-items: center; gap: 12px;">
                <span style="background: #e6f3ff; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">⚡</span>
                <span style="color: #4a5568; font-size: 14px;">${this.getLocalizedText('email.invite.feature3', lang)}</span>
              </div>
            </div>
          </div>
          
          <!-- Invite Code -->
          <div style="text-align: center; margin: 32px 0;">
            <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border: 2px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
              <p style="color: #4a5568; font-size: 14px; margin-bottom: 12px; font-weight: 500;">
                ${this.getLocalizedText('email.invite.codeLabel', lang)}
              </p>
              <div style="background: white; border-radius: 8px; padding: 16px; box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);">
                <span style="font-family: 'Monaco', 'Menlo', monospace; font-size: 28px; font-weight: bold; color: #667eea; letter-spacing: 3px;">
                  ${data.inviteCode}
                </span>
              </div>
            </div>
            
            <a href="${data.inviteUrl}" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 18px 36px; text-decoration: none; border-radius: 12px; font-weight: 600; font-size: 16px; display: inline-block; box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);">
              🎯 ${this.getLocalizedText('email.invite.getStarted', lang)}
            </a>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f7fafc; padding: 24px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
            ${this.getLocalizedText('email.invite.footerV2', lang)}
          </p>
        </div>
      </div>
    `, lang);
    
    const text = `
🎉 ${this.getLocalizedText('email.invite.welcomeV2', lang)}
${data.companyName || 'SpecificAI'}

${this.getLocalizedText('email.invite.personalGreeting', lang, {
  name: data.userName || this.getLocalizedText('common.user', lang)
})}

${this.getLocalizedText('email.invite.inviterMessage', lang, {
  inviter: data.inviterName || this.getLocalizedText('common.team', lang),
  company: data.companyName || 'SpecificAI'
})}

🚀 ${this.getLocalizedText('email.invite.featuresTitle', lang)}
• ${this.getLocalizedText('email.invite.feature1', lang)}
• ${this.getLocalizedText('email.invite.feature2', lang)}
• ${this.getLocalizedText('email.invite.feature3', lang)}

${this.getLocalizedText('email.invite.codeLabel', lang)}: ${data.inviteCode}

${this.getLocalizedText('email.invite.joinLink', lang)}: ${data.inviteUrl}

${this.getLocalizedText('email.invite.footerV2', lang)}
    `.trim();
    
    return { subject, html, text };
  }

  /**
   * V3版本 - 企业级豪华设计
   */
  private renderV3(data: EmailTemplateData, language?: string): EmailTemplate {
    const lang = this.normalizeLanguage(language || data.locale || 'zh-CN');
    const subject = this.getLocalizedText('email.invite.subjectV3', lang, { 
      company: data.companyName || 'SpecificAI',
      inviter: data.inviterName || this.getLocalizedText('common.team', lang)
    });
    
    const html = this.wrapInContainer(`
      <div style="background: white; border-radius: 20px; overflow: hidden; box-shadow: 0 20px 64px rgba(0,0,0,0.15);">
        <!-- Premium Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; padding: 48px 40px; text-align: center; position: relative;">
          <h1 style="margin: 0 0 16px 0; font-size: 36px; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
            🌟 ${this.getLocalizedText('email.invite.welcomeV3', lang)}
          </h1>
          <p style="margin: 0; font-size: 20px; opacity: 0.95; font-weight: 500;">
            ${data.companyName || 'SpecificAI'} · ${this.getLocalizedText('email.invite.exclusive', lang)}
          </p>
        </div>
        
        <!-- Main Content -->
        <div style="padding: 48px 40px;">
          <div style="text-align: center; margin-bottom: 32px;">
            <div style="background: linear-gradient(135deg, #e6f3ff 0%, #f0f7ff 100%); border-radius: 50%; width: 80px; height: 80px; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 24px;">
              <span style="font-size: 32px;">🎯</span>
            </div>
            
            <h2 style="color: #2d3748; font-size: 24px; font-weight: 700; margin-bottom: 16px;">
              ${this.getLocalizedText('email.invite.personalGreetingV3', lang, {
                name: data.userName || this.getLocalizedText('common.user', lang)
              })}
            </h2>
            
            <p style="color: #4a5568; font-size: 18px; line-height: 1.6; margin-bottom: 24px;">
              ${this.getLocalizedText('email.invite.inviterMessageV3', lang, {
                inviter: data.inviterName || this.getLocalizedText('common.team', lang),
                company: data.companyName || 'SpecificAI'
              })}
            </p>
          </div>
          
          <!-- Premium Features -->
          <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-radius: 16px; padding: 32px; margin: 32px 0;">
            <h3 style="color: #2d3748; font-size: 20px; font-weight: 700; margin-bottom: 24px; text-align: center;">
              🚀 ${this.getLocalizedText('email.invite.premiumFeatures', lang)}
            </h3>
            
            <div class="feature-grid" style="display: grid; gap: 16px;">
              <div class="feature-item" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #667eea;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; font-size: 20px; font-weight: bold;">🤖</div>
                <div>
                  <div style="color: #2d3748; font-weight: 600; margin-bottom: 4px;">
                    ${this.getLocalizedText('email.invite.feature1Title', lang)}
                  </div>
                  <div style="color: #4a5568; font-size: 14px;">
                    ${this.getLocalizedText('email.invite.feature1Desc', lang)}
                  </div>
                </div>
              </div>
              
              <div class="feature-item" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #667eea;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; font-size: 20px; font-weight: bold;">📊</div>
                <div>
                  <div style="color: #2d3748; font-weight: 600; margin-bottom: 4px;">
                    ${this.getLocalizedText('email.invite.feature2Title', lang)}
                  </div>
                  <div style="color: #4a5568; font-size: 14px;">
                    ${this.getLocalizedText('email.invite.feature2Desc', lang)}
                  </div>
                </div>
              </div>
              
              <div class="feature-item" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #667eea;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; font-size: 20px; font-weight: bold;">⚡</div>
                <div>
                  <div style="color: #2d3748; font-weight: 600; margin-bottom: 4px;">
                    ${this.getLocalizedText('email.invite.feature3Title', lang)}
                  </div>
                  <div style="color: #4a5568; font-size: 14px;">
                    ${this.getLocalizedText('email.invite.feature3Desc', lang)}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Exclusive Invite Code -->
          <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 3px solid #e2e8f0; border-radius: 16px; padding: 32px; text-align: center; margin: 32px 0;">
            <p style="color: #4a5568; font-size: 16px; margin-bottom: 16px; font-weight: 600;">
              🎫 ${this.getLocalizedText('email.invite.exclusiveCode', lang)}
            </p>
            <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: inset 0 4px 8px rgba(0,0,0,0.08); border: 2px solid #667eea;">
              <span style="font-family: 'Monaco', 'Menlo', monospace; font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 4px;">
                ${data.inviteCode}
              </span>
            </div>
            <p style="color: #718096; font-size: 12px; margin-top: 12px; font-style: italic;">
              ${this.getLocalizedText('email.invite.codeExpiry', lang)}
            </p>
          </div>
          
          <!-- CTA Button -->
          <div style="text-align: center; margin: 40px 0;">
            <a href="${data.inviteUrl}" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 40px; text-decoration: none; border-radius: 16px; font-weight: 700; font-size: 18px; display: inline-block; box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);">
              🎯 ${this.getLocalizedText('email.invite.joinExclusive', lang)}
            </a>
          </div>
          
          <!-- Quick Start Guide -->
          <div style="background: #e6f3ff; border-radius: 12px; padding: 24px; margin: 32px 0;">
            <h4 style="color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 16px;">
              🚀 ${this.getLocalizedText('email.invite.quickStart', lang)}
            </h4>
            <ol style="color: #4a5568; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
              <li>${this.getLocalizedText('email.invite.step1', lang)}</li>
              <li>${this.getLocalizedText('email.invite.step2', lang)}</li>
              <li>${this.getLocalizedText('email.invite.step3', lang)}</li>
            </ol>
          </div>
        </div>
        
        <!-- Premium Footer -->
        <div style="background: #2d3748; color: #a0aec0; padding: 32px 40px; text-align: center;">
          <p style="margin: 0 0 16px 0; font-size: 14px; line-height: 1.6;">
            ${this.getLocalizedText('email.invite.footerV3', lang)}
          </p>
          <div style="border-top: 1px solid #4a5568; padding-top: 16px; margin-top: 16px;">
            <p style="margin: 0; font-size: 12px; opacity: 0.8;">
              © 2024 SpecificAI. ${this.getLocalizedText('email.invite.rights', lang)}
            </p>
          </div>
        </div>
      </div>
    `, lang);
    
    const text = `
🌟 ${this.getLocalizedText('email.invite.welcomeV3', lang)}
${data.companyName || 'SpecificAI'} · ${this.getLocalizedText('email.invite.exclusive', lang)}

🎯 ${this.getLocalizedText('email.invite.personalGreetingV3', lang, {
  name: data.userName || this.getLocalizedText('common.user', lang)
})}

${this.getLocalizedText('email.invite.inviterMessageV3', lang, {
  inviter: data.inviterName || this.getLocalizedText('common.team', lang),
  company: data.companyName || 'SpecificAI'
})}

🚀 ${this.getLocalizedText('email.invite.premiumFeatures', lang)}
• ${this.getLocalizedText('email.invite.feature1Title', lang)}: ${this.getLocalizedText('email.invite.feature1Desc', lang)}
• ${this.getLocalizedText('email.invite.feature2Title', lang)}: ${this.getLocalizedText('email.invite.feature2Desc', lang)}
• ${this.getLocalizedText('email.invite.feature3Title', lang)}: ${this.getLocalizedText('email.invite.feature3Desc', lang)}

🎫 ${this.getLocalizedText('email.invite.exclusiveCode', lang)}: ${data.inviteCode}

${this.getLocalizedText('email.invite.joinLink', lang)}: ${data.inviteUrl}

🚀 ${this.getLocalizedText('email.invite.quickStart', lang)}
1. ${this.getLocalizedText('email.invite.step1', lang)}
2. ${this.getLocalizedText('email.invite.step2', lang)}
3. ${this.getLocalizedText('email.invite.step3', lang)}

${this.getLocalizedText('email.invite.footerV3', lang)}

© 2024 SpecificAI. ${this.getLocalizedText('email.invite.rights', lang)}
    `.trim();
    
    return { subject, html, text };
  }
}