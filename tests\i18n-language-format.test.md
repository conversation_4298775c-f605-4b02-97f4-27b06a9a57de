# 国际化语言格式支持测试

## 📋 测试目标

验证国际化中间件能够正确处理 `chinese`/`english` 格式的语言参数，并将其转换为标准的语言代码。

## 🧪 测试用例

### 1. 请求头语言格式测试

#### 1.1 使用 `chinese` 格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: chinese" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为中文
- `message` 字段显示中文错误信息
- 响应头 `Content-Language: zh-CN`

#### 1.2 使用 `english` 格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: english" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为英文
- `message` 字段显示英文错误信息
- 响应头 `Content-Language: en-US`

#### 1.3 使用 `japanese` 格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: japanese" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为日文（如果支持）
- 响应头 `Content-Language: ja-JP`

#### 1.4 使用 `korean` 格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: korean" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为韩文（如果支持）
- 响应头 `Content-Language: ko-KR`

### 2. 请求体语言格式测试

#### 2.1 注册接口使用 `chinese` 格式
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "测试用户",
    "company_name": "测试公司",
    "invite_code": "INVALID",
    "language": "chinese"
  }'
```

**期望结果:**
- 响应语言为中文
- 错误消息显示为中文

#### 2.2 注册接口使用 `english` 格式
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User",
    "company_name": "Test Company",
    "invite_code": "INVALID",
    "language": "english"
  }'
```

**期望结果:**
- 响应语言为英文
- 错误消息显示为英文

### 3. 查询参数语言格式测试

#### 3.1 GET请求使用 `chinese` 格式
```bash
curl "http://localhost:10086/api/auth/languages?language=chinese"
```

**期望结果:**
- 响应语言为中文
- `message` 字段显示 "成功"

#### 3.2 GET请求使用 `english` 格式
```bash
curl "http://localhost:10086/api/auth/languages?language=english"
```

**期望结果:**
- 响应语言为英文
- `message` 字段显示 "Success"

### 4. 语言切换API测试

#### 4.1 切换到 `chinese`
```bash
curl -X POST http://localhost:10086/api/auth/switch-language \
  -H "Content-Type: application/json" \
  -d '{"language": "chinese"}'
```

**期望结果:**
```json
{
  "success": true,
  "message": "语言设置更新成功",
  "language": "zh-CN",
  "originalLanguage": "chinese",
  "userUpdated": false
}
```

#### 4.2 切换到 `english`
```bash
curl -X POST http://localhost:10086/api/auth/switch-language \
  -H "Content-Type: application/json" \
  -d '{"language": "english"}'
```

**期望结果:**
```json
{
  "success": true,
  "message": "Language settings updated successfully",
  "language": "en-US",
  "originalLanguage": "english",
  "userUpdated": false
}
```

### 5. 混合格式优先级测试

#### 5.1 请求头和请求体不同格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: english" \
  -d '{
    "invite_code": "INVALID",
    "language": "chinese"
  }'
```

**期望结果:**
- 响应语言为英文（请求头优先级更高）
- 响应头 `Content-Language: en-US`

#### 5.2 标准格式和文字格式混合
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: zh-CN" \
  -d '{
    "invite_code": "INVALID",
    "language": "english"
  }'
```

**期望结果:**
- 响应语言为中文（请求头优先级更高）
- 响应头 `Content-Language: zh-CN`

### 6. 大小写不敏感测试

#### 6.1 大写格式
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: ENGLISH" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为英文
- 大小写不影响识别

#### 6.2 混合大小写
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: Chinese" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为中文
- 混合大小写正确识别

### 7. 错误格式处理测试

#### 7.1 不支持的语言格式
```bash
curl -X POST http://localhost:10086/api/auth/switch-language \
  -H "Content-Type: application/json" \
  -d '{"language": "french"}'
```

**期望结果:**
```json
{
  "success": false,
  "error": "INVALID_LANGUAGE",
  "message": "格式不正确",
  "supportedFormats": [
    "chinese", "english", "japanese", "korean",
    "zh-CN", "en-US", "ja-JP", "ko-KR"
  ]
}
```

#### 7.2 空语言参数
```bash
curl -X POST http://localhost:10086/api/auth/switch-language \
  -H "Content-Type: application/json" \
  -d '{"language": ""}'
```

**期望结果:**
- 返回验证错误
- 提供支持的格式列表

### 8. 完整注册流程测试

#### 8.1 使用 `english` 完成注册
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "New User",
    "company_name": "New Company",
    "invite_code": "TEST2025",
    "language": "english"
  }'
```

**期望结果:**
- 注册成功消息为英文
- `nextSteps.instructions` 为英文
- 所有用户反馈信息为英文

#### 8.2 使用 `chinese` 完成注册
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "新用户",
    "company_name": "新公司",
    "invite_code": "TEST2025",
    "language": "chinese"
  }'
```

**期望结果:**
- 注册成功消息为中文
- `nextSteps.instructions` 为中文
- 所有用户反馈信息为中文

## 🔍 验证要点

### 1. 语言格式转换
- [ ] `chinese` → `zh-CN`
- [ ] `english` → `en-US`
- [ ] `japanese` → `ja-JP`
- [ ] `korean` → `ko-KR`
- [ ] 标准格式 (`zh-CN`, `en-US`) 保持不变

### 2. 大小写处理
- [ ] 大写格式正确识别
- [ ] 小写格式正确识别
- [ ] 混合大小写正确识别
- [ ] 前后空格正确处理

### 3. 优先级保持
- [ ] 请求头优先级最高（无论格式）
- [ ] 请求体次之
- [ ] 查询参数第三
- [ ] 默认语言最后

### 4. 错误处理
- [ ] 不支持的格式返回明确错误
- [ ] 错误响应包含支持的格式列表
- [ ] 空值和无效值正确处理

### 5. API响应一致性
- [ ] 响应中包含标准化后的语言代码
- [ ] 响应中包含原始输入（用于调试）
- [ ] 响应头正确设置 `Content-Language`

## 📊 支持的语言格式映射

| 输入格式 | 标准代码 | 说明 |
|---------|---------|------|
| `chinese` | `zh-CN` | 简体中文 |
| `english` | `en-US` | 美式英语 |
| `japanese` | `ja-JP` | 日语 |
| `korean` | `ko-KR` | 韩语 |
| `zh-CN` | `zh-CN` | 标准格式保持不变 |
| `en-US` | `en-US` | 标准格式保持不变 |
| `ja-JP` | `ja-JP` | 标准格式保持不变 |
| `ko-KR` | `ko-KR` | 标准格式保持不变 |

## 🐛 故障排除

### 常见问题

1. **语言格式不识别**
   - 检查拼写是否正确
   - 确认是否在支持的格式列表中
   - 查看控制台日志

2. **转换结果不正确**
   - 验证 `normalizeLanguageCode` 函数
   - 检查映射表是否完整

3. **优先级不正确**
   - 确认请求头、请求体、查询参数的设置
   - 验证中间件的执行顺序

---

**测试完成标准:**
- 所有语言格式正确转换
- 优先级逻辑保持不变
- 错误处理完善
- API响应格式正确
