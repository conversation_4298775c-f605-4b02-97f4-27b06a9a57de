#!/usr/bin/env node
import "dotenv/config";

// Temporarily disable proxy for localhost testing
delete process.env.ALL_PROXY;
delete process.env.HTTP_PROXY;
delete process.env.HTTPS_PROXY;

const BASE_URL = process.env.BETTER_AUTH_URL || "http://localhost:10086";
const TEST_EMAIL = `test-${Date.now()}@example.com`;
const TEST_PASSWORD = "TestPassword123!";

console.log("🚀 Starting Better Auth API Test");
console.log("📍 Base URL:", BASE_URL);
console.log("📧 Test Email:", TEST_EMAIL);
console.log("🔒 Test Password:", TEST_PASSWORD);
console.log("");

let authHeaders = {};
let sessionCookies = "";
let apiLogs = [];

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    "Content-Type": "application/json",
    ...authHeaders,
    ...options.headers,
  };

  if (sessionCookies) {
    headers["Cookie"] = sessionCookies;
  }

  console.log(`🔄 ${options.method || "GET"} ${url}`);

  try {
    // Create a custom fetch agent that bypasses proxy
    const requestOptions = {
      method: options.method || "GET",
      headers,
      body: options.body ? JSON.stringify(options.body) : undefined,
      credentials: "include",
      // Bypass proxy for localhost
      agent: false,
    };

    const response = await fetch(url, requestOptions);

    // Store cookies for session management
    const setCookieHeader = response.headers.get("set-cookie");
    if (setCookieHeader) {
      sessionCookies = setCookieHeader;
    }

    const responseText = await response.text();
    let data;

    try {
      data = JSON.parse(responseText);
    } catch {
      data = responseText;
    }

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Response:`, data);
    console.log("");

    // Log API call details to array
    const logEntry = {
      timestamp: new Date().toISOString(),
      endpoint,
      method: options.method || "GET",
      url,
      requestHeaders: headers,
      requestBody: options.body || null,
      responseStatus: response.status,
      responseHeaders: Object.fromEntries(response.headers.entries()),
      responseData: data,
      cookies: setCookieHeader || null,
    };
    apiLogs.push(logEntry);

    return { status: response.status, data, response };
  } catch (error) {
    console.error(`❌ Request failed:`, error.message);

    // Log error to array
    const errorLogEntry = {
      timestamp: new Date().toISOString(),
      endpoint,
      method: options.method || "GET",
      url,
      requestHeaders: headers,
      requestBody: options.body || null,
      error: error.message,
      responseStatus: 0,
    };
    apiLogs.push(errorLogEntry);

    return { status: 0, error: error.message };
  }
}

async function testHealthCheck() {
  console.log("🏥 Testing Health Check...");
  const result = await makeRequest("/api/health");
  return result.status === 200;
}

async function testSignUp() {
  console.log("📝 Testing Sign Up...");
  const result = await makeRequest("/api/auth/sign-up/email", {
    method: "POST",
    body: {
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
      name: "Test User",
    },
  });

  return result.status === 200 || result.status === 200;
}

async function testSignIn() {
  console.log("🔐 Testing Sign In...");
  const result = await makeRequest("/api/auth/sign-in/email", {
    method: "POST",
    body: {
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    },
  });

  if (result.status === 200 && result.data && result.data.user) {
    console.log("✅ Sign In successful, user:", result.data.user.email);
    return true;
  }

  return false;
}

async function testGetSession() {
  console.log("👤 Testing Get Session...");
  const result = await makeRequest("/api/auth/get-session");

  if (result.status === 200) {
    if (result.data && result.data.user) {
      console.log("✅ Session retrieved, user:", result.data.user.email);
      return true;
    } else {
      console.log("ℹ️  No active session found");
      return true; // This is also a valid state
    }
  }

  return false;
}

async function testSignOut() {
  console.log("🚪 Testing Sign Out...");
  const result = await makeRequest("/api/auth/sign-out", {
    method: "POST",
    body: {},
  });

  return result.status === 200;
}

async function testGetSessionAfterSignOut() {
  console.log("❓ Testing Get Session After Sign Out...");
  const result = await makeRequest("/api/auth/get-session");

  if (result.status === 200) {
    if (!result.data || !result.data.user) {
      console.log("✅ Session correctly cleared after sign out");
      return true;
    } else {
      console.log("❌ Session still exists after sign out");
      return false;
    }
  }

  return false;
}

async function runTests() {
  console.log("🧪 Running Authentication Tests...");
  console.log("=".repeat(50));

  const tests = [
    { name: "Health Check", fn: testHealthCheck },
    { name: "Sign Up", fn: testSignUp },
    { name: "Sign In", fn: testSignIn },
    { name: "Get Session", fn: testGetSession },
    { name: "Sign Out", fn: testSignOut },
    { name: "Get Session After Sign Out", fn: testGetSessionAfterSignOut },
  ];

  const results = [];

  for (const test of tests) {
    try {
      const success = await test.fn();
      results.push({ name: test.name, success });
      console.log(
        `${success ? "✅" : "❌"} ${test.name}: ${success ? "PASSED" : "FAILED"}`,
      );
    } catch (error) {
      console.error(`❌ ${test.name}: ERROR - ${error.message}`);
      results.push({ name: test.name, success: false, error: error.message });
    }
    console.log("-".repeat(30));
  }

  console.log("\n📊 Test Results Summary:");
  console.log("=".repeat(50));

  const passed = results.filter((r) => r.success).length;
  const total = results.length;

  results.forEach((result) => {
    const icon = result.success ? "✅" : "❌";
    const status = result.success ? "PASSED" : "FAILED";
    console.log(`${icon} ${result.name}: ${status}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);

  if (passed === total) {
    console.log(
      "🎉 All tests passed! Authentication service is working correctly.",
    );
  } else {
    console.log(
      "⚠️  Some tests failed. Please check the configuration and try again.",
    );
  }

  return passed === total;
}

async function saveLogsToFile() {
  const fs = await import("fs/promises");
  const path = await import("path");

  const logData = {
    testRun: {
      timestamp: new Date().toISOString(),
      testEmail: TEST_EMAIL,
      baseUrl: BASE_URL,
    },
    apiCalls: apiLogs,
    summary: {
      totalCalls: apiLogs.length,
      successfulCalls: apiLogs.filter(
        (log) => log.responseStatus >= 200 && log.responseStatus < 400,
      ).length,
      failedCalls: apiLogs.filter(
        (log) => log.responseStatus === 0 || log.responseStatus >= 400,
      ).length,
    },
  };

  const fileName = `api-test-logs-${Date.now()}.json`;
  const filePath = path.resolve(fileName);

  try {
    await fs.writeFile(filePath, JSON.stringify(logData, null, 2));
    console.log(`\n📁 API logs saved to: ${filePath}`);
    console.log(`📊 Total API calls logged: ${apiLogs.length}`);
  } catch (error) {
    console.error("❌ Failed to save logs:", error.message);
  }
}

// Run the tests and save logs
runTests()
  .then(() => saveLogsToFile())
  .catch(console.error);
