import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./auth-schema.js";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required");
}

// 移除连接字符串中的 schema 参数，因为 postgres 库不支持
const dbUrl = process.env.DATABASE_URL.replace(/[?&]schema=\w+/, "");

// 创建 postgres 客户端
const client = postgres(dbUrl, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
  // 在连接后设置默认 schema
  transform: {
    ...postgres.camel,
    undefined: null,
  },
  onnotice: () => {}, // 禁用通知日志
  connection: {
    search_path: "auth,public", // 设置 schema 搜索路径
  },
});

// 创建 Drizzle 实例
export const db = drizzle(client, { schema, logger: false });

export type DbType = typeof db;
