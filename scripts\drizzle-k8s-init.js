#!/usr/bin/env node

// @ts-check

/**
 * Kubernetes环境下的Drizzle数据库初始化脚本
 * 专为容器环境优化的自动化数据库迁移
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const PROJECT_ROOT = dirname(__dirname);

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

// 检查环境变量
function checkEnvironment() {
  log('📋 检查环境配置...', 'blue');
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log(`❌ 缺少必要的环境变量: ${missingVars.join(', ')}`, 'red');
    process.exit(1);
  }
  
  log('✅ 环境配置检查通过', 'green');
}

// 检查文件存在性
function checkRequiredFiles() {
  log('📄 检查必要文件...', 'blue');
  
  const requiredFiles = [
    'drizzle.config.ts',
    'src/lib/auth-schema.ts',
    'package.json'
  ];
  
  for (const file of requiredFiles) {
    const filePath = join(PROJECT_ROOT, file);
    if (!existsSync(filePath)) {
      log(`❌ 必要文件不存在: ${file}`, 'red');
      process.exit(1);
    }
  }
  
  log('✅ 必要文件检查通过', 'green');
}

// 安装依赖（如果需要）
function ensureDependencies() {
  log('📦 检查依赖安装...', 'blue');
  
  try {
    // 检查node_modules是否存在
    if (!existsSync(join(PROJECT_ROOT, 'node_modules'))) {
      log('安装项目依赖...', 'cyan');
      execSync('pnpm install --frozen-lockfile', {
        cwd: PROJECT_ROOT,
        stdio: 'inherit',
        timeout: 300000 // 5分钟超时
      });
    }
    
    log('✅ 依赖检查完成', 'green');
  } catch (error) {
    log(`❌ 依赖安装失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 测试数据库连接
async function testDatabaseConnection() {
  log('🔌 测试数据库连接...', 'blue');
  
  try {
    execSync(`echo "SELECT 1;" | psql "${process.env.DATABASE_URL}"`, {
      stdio: 'pipe',
      timeout: 10000
    });
    log('✅ 数据库连接正常', 'green');
  } catch (error) {
    log('❌ 数据库连接失败', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// 生成并执行Drizzle迁移
async function runDrizzleMigration() {
  log('🚀 开始Drizzle数据库迁移...', 'blue');
  
  try {
    // 切换到项目目录
    process.chdir(PROJECT_ROOT);
    
    // 1. 生成迁移文件
    log('📝 生成迁移文件...', 'cyan');
    execSync('pnpm db:generate', {
      stdio: 'inherit',
      timeout: 60000
    });
    
    // 2. 执行迁移
    log('⚡ 执行数据库迁移...', 'cyan');
    execSync('pnpm db:migrate', {
      stdio: 'inherit',
      timeout: 60000
    });
    
    // 3. 执行Better Auth特定迁移（如果需要）
    log('🔐 执行Better Auth迁移...', 'cyan');
    try {
      execSync('pnpm db:better-auth-migrate', {
        stdio: 'inherit',
        timeout: 60000
      });
    } catch (error) {
      log('⚠️  Better Auth迁移可选，如失败可忽略', 'yellow');
    }
    
    log('✅ Drizzle迁移执行成功', 'green');
  } catch (error) {
    log('❌ Drizzle迁移失败', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// 验证迁移结果
async function verifyMigration() {
  log('🔍 验证迁移结果...', 'blue');
  
  try {
    // 检查auth schema
    const schemaExists = execSync(`echo "SELECT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'auth');" | psql "${process.env.DATABASE_URL}" -t`, {
      encoding: 'utf8',
      timeout: 5000
    }).trim();
    
    if (schemaExists !== 't') {
      log('❌ auth schema 不存在', 'red');
      process.exit(1);
    }
    
    // 统计表数量
    const tableCount = execSync(`echo "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'auth';" | psql "${process.env.DATABASE_URL}" -t`, {
      encoding: 'utf8',
      timeout: 5000
    }).trim();
    
    log(`📊 创建了 ${tableCount} 个数据库表`, 'cyan');
    
    // 检查JWT插件表
    const jwksExists = execSync(`echo "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'jwks');" | psql "${process.env.DATABASE_URL}" -t`, {
      encoding: 'utf8',
      timeout: 5000
    }).trim();
    
    if (jwksExists === 't') {
      log('🔐 JWT插件支持已启用 (JWKS表存在)', 'green');
    } else {
      log('⚠️  JWT插件表不存在，可能影响JWT功能', 'yellow');
    }
    
    log('✅ 迁移结果验证完成', 'green');
  } catch (error) {
    log('❌ 迁移验证失败', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// 显示完成信息
function showCompletionInfo() {
  log('🎉 Kubernetes环境数据库初始化完成！', 'magenta');
  
  console.log('');
  log('📋 初始化摘要:', 'blue');
  log('  ✅ 环境配置验证通过', 'green');
  log('  ✅ Drizzle迁移执行成功', 'green');
  log('  ✅ 数据库表结构已创建', 'green');
  log('  ✅ JWT插件支持已启用', 'green');
  
  console.log('');
  log('🚀 服务准备就绪，可以启动应用！', 'green');
}

// 主函数
async function main() {
  try {
    log('🔧 Kubernetes环境 - Drizzle数据库初始化', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    // 1. 检查环境变量
    checkEnvironment();
    
    // 2. 检查必要文件
    checkRequiredFiles();
    
    // 3. 确保依赖安装
    ensureDependencies();
    
    // 4. 测试数据库连接
    await testDatabaseConnection();
    
    // 5. 执行Drizzle迁移
    await runDrizzleMigration();
    
    // 6. 验证迁移结果
    await verifyMigration();
    
    // 7. 显示完成信息
    showCompletionInfo();
    
  } catch (error) {
    log('❌ 初始化过程中发生错误', 'red');
    console.error(error);
    process.exit(1);
  }
}

// 设置进程退出处理
process.on('SIGINT', () => {
  log('🛑 初始化被中断', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('🛑 初始化被终止', 'yellow');
  process.exit(1);
});

// 运行主函数
main().catch(error => {
  log('❌ 初始化失败', 'red');
  console.error(error);
  process.exit(1);
});