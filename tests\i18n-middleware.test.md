# 国际化中间件语言检测测试用例

## 📋 测试目标

验证修改后的国际化中间件能够正确按照优先级检测语言，且完全不依赖数据库查询。

## 🧪 测试用例

### 1. 第一优先级：请求头语言检测

#### 1.1 使用 `language` 头
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: en-US" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为英文
- `message` 字段显示英文错误信息

#### 1.2 使用 `Accept-Language` 头
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en-US,zh-CN;q=0.9" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为英文（Accept-Language 头中优先级最高的支持语言）

### 2. 第二优先级：请求体语言检测

#### 2.1 POST请求体中的language参数
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User",
    "company_name": "Test Company",
    "invite_code": "INVALID",
    "language": "en-US"
  }'
```

**期望结果:**
- 响应语言为英文
- 注册失败消息显示为英文

#### 2.2 请求头和请求体同时存在（请求头优先）
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -H "language: ja-JP" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User",
    "company_name": "Test Company",
    "invite_code": "INVALID",
    "language": "en-US"
  }'
```

**期望结果:**
- 响应语言为日文（请求头优先级更高）

### 3. 第三优先级：查询参数语言检测

#### 3.1 GET请求的查询参数
```bash
curl "http://localhost:10086/api/auth/languages?language=en-US"
```

**期望结果:**
- 响应语言为英文
- `message` 字段显示 "Success"

#### 3.2 多种参数同时存在的优先级测试
```bash
curl -X POST "http://localhost:10086/api/auth/validate-invite-code?language=ko-KR" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: ja-JP" \
  -d '{
    "invite_code": "INVALID",
    "language": "en-US"
  }'
```

**期望结果:**
- 响应语言为日文（Accept-Language 头优先级最高）

### 4. 默认语言回退测试

#### 4.1 无任何语言参数
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为中文（默认语言）
- `message` 字段显示中文错误信息

#### 4.2 不支持的语言参数
```bash
curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: fr-FR" \
  -d '{"invite_code": "INVALID"}'
```

**期望结果:**
- 响应语言为中文（回退到默认语言）

### 5. 语言切换API测试

#### 5.1 未登录用户语言切换
```bash
curl -X POST http://localhost:10086/api/auth/switch-language \
  -H "Content-Type: application/json" \
  -d '{"language": "en-US"}'
```

**期望结果:**
- 切换成功
- `userUpdated: false`（不更新数据库）
- 响应消息为英文

#### 5.2 已登录用户语言切换
```bash
# 先登录获取token
curl -X POST http://localhost:10086/api/auth/sign-in \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 使用token切换语言
curl -X PUT http://localhost:10086/api/auth/user/language \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"language": "en-US"}'
```

**期望结果:**
- 切换成功
- 仅影响当前会话，不更新数据库
- 响应消息为英文

### 6. 复杂场景测试

#### 6.1 注册流程完整测试（英文）
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -H "language: en-US" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "New User",
    "company_name": "New Company",
    "invite_code": "TEST2025",
    "language": "zh-CN"
  }'
```

**期望结果:**
- 注册成功消息为英文（请求头优先）
- `nextSteps.instructions` 为英文
- 所有用户反馈信息为英文

#### 6.2 邮箱验证状态查询（多语言）
```bash
curl -X POST http://localhost:10086/api/auth/check-email-verification \
  -H "Content-Type: application/json" \
  -H "Accept-Language: ja-JP" \
  -d '{"email": "<EMAIL>"}'
```

**期望结果:**
- 查询结果消息为日文
- `nextSteps.instructions` 为日文

## 🔍 验证要点

### 1. 语言检测优先级
- [ ] 请求头 `language` 优先级最高
- [ ] `Accept-Language` 头次之
- [ ] 请求体 `language` 参数第三
- [ ] 查询参数 `language` 第四
- [ ] 默认语言 `zh-CN` 最后

### 2. 数据库独立性
- [ ] 未注册用户能正确获得多语言支持
- [ ] 语言检测不依赖用户会话
- [ ] 语言切换不更新数据库
- [ ] 所有语言检测逻辑基于请求参数

### 3. API响应一致性
- [ ] 所有API响应包含正确的语言信息
- [ ] 错误消息使用正确的语言
- [ ] 成功消息使用正确的语言
- [ ] 响应头包含 `Content-Language`

### 4. 边界情况处理
- [ ] 不支持的语言正确回退
- [ ] 空语言参数正确处理
- [ ] 格式错误的语言参数正确处理
- [ ] 缺少语言参数时使用默认语言

## 📊 性能验证

### 1. 响应时间测试
```bash
# 测试语言检测对响应时间的影响
time curl -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: en-US" \
  -d '{"invite_code": "TEST"}'
```

**期望结果:**
- 响应时间 < 100ms
- 语言检测不显著影响性能

### 2. 并发测试
```bash
# 并发测试不同语言请求
for i in {1..10}; do
  curl -X POST http://localhost:10086/api/auth/validate-invite-code \
    -H "Content-Type: application/json" \
    -H "language: en-US" \
    -d '{"invite_code": "TEST"}' &
done
wait
```

**期望结果:**
- 所有请求正确处理
- 语言检测结果一致
- 无并发冲突

## 🐛 故障排除

### 常见问题检查

1. **语言检测不生效**
   - 检查请求头格式是否正确
   - 确认语言代码是否支持
   - 查看控制台日志

2. **默认语言不正确**
   - 确认中间件正确注册
   - 检查默认语言配置

3. **响应语言不一致**
   - 验证翻译文件是否存在
   - 检查翻译键是否正确

### 调试命令

```bash
# 查看详细的请求和响应
curl -v -X POST http://localhost:10086/api/auth/validate-invite-code \
  -H "Content-Type: application/json" \
  -H "language: en-US" \
  -d '{"invite_code": "TEST"}'

# 检查响应头
curl -I http://localhost:10086/api/auth/languages
```

---

**测试完成标准:**
- 所有优先级测试通过
- 数据库独立性验证通过
- 性能指标达标
- 边界情况正确处理
