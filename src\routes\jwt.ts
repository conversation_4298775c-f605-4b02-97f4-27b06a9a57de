import type { FastifyInstance, FastifyPluginOptions } from "fastify";
import { auth } from "../auth.js";
import { jwtTokenService } from "../services/jwt-service.js";
import { DualAuthMiddleware } from "../middleware/dual-auth.js";

/**
 * JWT Token管理路由
 */
export async function jwtRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // JWT Token创建接口
  fastify.post("/jwt/create", {
    preHandler: [DualAuthMiddleware.requireAuth()],
    schema: {
      description: "创建JWT Token",
      tags: ["JWT"],
      body: {
        type: "object",
        properties: {
          expiresIn: {
            type: "string",
            description: "Token有效期（如：1h, 30m, 1d）",
            default: "1h",
          },
          customClaims: {
            type: "object",
            description: "自定义声明",
            additionalProperties: true,
          },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            token: { type: "string" },
            expiresIn: { type: "number" },
            tokenType: { type: "string", default: "Bearer" },
          },
        },
        401: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            error: { type: "string" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { expiresIn = "1h", customClaims = {} } = request.body as any;
      const authContext = (request as any).authContext;

      if (!authContext?.isAuthenticated) {
        return reply.status(401).send({
          success: false,
          error: "未认证",
          message: "需要有效的认证信息",
        });
      }

      // 使用Better Auth JWT插件创建token
      // 注意：这里需要调用Better Auth的JWT创建API
      const tokenResult = await jwtTokenService.createToken(
        authContext.user,
        authContext.session
      );

      if (!tokenResult.success) {
        return reply.status(500).send({
          success: false,
          error: tokenResult.error,
          message: "JWT Token创建失败",
        });
      }

      return reply.send({
        success: true,
        token: tokenResult.token,
        expiresIn: tokenResult.expiresIn,
        tokenType: "Bearer",
      });
    } catch (error) {
      fastify.log.error("JWT Token创建失败:", error);
      return reply.status(500).send({
        success: false,
        error: "内部服务器错误",
        message: "JWT Token创建失败",
      });
    }
  });

  // JWT Token验证接口
  fastify.post("/jwt/verify", {
    schema: {
      description: "验证JWT Token",
      tags: ["JWT"],
      body: {
        type: "object",
        required: ["token"],
        properties: {
          token: {
            type: "string",
            description: "要验证的JWT Token",
          },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            valid: { type: "boolean" },
            payload: { type: "object" },
            expiresAt: { type: "string" },
          },
        },
        400: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            error: { type: "string" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { token } = request.body as { token: string };

      if (!token) {
        return reply.status(400).send({
          success: false,
          error: "Token参数缺失",
          message: "请提供要验证的JWT Token",
        });
      }

      // 验证JWT Token
      const verifyResult = await jwtTokenService.verifyToken(token);

      if (!verifyResult.success) {
        return reply.send({
          success: true,
          valid: false,
          error: verifyResult.error,
        });
      }

      // 解析token payload
      const payload = jwtTokenService.parseTokenPayload(token);

      return reply.send({
        success: true,
        valid: true,
        payload,
        expiresAt: payload?.exp ? new Date(payload.exp * 1000).toISOString() : undefined,
      });
    } catch (error) {
      fastify.log.error("JWT Token验证失败:", error);
      return reply.status(500).send({
        success: false,
        error: "内部服务器错误",
        message: "JWT Token验证失败",
      });
    }
  });

  // JWT Token刷新接口
  fastify.post("/jwt/refresh", {
    schema: {
      description: "刷新JWT Token",
      tags: ["JWT"],
      body: {
        type: "object",
        required: ["refresh_token"],
        properties: {
          refresh_token: {
            type: "string",
            description: "刷新Token",
          },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            token: { type: "string" },
            expiresIn: { type: "number" },
            tokenType: { type: "string" },
          },
        },
        400: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            error: { type: "string" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { refresh_token } = request.body as { refresh_token: string };

      if (!refresh_token) {
        return reply.status(400).send({
          success: false,
          error: "刷新Token参数缺失",
          message: "请提供要刷新的Token",
        });
      }

      // 刷新JWT Token
      const refreshResult = await jwtTokenService.refreshToken(refresh_token);

      if (!refreshResult.success) {
        return reply.status(400).send({
          success: false,
          error: refreshResult.error,
          message: "Token刷新失败",
        });
      }

      return reply.send({
        success: true,
        token: refreshResult.token,
        expiresIn: refreshResult.expiresIn,
        tokenType: "Bearer",
      });
    } catch (error) {
      fastify.log.error("JWT Token刷新失败:", error);
      return reply.status(500).send({
        success: false,
        error: "内部服务器错误",
        message: "JWT Token刷新失败",
      });
    }
  });

  // JWT Token撤销接口
  fastify.post("/jwt/revoke", {
    preHandler: [DualAuthMiddleware.requireAuth()],
    schema: {
      description: "撤销JWT Token",
      tags: ["JWT"],
      body: {
        type: "object",
        required: ["token"],
        properties: {
          token: {
            type: "string",
            description: "要撤销的JWT Token",
          },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            message: { type: "string" },
          },
        },
        400: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            error: { type: "string" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { token } = request.body as { token: string };

      if (!token) {
        return reply.status(400).send({
          success: false,
          error: "Token参数缺失",
          message: "请提供要撤销的JWT Token",
        });
      }

      // 撤销JWT Token
      const revokeResult = await jwtTokenService.revokeToken(token);

      if (!revokeResult.success) {
        return reply.status(400).send({
          success: false,
          error: revokeResult.error,
          message: "Token撤销失败",
        });
      }

      return reply.send({
        success: true,
        message: "JWT Token已成功撤销",
      });
    } catch (error) {
      fastify.log.error("JWT Token撤销失败:", error);
      return reply.status(500).send({
        success: false,
        error: "内部服务器错误",
        message: "JWT Token撤销失败",
      });
    }
  });

  // JWT Token信息查询接口
  fastify.get("/jwt/info", {
    preHandler: [DualAuthMiddleware.requireAuth()],
    schema: {
      description: "获取当前用户的JWT Token信息",
      tags: ["JWT"],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            authType: { type: "string" },
            user: { type: "object" },
            canCreateToken: { type: "boolean" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const authContext = (request as any).authContext;

      return reply.send({
        success: true,
        authType: authContext.authType,
        user: {
          id: authContext.user.id,
          email: authContext.user.email,
          role: authContext.user.role,
        },
        canCreateToken: authContext.isAuthenticated,
        message: `当前使用${authContext.authType}认证方式`,
      });
    } catch (error) {
      fastify.log.error("JWT Token信息查询失败:", error);
      return reply.status(500).send({
        success: false,
        error: "内部服务器错误",
        message: "JWT Token信息查询失败",
      });
    }
  });
}