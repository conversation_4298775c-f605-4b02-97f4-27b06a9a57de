import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { pgSchema } from "drizzle-orm/pg-core";
const auth = pgSchema("auth");
export const user = auth.table("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified")
    .$defaultFn(() => false)
    .notNull(),
  image: text("image"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
  role: text("role"),
  banned: boolean("banned"),
  banReason: text("ban_reason"),
  banExpires: timestamp("ban_expires"),
  companyName: text("company_name"),
  language: text("language").default("chinese").notNull(),
});

export const session = auth.table("session", {
  id: text("id").primaryKey(),
  expiresAt: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  impersonatedBy: text("impersonated_by"),
  activeOrganizationId: text("active_organization_id"),
});

export const account = auth.table("account", {
  id: text("id").primaryKey(),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
});

export const verification = auth.table("verification", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").$defaultFn(
    () => /* @__PURE__ */ new Date(),
  ),
  updatedAt: timestamp("updated_at").$defaultFn(
    () => /* @__PURE__ */ new Date(),
  ),
});

export const organization = auth.table("organization", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").unique(),
  logo: text("logo"),
  createdAt: timestamp("created_at").notNull(),
  metadata: text("metadata"),
  hasCompanyProfile: boolean("has_company_profile").default(false).notNull(),
});

export const member = auth.table("member", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id")
    .notNull()
    .references(() => organization.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  role: text("role").default("member").notNull(),
  createdAt: timestamp("created_at").notNull(),
});

export const invitation = auth.table("invitation", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id")
    .notNull()
    .references(() => organization.id, { onDelete: "cascade" }),
  email: text("email").notNull(),
  role: text("role"),
  status: text("status").default("pending").notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  inviterId: text("inviter_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

// 订阅表
export const subscription = auth.table("subscription", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id")
    .notNull()
    .unique()
    .references(() => organization.id, { onDelete: "cascade" }),
  plan: text("plan").notNull(),
  memberLimit: integer("member_limit").notNull(),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

// 关系定义
export const userRelations = relations(user, ({ many }) => ({
  accounts: many(account),
  sessions: many(session),
  invitations: many(invitation),
  members: many(member),
  emailLogs: many(emailLog),
  requestLogs: many(requestLog),
  securityEvents: many(securityEvent),
}));

export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, {
    fields: [session.userId],
    references: [user.id],
  }),
}));

export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, {
    fields: [account.userId],
    references: [user.id],
  }),
}));

export const organizationRelations = relations(
  organization,
  ({ many, one }) => ({
    invitations: many(invitation),
    members: many(member),
    subscription: one(subscription, {
      fields: [organization.id],
      references: [subscription.organizationId],
    }),
  }),
);

export const memberRelations = relations(member, ({ one }) => ({
  organization: one(organization, {
    fields: [member.organizationId],
    references: [organization.id],
  }),
  user: one(user, {
    fields: [member.userId],
    references: [user.id],
  }),
}));

export const invitationRelations = relations(invitation, ({ one }) => ({
  user: one(user, {
    fields: [invitation.inviterId],
    references: [user.id],
  }),
  organization: one(organization, {
    fields: [invitation.organizationId],
    references: [organization.id],
  }),
}));

export const subscriptionRelations = relations(subscription, ({ one }) => ({
  organization: one(organization, {
    fields: [subscription.organizationId],
    references: [organization.id],
  }),
}));

// 邮件发送日志表 - 用于跟踪邮件发送状态和频率限制
export const emailLog = auth.table("email_log", {
  id: text("id").primaryKey(),
  userId: text("user_id")
    .references(() => user.id, { onDelete: "cascade" }),
  email: text("email").notNull(),
  emailType: text("email_type").notNull(), // 'verification', 'password-reset', 'notification'
  status: text("status").notNull(), // 'pending', 'sent', 'failed', 'delivered', 'bounced'
  provider: text("provider").default("resend").notNull(),
  messageId: text("message_id"),
  errorMessage: text("error_message"),
  attemptCount: integer("attempt_count").default(1).notNull(),
  sentAt: timestamp("sent_at"),
  deliveredAt: timestamp("delivered_at"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
  // 邮件内容元数据（可选）
  metadata: text("metadata"), // JSON格式存储模板版本、内容摘要等
});

// 频率限制表 - 用于API速率限制和邮件发送频率控制
export const rateLimiter = auth.table("rate_limiter", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(), // 可以是用户ID、IP地址、邮箱等
  action: text("action").notNull(), // 'email_send', 'api_request', 'login_attempt'
  windowStart: timestamp("window_start").notNull(),
  requestCount: integer("request_count").default(1).notNull(),
  maxRequests: integer("max_requests").notNull(),
  windowDurationMs: integer("window_duration_ms").notNull(),
  blocked: boolean("blocked").default(false).notNull(),
  blockedUntil: timestamp("blocked_until"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

// 请求日志表 - 用于API请求监控和审计
export const requestLog = auth.table("request_log", {
  id: text("id").primaryKey(),
  userId: text("user_id")
    .references(() => user.id, { onDelete: "set null" }),
  method: text("method").notNull(),
  path: text("path").notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  statusCode: integer("status_code"),
  responseTime: integer("response_time_ms"),
  requestSize: integer("request_size_bytes"),
  responseSize: integer("response_size_bytes"),
  errorMessage: text("error_message"),
  referrer: text("referrer"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  // 请求元数据
  metadata: text("metadata"), // JSON格式存储请求详情
});

// 安全事件表 - 用于记录安全相关的事件
export const securityEvent = auth.table("security_event", {
  id: text("id").primaryKey(),
  userId: text("user_id")
    .references(() => user.id, { onDelete: "set null" }),
  eventType: text("event_type").notNull(), // 'failed_login', 'suspicious_activity', 'rate_limit_exceeded'
  severity: text("severity").notNull(), // 'low', 'medium', 'high', 'critical'
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  details: text("details"), // JSON格式存储事件详情
  resolved: boolean("resolved").default(false).notNull(),
  resolvedAt: timestamp("resolved_at"),
  resolvedBy: text("resolved_by")
    .references(() => user.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

// 邮件日志关系
export const emailLogRelations = relations(emailLog, ({ one }) => ({
  user: one(user, {
    fields: [emailLog.userId],
    references: [user.id],
  }),
}));

// 频率限制关系（无外键关系）
export const rateLimiterRelations = relations(rateLimiter, ({ one }) => ({}));

// 请求日志关系
export const requestLogRelations = relations(requestLog, ({ one }) => ({
  user: one(user, {
    fields: [requestLog.userId],
    references: [user.id],
  }),
}));

// 安全事件关系
export const securityEventRelations = relations(securityEvent, ({ one }) => ({
  user: one(user, {
    fields: [securityEvent.userId],
    references: [user.id],
  }),
  resolvedByUser: one(user, {
    fields: [securityEvent.resolvedBy],
    references: [user.id],
  }),
}));

// ===================================
// JWT插件核心表 - JWKS密钥存储
// ===================================

// JWKS表 - 存储JWT签名密钥对
export const jwks = auth.table("jwks", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  publicKey: text("public_key").notNull(),
  privateKey: text("private_key").notNull(),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  
  // 可选字段：密钥算法信息
  algorithm: text("algorithm").default("EdDSA"),
  curve: text("curve").default("Ed25519"),
  keyId: text("key_id").unique().$defaultFn(() => crypto.randomUUID()),
  
  // 密钥状态
  active: boolean("active").default(true),
  expiresAt: timestamp("expires_at"),
  
  // 元数据
  metadata: text("metadata").$type<Record<string, any>>(),
});

// JWKS关系（无外键关系）
export const jwksRelations = relations(jwks, ({ one }) => ({}));

// ===================================
// 临时注册表 - 邮箱验证前的数据暂存
// ===================================

/**
 * 临时注册表 - 存储注册过程中的临时数据
 * 用于在邮箱验证完成前暂存用户注册信息
 */
export const pendingRegistration = auth.table("pending_registration", {
  // 主键
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  
  // 用户基本信息
  email: text("email").notNull().unique(),
  password: text("password").notNull(), // 加密后的密码
  name: text("name").notNull(),
  companyName: text("company_name").notNull(),
  language: text("language").default("chinese").notNull(),
  
  // 邀请码信息
  inviteCode: text("invite_code").notNull(),
  inviteCodeVerified: boolean("invite_code_verified").default(false).notNull(),
  
  // 验证状态
  verificationToken: text("verification_token").unique(), // Better Auth 生成的验证令牌
  verificationSent: boolean("verification_sent").default(false).notNull(),
  verificationAttempts: integer("verification_attempts").default(0).notNull(),
  lastVerificationSentAt: timestamp("last_verification_sent_at"),
  
  // 注册状态管理
  status: text("status").default("pending").notNull(), // pending, verified, expired, failed
  registrationStep: text("registration_step").default("email_verification").notNull(), // email_verification, completed
  
  // 时间管理
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
  expiresAt: timestamp("expires_at")
    .$defaultFn(() => new Date(Date.now() + 24 * 60 * 60 * 1000)) // 24小时后过期
    .notNull(),
  verifiedAt: timestamp("verified_at"),
  
  // 元数据和审计信息
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  metadata: text("metadata"), // JSON格式存储额外信息
  
  // 关联的组织信息（预创建）
  pendingOrganizationId: text("pending_organization_id"), // 预生成的组织ID
  pendingOrganizationSlug: text("pending_organization_slug"), // 预生成的组织slug
});

// 临时注册关系（无外键关系，避免数据约束问题）
export const pendingRegistrationRelations = relations(pendingRegistration, ({ one }) => ({}));

// ===================================
// 注册验证日志表 - 详细的验证过程跟踪
// ===================================

/**
 * 注册验证日志表 - 跟踪每个注册请求的验证过程
 */
export const registrationVerificationLog = auth.table("registration_verification_log", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  
  // 关联到临时注册记录
  pendingRegistrationId: text("pending_registration_id")
    .notNull()
    .references(() => pendingRegistration.id, { onDelete: "cascade" }),
  
  // 验证详情
  verificationType: text("verification_type").notNull(), // email_send, email_verify, invite_code_verify
  verificationStatus: text("verification_status").notNull(), // success, failed, pending
  attemptNumber: integer("attempt_number").default(1).notNull(),
  
  // 结果信息
  resultCode: text("result_code"), // HTTP状态码或自定义错误码
  resultMessage: text("result_message"), // 详细的结果消息
  
  // 技术详情
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  processingTimeMs: integer("processing_time_ms"), // 处理时间（毫秒）
  
  // 时间戳
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  
  // 额外数据
  metadata: text("metadata"), // JSON格式存储验证过程的详细信息
});

// 注册验证日志关系
export const registrationVerificationLogRelations = relations(
  registrationVerificationLog,
  ({ one }) => ({
    pendingRegistration: one(pendingRegistration, {
      fields: [registrationVerificationLog.pendingRegistrationId],
      references: [pendingRegistration.id],
    }),
  })
);

// 更新 pendingRegistration 的关系定义
export const pendingRegistrationRelationsUpdated = relations(
  pendingRegistration,
  ({ many }) => ({
    verificationLogs: many(registrationVerificationLog),
  })
);
