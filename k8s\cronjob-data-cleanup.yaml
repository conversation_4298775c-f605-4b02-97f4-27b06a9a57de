apiVersion: batch/v1
kind: CronJob
metadata:
  name: specific-ai-auth-data-cleanup
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: data-cleanup
    app.kubernetes.io/name: specific-ai-auth-cleanup
    app.kubernetes.io/component: cronjob
    app.kubernetes.io/version: "1.0.0"
spec:
  # 每天凌晨2点执行清理任务
  schedule: "0 2 * * *"
  
  # 时区设置（可选）
  timeZone: "Asia/Shanghai"
  
  # 并发策略：禁止并发执行，避免多个清理任务同时运行
  concurrencyPolicy: Forbid
  
  # 保留最近3次成功的任务历史
  successfulJobsHistoryLimit: 3
  
  # 保留最近1次失败的任务历史
  failedJobsHistoryLimit: 1
  
  # 开始任务的截止时间（如果错过了调度时间）
  startingDeadlineSeconds: 300
  
  jobTemplate:
    metadata:
      labels:
        app: specific-ai-auth
        component: data-cleanup
        job-type: scheduled-cleanup
    spec:
      # 任务执行超时时间：30分钟
      activeDeadlineSeconds: 1800
      
      # 失败重试次数
      backoffLimit: 2
      
      template:
        metadata:
          labels:
            app: specific-ai-auth
            component: data-cleanup
            job-type: scheduled-cleanup
        spec:
          restartPolicy: Never
          
          containers:
          - name: data-cleanup
            image: **************/specific-ai/specific-ai-auth:latest
            imagePullPolicy: Always
            
            # 执行清理命令
            command: 
              - "/bin/sh"
              - "-c"
              - |
                echo "开始执行数据清理任务..."
                echo "当前时间: $(date)"
                echo "Pod 信息: $HOSTNAME"
                
                # 设置环境变量
                export CLEANUP_VERBOSE=true
                export NODE_ENV=production
                
                # 执行健康检查
                echo "执行健康检查..."
                tsx /app/scripts/cleanup-data.ts --health-check --verbose
                health_check_exit_code=$?
                
                if [ $health_check_exit_code -ne 0 ]; then
                  echo "健康检查失败，但继续执行清理任务"
                fi
                
                # 执行数据清理
                echo "执行数据清理..."
                tsx /app/scripts/cleanup-data.ts --force --verbose
                cleanup_exit_code=$?
                
                if [ $cleanup_exit_code -eq 0 ]; then
                  echo "数据清理任务完成成功"
                else
                  echo "数据清理任务失败，退出码: $cleanup_exit_code"
                  exit $cleanup_exit_code
                fi
                
                echo "任务完成时间: $(date)"
            
            # 环境变量配置
            env:
            # 从ConfigMap加载基础配置
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-auth-config
                  key: NODE_ENV
            - name: BETTER_AUTH_URL
              valueFrom:
                configMapKeyRef:
                  name: specific-ai-auth-config
                  key: BETTER_AUTH_URL
            
            # 从Secret加载敏感配置
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: specific-ai-auth-secret
                  key: DATABASE_URL
            - name: BETTER_AUTH_SECRET
              valueFrom:
                secretKeyRef:
                  name: specific-ai-auth-secret
                  key: BETTER_AUTH_SECRET
            
            # 清理任务特定配置
            - name: CLEANUP_VERBOSE
              value: "true"
            - name: CLEANUP_BATCH_SIZE
              value: "1000"
            - name: CLEANUP_ENABLE_BACKUP
              value: "true"
            - name: CLEANUP_ENABLE_ALERTS
              value: "true"
            
            # Pod 信息
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            
            # 资源限制
            resources:
              limits:
                cpu: 500m
                memory: 512Mi
              requests:
                cpu: 200m
                memory: 256Mi
            
            # 安全上下文
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: false
              runAsNonRoot: true
              runAsUser: 1000
              capabilities:
                drop:
                - ALL
          
          # DNS 策略
          dnsPolicy: ClusterFirst
          
          # 容器组安全策略
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            fsGroup: 1000

---
# 清理任务监控 ServiceMonitor (如果使用 Prometheus)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: specific-ai-auth-cleanup-monitor
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: data-cleanup
spec:
  selector:
    matchLabels:
      app: specific-ai-auth
      component: data-cleanup
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
# 清理任务配置 ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: specific-ai-auth-cleanup-config
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: data-cleanup
data:
  # 清理策略配置
  cleanup-config.json: |
    {
      "enableCleanup": true,
      "maxRetries": 3,
      "batchSize": 1000,
      "retentionPolicies": {
        "expiredPendingRegistrations": 7,
        "completedRegistrations": 30,
        "failedRegistrations": 14,
        "verificationLogs": 30,
        "emailLogs": 60,
        "rateLimiterRecords": 7
      },
      "backupBeforeCleanup": true,
      "backupRetentionDays": 90,
      "enableMetrics": true,
      "enableAlerts": true,
      "alertThresholds": {
        "maxCleanupTimeMs": 300000,
        "maxRecordsPerBatch": 10000,
        "minCleanupSuccessRate": 0.95
      }
    }
  
  # 清理脚本配置
  cleanup-schedule.sh: |
    #!/bin/bash
    set -euo pipefail
    
    # 日志函数
    log() {
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] $*"
    }
    
    log "开始数据清理任务"
    
    # 检查数据库连接
    log "检查数据库连接..."
    if ! timeout 30 tsx /app/scripts/cleanup-data.ts --health-check; then
        log "数据库连接检查失败"
        exit 1
    fi
    
    # 执行清理
    log "执行数据清理..."
    if tsx /app/scripts/cleanup-data.ts --force --verbose --config /etc/cleanup/cleanup-config.json; then
        log "数据清理完成"
    else
        log "数据清理失败"
        exit 1
    fi
    
    log "清理任务结束"

---
# 应急清理任务 Job (手动触发)
apiVersion: batch/v1
kind: Job
metadata:
  name: specific-ai-auth-emergency-cleanup
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: data-cleanup
    job-type: emergency-cleanup
spec:
  # 不自动删除，方便查看结果
  ttlSecondsAfterFinished: 86400  # 24小时后删除
  
  backoffLimit: 1
  activeDeadlineSeconds: 3600  # 1小时超时
  
  template:
    metadata:
      labels:
        app: specific-ai-auth
        component: data-cleanup
        job-type: emergency-cleanup
    spec:
      restartPolicy: Never
      
      containers:
      - name: emergency-cleanup
        image: **************/specific-ai/specific-ai-auth:latest
        imagePullPolicy: Always
        
        command: 
          - "/bin/sh"
          - "-c"
          - |
            echo "=== 应急数据清理任务 ==="
            echo "开始时间: $(date)"
            echo "执行节点: $HOSTNAME"
            
            # 首先执行健康检查
            echo "执行健康检查..."
            tsx /app/scripts/cleanup-data.ts --health-check --verbose
            
            # 显示待清理数据统计
            echo "显示待清理数据预览..."
            tsx /app/scripts/cleanup-data.ts --dry-run --verbose
            
            # 执行强制清理（跳过确认）
            echo "执行应急清理..."
            tsx /app/scripts/cleanup-data.ts --force --verbose --batch-size 2000
            
            echo "应急清理完成时间: $(date)"
        
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: DATABASE_URL
        - name: BETTER_AUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: BETTER_AUTH_SECRET
        - name: NODE_ENV
          value: "production"
        - name: CLEANUP_VERBOSE
          value: "true"
        
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi