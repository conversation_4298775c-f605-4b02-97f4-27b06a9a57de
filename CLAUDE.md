# CLAUDE.md

## 必须使用中文回答我的问题

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Better Auth service implementation for an invite-only authentication system supporting email registration and login. The service is designed as a minimal authentication microservice for beta testing or private access scenarios.

## Usage

`/project:ultrathink-task <TASK_DESCRIPTION>`

## Context

- Task description: $ARGUMENTS
- Relevant code or files will be referenced ad-hoc using @file syntax.

## Your Role

You are the Coordinator Agent orchestrating four specialist sub-agents:

1. Architect Agent – designs high-level approach.
2. Research Agent – gathers external knowledge and precedent.
3. Coder Agent – writes or edits code.
4. Tester Agent – proposes tests and validation strategy.

## Process

1. Think step-by-step, laying out assumptions and unknowns.
2. For each sub-agent, clearly delegate its task, capture its output, and summarise insights.
3. Perform an "ultrathink" reflection phase where you combine all insights to form a cohesive solution.
4. If gaps remain, iterate (spawn sub-agents again) until confident.

## Output Format

1. **Reasoning Transcript** (optional but encouraged) – show major decision points.
2. **Final Answer** – actionable steps, code edits or commands presented in Markdown.
3. **Next Actions** – bullet list of follow-up items for the team (if any).

---

## Architecture

The project is structured as a minimal Better Auth service with the following key components:

- **Better Auth Framework**: Uses the Better Auth library for authentication functionality
- **PostgreSQL Database**: Uses PostgreSQL for user data storage with a dedicated `auth_user` database user
- **Invite Code System**: Implements a custom invite code validation system for controlling access
- **Email/Password Authentication**: Core authentication method using email and password

## Development Commands

Based on the README documentation, the following commands are available:

### Setup and Installation

```bash
npm init -y
npm install better-auth
```

### Development Server

```bash
npx better-auth serve ./auth.ts --env .env
```

### Docker Development (Optional)

```bash
# Build and run with Docker
docker build -t better-auth-service .
docker run -p 3001:3001 better-auth-service
```

## Configuration

The service expects the following environment variables in `.env`:

- `DATABASE_URL`: PostgreSQL connection string
- `BETTER_AUTH_SECRET`: Authentication secret key
- `BETTER_AUTH_BASE_URL`: Base URL for the service (e.g., <http://localhost:3001>)

## Core Implementation Details

### Main Configuration File

The `auth.ts` file contains the Better Auth configuration with:

- PostgreSQL database connection
- Email/password authentication enabled
- Email verification disabled for beta testing
- Custom invite code validation (currently hardcoded to 'GO2025')

### API Endpoints

The service automatically exposes these endpoints:

- `POST /api/sign-up/email` - User registration (requires invite code)
- `POST /api/sign-in/email` - User login
- `GET /api/session` - Get current user session
- `POST /api/sign-out` - User logout

### Database Requirements

- PostgreSQL database named `auth_db`
- Database user `auth_user` with permissions limited to the auth database

## Current Implementation Status

The project is currently in a documentation/planning phase. The actual implementation files (auth.ts, package.json, .env, Dockerfile, docker-compose.yml) are not yet created and exist only as specifications in the README.

## Development Notes

- The invite code validation is currently hardcoded and should be made configurable for production use
- Email verification is disabled for beta testing purposes
- The service is designed to be extended with additional features like OAuth, user management, and multi-tenancy
- No admin interface is currently planned for the minimal implementation
