/**
 * 缓存服务 - 用于优化邮件发送状态和验证令牌的缓存机制
 * 
 * 支持内存缓存和可扩展到Redis缓存
 */

import { randomUUID } from "crypto";

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  value: T;
  expireAt: number;
  createdAt: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  maxSize: number; // 最大缓存项数
  defaultTTL: number; // 默认过期时间（毫秒）
  cleanupInterval: number; // 清理间隔（毫秒）
  enableStats: boolean; // 是否启用统计
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  size: number;
  hitRate: number;
}

/**
 * 邮件发送状态缓存项
 */
export interface EmailSendStatus {
  userId: string;
  email: string;
  emailType: string;
  status: 'pending' | 'sent' | 'failed' | 'delivered' | 'bounced';
  messageId?: string;
  sentAt?: Date;
  attemptCount: number;
  lastAttemptAt: Date;
  nextRetryAt?: Date;
  errorMessage?: string;
}

/**
 * 验证令牌缓存项
 */
export interface VerificationToken {
  token: string;
  userId: string;
  email: string;
  type: 'email_verification' | 'password_reset';
  createdAt: Date;
  expiresAt: Date;
  used: boolean;
}

/**
 * 用户会话缓存项
 */
export interface UserSessionCache {
  userId: string;
  email: string;
  emailVerified: boolean;
  role?: string;
  organizationId?: string;
  lastActiveAt: Date;
}

/**
 * 内存缓存服务类
 */
export class InMemoryCacheService {
  private cache = new Map<string, CacheItem<any>>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 10000,
      defaultTTL: 5 * 60 * 1000, // 5分钟
      cleanupInterval: 60 * 1000, // 1分钟
      enableStats: true,
      ...config,
    };

    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0,
      hitRate: 0,
    };

    this.startCleanup();
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, value: T, ttl?: number): void {
    const now = Date.now();
    const expireAt = now + (ttl || this.config.defaultTTL);

    // 如果缓存已满，删除最老的项
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    const item: CacheItem<T> = {
      value,
      expireAt,
      createdAt: now,
      accessCount: 0,
      lastAccessed: now,
    };

    this.cache.set(key, item);
    
    if (this.config.enableStats) {
      this.stats.sets++;
      this.stats.size = this.cache.size;
    }
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key) as CacheItem<T> | undefined;
    const now = Date.now();

    if (!item) {
      if (this.config.enableStats) {
        this.stats.misses++;
        this.updateHitRate();
      }
      return null;
    }

    // 检查是否过期
    if (item.expireAt <= now) {
      this.cache.delete(key);
      if (this.config.enableStats) {
        this.stats.misses++;
        this.stats.size = this.cache.size;
        this.updateHitRate();
      }
      return null;
    }

    // 更新访问统计
    item.accessCount++;
    item.lastAccessed = now;

    if (this.config.enableStats) {
      this.stats.hits++;
      this.updateHitRate();
    }

    return item.value;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    
    if (deleted && this.config.enableStats) {
      this.stats.deletes++;
      this.stats.size = this.cache.size;
    }
    
    return deleted;
  }

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    // 检查是否过期
    if (item.expireAt <= Date.now()) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    if (this.config.enableStats) {
      this.stats.size = 0;
    }
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: this.cache.size,
      hitRate: 0,
    };
  }

  /**
   * 启动清理定时器
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, item] of this.cache.entries()) {
      if (item.expireAt <= now) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0 && this.config.enableStats) {
      this.stats.size = this.cache.size;
      console.log(`🧹 缓存清理完成，删除了 ${cleaned} 个过期项，当前缓存大小: ${this.cache.size}`);
    }
  }

  /**
   * 驱逐最老的项
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }
}

/**
 * 邮件发送状态缓存服务
 */
export class EmailCacheService {
  private cache: InMemoryCacheService;

  constructor() {
    this.cache = new InMemoryCacheService({
      maxSize: 5000,
      defaultTTL: 10 * 60 * 1000, // 10分钟
      cleanupInterval: 2 * 60 * 1000, // 2分钟清理一次
    });
  }

  /**
   * 缓存邮件发送状态
   */
  setEmailStatus(userId: string, emailType: string, status: EmailSendStatus): void {
    const key = this.getEmailStatusKey(userId, emailType);
    this.cache.set(key, status, 15 * 60 * 1000); // 15分钟过期
  }

  /**
   * 获取邮件发送状态
   */
  getEmailStatus(userId: string, emailType: string): EmailSendStatus | null {
    const key = this.getEmailStatusKey(userId, emailType);
    return this.cache.get<EmailSendStatus>(key);
  }

  /**
   * 删除邮件发送状态
   */
  deleteEmailStatus(userId: string, emailType: string): boolean {
    const key = this.getEmailStatusKey(userId, emailType);
    return this.cache.delete(key);
  }

  /**
   * 检查是否可以发送邮件（基于缓存的频率限制）
   */
  canSendEmail(userId: string, emailType: string, minIntervalMs: number = 60000): boolean {
    const status = this.getEmailStatus(userId, emailType);
    if (!status) return true;

    const now = Date.now();
    const lastAttempt = status.lastAttemptAt.getTime();
    
    return (now - lastAttempt) >= minIntervalMs;
  }

  /**
   * 更新邮件发送尝试
   */
  updateEmailAttempt(userId: string, emailType: string, success: boolean, messageId?: string, errorMessage?: string): void {
    const status = this.getEmailStatus(userId, emailType) || {
      userId,
      email: '',
      emailType,
      status: 'pending' as const,
      attemptCount: 0,
      lastAttemptAt: new Date(),
    };

    status.attemptCount++;
    status.lastAttemptAt = new Date();
    
    if (success) {
      status.status = 'sent';
      status.sentAt = new Date();
      if (messageId) {
        status.messageId = messageId;
      }
    } else {
      status.status = 'failed';
      if (errorMessage) {
        status.errorMessage = errorMessage;
      }
      // 设置下次重试时间（指数退避）
      const retryDelay = Math.min(Math.pow(2, status.attemptCount) * 60000, 30 * 60000); // 最大30分钟
      status.nextRetryAt = new Date(Date.now() + retryDelay);
    }

    this.setEmailStatus(userId, emailType, status);
  }

  private getEmailStatusKey(userId: string, emailType: string): string {
    return `email_status:${userId}:${emailType}`;
  }
}

/**
 * 验证令牌缓存服务
 */
export class TokenCacheService {
  private cache: InMemoryCacheService;

  constructor() {
    this.cache = new InMemoryCacheService({
      maxSize: 2000,
      defaultTTL: 30 * 60 * 1000, // 30分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟清理一次
    });
  }

  /**
   * 缓存验证令牌
   */
  setToken(token: string, tokenData: VerificationToken): void {
    this.cache.set(token, tokenData, 30 * 60 * 1000); // 30分钟过期
  }

  /**
   * 获取验证令牌
   */
  getToken(token: string): VerificationToken | null {
    return this.cache.get<VerificationToken>(token);
  }

  /**
   * 标记令牌为已使用
   */
  markTokenAsUsed(token: string): boolean {
    const tokenData = this.getToken(token);
    if (!tokenData) return false;

    tokenData.used = true;
    this.cache.set(token, tokenData, 60 * 1000); // 保留1分钟用于防重放
    return true;
  }

  /**
   * 删除验证令牌
   */
  deleteToken(token: string): boolean {
    return this.cache.delete(token);
  }

  /**
   * 检查令牌是否有效
   */
  isTokenValid(token: string): boolean {
    const tokenData = this.getToken(token);
    if (!tokenData) return false;
    
    const now = new Date();
    return !tokenData.used && tokenData.expiresAt > now;
  }
}

/**
 * 用户会话缓存服务
 */
export class UserSessionCacheService {
  private cache: InMemoryCacheService;

  constructor() {
    this.cache = new InMemoryCacheService({
      maxSize: 1000,
      defaultTTL: 15 * 60 * 1000, // 15分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟清理一次
    });
  }

  /**
   * 缓存用户会话信息
   */
  setUserSession(userId: string, sessionData: UserSessionCache): void {
    this.cache.set(`user_session:${userId}`, sessionData, 20 * 60 * 1000); // 20分钟过期
  }

  /**
   * 获取用户会话信息
   */
  getUserSession(userId: string): UserSessionCache | null {
    return this.cache.get<UserSessionCache>(`user_session:${userId}`);
  }

  /**
   * 删除用户会话缓存
   */
  deleteUserSession(userId: string): boolean {
    return this.cache.delete(`user_session:${userId}`);
  }

  /**
   * 更新用户最后活动时间
   */
  updateLastActive(userId: string): void {
    const session = this.getUserSession(userId);
    if (session) {
      session.lastActiveAt = new Date();
      this.setUserSession(userId, session);
    }
  }
}

// 创建全局实例
export const emailCacheService = new EmailCacheService();
export const tokenCacheService = new TokenCacheService();
export const userSessionCacheService = new UserSessionCacheService();

/**
 * 缓存管理器 - 统一管理所有缓存服务
 */
export class CacheManager {
  private services: Map<string, InMemoryCacheService> = new Map();

  constructor() {
    this.services.set('email', emailCacheService['cache']);
    this.services.set('token', tokenCacheService['cache']);
    this.services.set('session', userSessionCacheService['cache']);
  }

  /**
   * 获取所有缓存服务的统计信息
   */
  getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {};
    
    for (const [name, service] of this.services.entries()) {
      stats[name] = service.getStats();
    }
    
    return stats;
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    for (const [name, service] of this.services.entries()) {
      service.clear();
      console.log(`🧹 已清空 ${name} 缓存`);
    }
  }

  /**
   * 重置所有统计信息
   */
  resetAllStats(): void {
    for (const service of this.services.values()) {
      service.resetStats();
    }
  }

  /**
   * 停止所有清理定时器
   */
  stopAllCleanup(): void {
    for (const service of this.services.values()) {
      service.stopCleanup();
    }
  }

  /**
   * 获取缓存健康状态
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    details: Record<string, any>;
  } {
    const stats = this.getAllStats();
    let totalSize = 0;
    let avgHitRate = 0;
    let servicesCount = 0;

    for (const [name, stat] of Object.entries(stats)) {
      totalSize += stat.size;
      avgHitRate += stat.hitRate;
      servicesCount++;
    }

    avgHitRate = servicesCount > 0 ? avgHitRate / servicesCount : 0;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (totalSize > 8000 || avgHitRate < 50) {
      status = 'warning';
    }
    
    if (totalSize > 15000 || avgHitRate < 20) {
      status = 'critical';
    }

    return {
      status,
      details: {
        totalCacheSize: totalSize,
        averageHitRate: avgHitRate.toFixed(2) + '%',
        services: stats,
      },
    };
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager();