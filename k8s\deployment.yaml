apiVersion: apps/v1
kind: Deployment
metadata:
  name: specific-ai-auth
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: specific-ai-auth
  template:
    metadata:
      labels:
        app: specific-ai-auth
        app.kubernetes.io/name: specific-ai-auth
        app.kubernetes.io/component: service
    spec:
      # 初始化容器，用于数据库设置
      initContainers:
      - name: drizzle-migrate
        image: **************/specific-ai/specific-ai-auth:latest
        imagePullPolicy: Always
        command: 
          - "/bin/sh"
          - "-c"
          - |
            echo "Starting Drizzle database migration..."
            node /app/scripts/drizzle-k8s-init.js
            echo "Drizzle migration completed successfully"
        env:
        # 数据库连接配置
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: DATABASE_URL
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: NODE_ENV
        # 资源限制
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
            
      containers:
      - name: specific-ai-auth
        image: **************/specific-ai/specific-ai-auth:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 10086
          name: http
          protocol: TCP
        env:
        # 从ConfigMap加载服务器配置
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: PORT
        - name: HOST
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: HOST
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: NODE_ENV
        - name: BETTER_AUTH_URL
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: BETTER_AUTH_URL
        - name: CORS_ORIGIN
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: CORS_ORIGIN
        - name: BUSINESS_BASE_API
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: BUSINESS_BASE_API
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            configMapKeyRef:
              name: specific-ai-auth-config
              key: GOOGLE_CLIENT_ID
        
        # 从Secret加载敏感配置
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: DATABASE_URL
        - name: BETTER_AUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: BETTER_AUTH_SECRET
        - name: RESEND_API_KEY
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: RESEND_API_KEY
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: GOOGLE_CLIENT_SECRET
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /api/health
            port: 10086
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /api/health
            port: 10086
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 资源限制
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      
      # 重启策略
      restartPolicy: Always
      
      # DNS策略
      dnsPolicy: ClusterFirst