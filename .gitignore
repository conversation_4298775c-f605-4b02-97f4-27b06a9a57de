# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 构建输出
dist/
build/
*.tsbuildinfo

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs
*.log

# 运行时
.nyc_output

# 覆盖率目录
coverage/
*.lcov

# ESLint 缓存
.eslintcache

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史记录
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Docker
.dockerignore

# IDE
.vscode/
.idea/
*.swp
*.swo
*~ 
/src/generated/prisma

demo/*
.claude-instance/*