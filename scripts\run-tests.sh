#!/bin/bash

# 测试运行脚本
# 提供不同级别的测试执行选项

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -a, --all               运行所有测试"
    echo "  -u, --unit              运行单元测试"
    echo "  -i, --integration       运行集成测试"
    echo "  -c, --coverage          运行测试并生成覆盖率报告"
    echo "  -w, --watch             监听模式"
    echo "  -e, --email             运行邮件服务完整测试"
    echo "  -f, --file <文件名>     运行特定测试文件"
    echo "  --setup                 设置测试环境"
    echo "  --cleanup               清理测试环境"
    echo ""
    echo "示例:"
    echo "  $0 -a                   # 运行所有测试"
    echo "  $0 -c                   # 运行测试并生成覆盖率报告"
    echo "  $0 -f email-registration.test.ts  # 运行特定测试文件"
    echo "  $0 -e                   # 运行邮件服务P0-P4级别测试"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查Jest是否安装
    if ! npm list jest &> /dev/null; then
        log_warning "Jest 未安装，正在安装测试依赖..."
        npm install
    fi
    
    log_success "依赖检查完成"
}

# 设置测试环境
setup_test_env() {
    log_info "设置测试环境..."
    
    # 创建测试环境变量文件
    if [ ! -f .env.test ]; then
        log_info "创建 .env.test 文件..."
        cat > .env.test << EOF
# 测试环境配置
NODE_ENV=test
RESEND_API_KEY=test-api-key
BETTER_AUTH_URL=http://localhost:10086
BETTER_AUTH_SECRET=test-secret-key-for-testing-only
DATABASE_URL=postgresql://test:test@localhost:5432/test_db

# 邮件服务测试配置
EMAIL_SERVICE_TEST_MODE=true
EMAIL_TEMPLATE_VERSION=v1
EOF
        log_success "已创建 .env.test 文件"
    fi
    
    # 检查测试数据库是否可用（可选）
    if command -v psql &> /dev/null; then
        log_info "检查测试数据库连接..."
        if ! psql $DATABASE_URL -c "SELECT 1;" &> /dev/null; then
            log_warning "测试数据库连接失败，将使用模拟数据库"
        else
            log_success "测试数据库连接正常"
        fi
    fi
    
    log_success "测试环境设置完成"
}

# 清理测试环境
cleanup_test_env() {
    log_info "清理测试环境..."
    
    # 清理测试生成的文件
    rm -rf coverage/ 2>/dev/null || true
    rm -rf .nyc_output/ 2>/dev/null || true
    rm -f junit.xml 2>/dev/null || true
    rm -f test-results.json 2>/dev/null || true
    
    log_success "测试环境清理完成"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    npx jest \
        --testMatch="**/tests/**/*(*.)(spec|test).ts" \
        --testPathIgnorePatterns="/node_modules/" \
        --verbose \
        --colors
    
    if [ $? -eq 0 ]; then
        log_success "单元测试通过"
    else
        log_error "单元测试失败"
        exit 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    npx jest \
        --testMatch="**/tests/**/*integration*.test.ts" \
        --verbose \
        --colors \
        --runInBand  # 串行运行，避免端口冲突
    
    if [ $? -eq 0 ]; then
        log_success "集成测试通过"
    else
        log_error "集成测试失败"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "运行所有测试..."
    
    npx jest \
        --verbose \
        --colors \
        --detectOpenHandles \
        --forceExit
    
    if [ $? -eq 0 ]; then
        log_success "所有测试通过"
    else
        log_error "测试失败"
        exit 1
    fi
}

# 运行测试并生成覆盖率报告
run_coverage_tests() {
    log_info "运行测试并生成覆盖率报告..."
    
    npx jest \
        --coverage \
        --coverageReporters=text \
        --coverageReporters=lcov \
        --coverageReporters=html \
        --verbose \
        --detectOpenHandles \
        --forceExit
    
    if [ $? -eq 0 ]; then
        log_success "测试完成，覆盖率报告已生成"
        log_info "HTML报告路径: ./coverage/lcov-report/index.html"
        
        # 如果在支持的环境中，尝试打开报告
        if command -v open &> /dev/null; then
            log_info "尝试打开覆盖率报告..."
            open ./coverage/lcov-report/index.html 2>/dev/null || true
        fi
    else
        log_error "测试失败"
        exit 1
    fi
}

# 监听模式
run_watch_tests() {
    log_info "启动测试监听模式..."
    log_warning "按 'q' 退出监听模式"
    
    npx jest --watch --verbose --colors
}

# 运行特定测试文件
run_specific_test() {
    local test_file=$1
    log_info "运行测试文件: $test_file"
    
    if [ ! -f "tests/$test_file" ]; then
        log_error "测试文件不存在: tests/$test_file"
        exit 1
    fi
    
    npx jest "tests/$test_file" --verbose --colors
    
    if [ $? -eq 0 ]; then
        log_success "测试文件执行完成: $test_file"
    else
        log_error "测试文件执行失败: $test_file"
        exit 1
    fi
}

# 运行邮件服务完整测试
run_email_service_tests() {
    log_info "运行邮件服务完整测试 (P0-P4级别)..."
    
    # 检查环境变量
    if [ -z "$RESEND_API_KEY" ]; then
        log_warning "RESEND_API_KEY 未设置，使用测试模式"
        export RESEND_API_KEY="test-api-key"
    fi
    
    # 运行邮件服务测试
    npm run test:email
    
    if [ $? -eq 0 ]; then
        log_success "邮件服务测试完成"
    else
        log_error "邮件服务测试失败"
        exit 1
    fi
}

# 显示测试统计
show_test_stats() {
    log_info "测试统计信息:"
    
    if [ -f "coverage/lcov.info" ]; then
        log_info "覆盖率报告已生成"
        
        # 尝试显示覆盖率摘要
        if command -v lcov &> /dev/null; then
            lcov --summary coverage/lcov.info 2>/dev/null | tail -n 3
        fi
    fi
    
    # 显示测试文件数量
    local test_file_count=$(find tests -name "*.test.ts" | wc -l)
    log_info "测试文件数量: $test_file_count"
    
    # 显示测试套件数量（大致估算）
    local test_suite_count=$(grep -r "describe(" tests/ | wc -l)
    log_info "测试套件数量: $test_suite_count"
    
    # 显示测试用例数量（大致估算）
    local test_case_count=$(grep -r "it(" tests/ | wc -l)
    log_info "测试用例数量: $test_case_count"
}

# 主函数
main() {
    local action="help"
    local test_file=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                action="all"
                shift
                ;;
            -u|--unit)
                action="unit"
                shift
                ;;
            -i|--integration)
                action="integration"
                shift
                ;;
            -c|--coverage)
                action="coverage"
                shift
                ;;
            -w|--watch)
                action="watch"
                shift
                ;;
            -e|--email)
                action="email"
                shift
                ;;
            -f|--file)
                action="file"
                test_file="$2"
                shift 2
                ;;
            --setup)
                action="setup"
                shift
                ;;
            --cleanup)
                action="cleanup"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行检查
    check_dependencies
    
    # 根据动作执行相应的命令
    case $action in
        setup)
            setup_test_env
            ;;
        cleanup)
            cleanup_test_env
            ;;
        unit)
            setup_test_env
            run_unit_tests
            show_test_stats
            ;;
        integration)
            setup_test_env
            run_integration_tests
            show_test_stats
            ;;
        all)
            setup_test_env
            run_all_tests
            show_test_stats
            ;;
        coverage)
            setup_test_env
            run_coverage_tests
            show_test_stats
            ;;
        watch)
            setup_test_env
            run_watch_tests
            ;;
        email)
            setup_test_env
            run_email_service_tests
            ;;
        file)
            if [ -z "$test_file" ]; then
                log_error "请指定测试文件名"
                exit 1
            fi
            setup_test_env
            run_specific_test "$test_file"
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 脚本入口
main "$@"