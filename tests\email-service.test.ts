#!/usr/bin/env ts-node

/**
 * Resend 邮件服务 P0-P4 级别测试脚本
 * 
 * 测试级别说明：
 * P0: 核心功能正常工作 - 基础邮件发送功能
 * P1: 主要功能正常工作 - 模板版本切换、内测码集成
 * P2: 重要功能正常工作 - 错误处理、边界条件
 * P3: 次要功能正常工作 - 配置管理、监控功能
 * P4: 附加功能正常工作 - 性能测试、并发测试
 */

import { config } from 'dotenv';
import { EmailService, createEmailService } from '../src/services/email-service.js';
import { EmailTemplateManager } from '../src/services/email-templates.js';
import { inviteCodeContextManager } from '../src/services/invite-code-context.js';
import { EmailSendResult } from '../src/types/email.js';

// 加载环境变量
config();

/**
 * 测试结果接口
 */
interface TestResult {
  testName: string;
  level: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

/**
 * 测试套件类
 */
class EmailServiceTestSuite {
  private emailService: EmailService;
  private templateManager: EmailTemplateManager;
  private results: TestResult[] = [];

  constructor() {
    this.emailService = createEmailService();
    this.templateManager = new EmailTemplateManager();
  }

  /**
   * 运行测试并记录结果
   */
  private async runTest(
    testName: string,
    level: string,
    testFn: () => Promise<void>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`\\n🧪 运行 ${level} 测试: ${testName}`);
      await testFn();
      
      const duration = Date.now() - startTime;
      this.results.push({
        testName,
        level,
        passed: true,
        duration,
      });
      
      console.log(`✅ ${testName} - 通过 (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.results.push({
        testName,
        level,
        passed: false,
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ ${testName} - 失败 (${duration}ms): ${errorMessage}`);
    }
  }

  /**
   * P0 测试: 基础邮件发送功能
   */
  async runP0Tests(): Promise<void> {
    console.log('\\n🚀 开始 P0 级别测试 - 核心功能');

    // P0-1: 测试邮件服务连接
    await this.runTest('邮件服务连接测试', 'P0', async () => {
      const result = await this.emailService.testConnection();
      if (!result.success) {
        throw new Error(result.error || '连接测试失败');
      }
      console.log('  📡 连接成功，消息ID:', result.messageId);
    });

    // P0-2: 发送邮箱验证邮件（v1版本）
    await this.runTest('发送邮箱验证邮件 (v1)', 'P0', async () => {
      const result = await this.emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123',
        version: 'v1',
      });
      
      if (!result.success) {
        throw new Error(result.error || '邮件发送失败');
      }
      console.log('  📧 邮件发送成功，消息ID:', result.messageId);
    });

    // P0-3: 发送密码重置邮件（v1版本）
    await this.runTest('发送密码重置邮件 (v1)', 'P0', async () => {
      const result = await this.emailService.sendPasswordResetEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        resetPasswordUrl: 'https://example.com/reset?token=reset123',
        version: 'v1',
      });
      
      if (!result.success) {
        throw new Error(result.error || '邮件发送失败');
      }
      console.log('  🔐 密码重置邮件发送成功，消息ID:', result.messageId);
    });
  }

  /**
   * P1 测试: 模板版本控制和内测码集成
   */
  async runP1Tests(): Promise<void> {
    console.log('\\n📋 开始 P1 级别测试 - 主要功能');

    // P1-1: 测试 v2 版本邮件模板
    await this.runTest('发送邮箱验证邮件 (v2)', 'P1', async () => {
      const result = await this.emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户V2',
        verificationUrl: 'https://example.com/verify?token=testv2',
        version: 'v2',
      });
      
      if (!result.success) {
        throw new Error(result.error || 'V2邮件发送失败');
      }
      console.log('  🎨 V2 邮件发送成功，消息ID:', result.messageId);
    });

    // P1-2: 测试带邀请码的邮箱验证邮件
    await this.runTest('发送带邀请码的邮箱验证邮件', 'P1', async () => {
      const result = await this.emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '邀请用户',
        verificationUrl: 'https://example.com/verify?token=invite123',
        inviteCode: 'TESTCODE2025',
        version: 'v1',
      });
      
      if (!result.success) {
        throw new Error(result.error || '带邀请码邮件发送失败');
      }
      console.log('  🎫 带邀请码邮件发送成功，消息ID:', result.messageId);
    });

    // P1-3: 测试模板版本切换
    await this.runTest('模板版本切换测试', 'P1', async () => {
      const originalVersion = this.emailService.getConfig().defaultVersion;
      
      // 切换到 v2
      this.emailService.setDefaultVersion('v2');
      const newConfig = this.emailService.getConfig();
      
      if (newConfig.defaultVersion !== 'v2') {
        throw new Error('版本切换到v2失败');
      }
      
      // 切换回原版本
      this.emailService.setDefaultVersion(originalVersion);
      console.log('  🔄 版本切换测试成功');
    });

    // P1-4: 测试邀请码上下文管理
    await this.runTest('邀请码上下文管理测试', 'P1', async () => {
      const testEmail = '<EMAIL>';
      const testInviteCode = 'CONTEXT123';
      
      // 设置上下文
      inviteCodeContextManager.setContext(testEmail, testInviteCode);
      
      // 获取上下文
      const retrievedCode = inviteCodeContextManager.getContext(testEmail);
      if (retrievedCode !== testInviteCode) {
        throw new Error('邀请码上下文获取失败');
      }
      
      // 清理上下文
      inviteCodeContextManager.cleanupContext(testEmail);
      const clearedCode = inviteCodeContextManager.getContext(testEmail);
      if (clearedCode !== null) {
        throw new Error('邀请码上下文清理失败');
      }
      
      console.log('  🗃️ 邀请码上下文管理测试成功');
    });
  }

  /**
   * P2 测试: 错误处理和边界条件
   */
  async runP2Tests(): Promise<void> {
    console.log('\\n⚠️  开始 P2 级别测试 - 错误处理');

    // P2-1: 测试无效邮箱地址
    await this.runTest('无效邮箱地址处理', 'P2', async () => {
      const result = await this.emailService.sendVerificationEmail({
        userEmail: 'invalid-email',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify',
      });
      
      // 这个测试期望失败
      if (result.success) {
        throw new Error('应该拒绝无效邮箱地址');
      }
      console.log('  ✋ 正确拒绝了无效邮箱地址');
    });

    // P2-2: 测试空URL处理
    await this.runTest('空URL处理', 'P2', async () => {
      const result = await this.emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: '',
      });
      
      // 这个应该仍然发送，但URL会是空的
      if (!result.success) {
        console.log('  ⚠️ 空URL被拒绝（这是预期行为）');
      } else {
        console.log('  📧 空URL邮件发送成功（模板应正确处理）');
      }
    });

    // P2-3: 测试不支持的模板版本
    await this.runTest('不支持的模板版本处理', 'P2', async () => {
      try {
        // @ts-ignore 故意使用不支持的版本
        this.emailService.setDefaultVersion('v999');
        throw new Error('应该拒绝不支持的版本');
      } catch (error) {
        if (error instanceof Error && error.message.includes('不支持的模板版本')) {
          console.log('  ✋ 正确拒绝了不支持的模板版本');
        } else {
          throw error;
        }
      }
    });

    // P2-4: 测试模板渲染边界条件
    await this.runTest('模板渲染边界条件', 'P2', async () => {
      const template = this.templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          // 故意省略其他字段，测试模板的容错性
        },
      });
      
      if (!template.html || !template.subject || !template.text) {
        throw new Error('模板渲染不完整');
      }
      console.log('  🎨 模板容错性测试通过');
    });
  }

  /**
   * P3 测试: 配置管理和监控
   */
  async runP3Tests(): Promise<void> {
    console.log('\\n⚙️ 开始 P3 级别测试 - 配置管理');

    // P3-1: 测试配置获取
    await this.runTest('配置信息获取', 'P3', async () => {
      const config = this.emailService.getConfig();
      
      if (!config.fromEmail || !config.fromName) {
        throw new Error('配置信息不完整');
      }
      
      console.log('  📋 配置信息:', {
        fromEmail: config.fromEmail,
        fromName: config.fromName,
        defaultVersion: config.defaultVersion,
      });
    });

    // P3-2: 测试可用版本列表
    await this.runTest('可用模板版本列表', 'P3', async () => {
      const versions = this.emailService.getAvailableVersions();
      
      if (!versions.includes('v1') || !versions.includes('v2')) {
        throw new Error('版本列表不完整');
      }
      
      console.log('  📋 可用版本:', versions);
    });

    // P3-3: 测试上下文监控
    await this.runTest('邀请码上下文监控', 'P3', async () => {
      const initialCount = inviteCodeContextManager.getContextCount();
      
      // 添加一些测试上下文
      inviteCodeContextManager.setContext('<EMAIL>', 'CODE1');
      inviteCodeContextManager.setContext('<EMAIL>', 'CODE2');
      
      const newCount = inviteCodeContextManager.getContextCount();
      if (newCount !== initialCount + 2) {
        throw new Error('上下文计数不正确');
      }
      
      // 清理
      inviteCodeContextManager.cleanupContext('<EMAIL>');
      inviteCodeContextManager.cleanupContext('<EMAIL>');
      
      console.log('  📊 上下文监控测试通过');
    });

    // P3-4: 测试模板版本检查
    await this.runTest('模板版本可用性检查', 'P3', async () => {
      const isV1Available = this.templateManager.isVersionAvailable('v1');
      const isV2Available = this.templateManager.isVersionAvailable('v2');
      // @ts-ignore
      const isV999Available = this.templateManager.isVersionAvailable('v999');
      
      if (!isV1Available || !isV2Available || isV999Available) {
        throw new Error('版本可用性检查错误');
      }
      
      console.log('  ✅ 版本可用性检查正确');
    });
  }

  /**
   * P4 测试: 性能和并发
   */
  async runP4Tests(): Promise<void> {
    console.log('\\n⚡ 开始 P4 级别测试 - 性能测试');

    // P4-1: 并发邮件发送测试
    await this.runTest('并发邮件发送测试', 'P4', async () => {
      const concurrentCount = 3; // 限制并发数避免触发频率限制
      const promises: Promise<EmailSendResult>[] = [];
      
      for (let i = 0; i < concurrentCount; i++) {
        promises.push(
          this.emailService.sendVerificationEmail({
            userEmail: '<EMAIL>',
            userName: `并发用户${i + 1}`,
            verificationUrl: `https://example.com/verify?concurrent=${i}`,
            inviteCode: `CONCURRENT${i}`,
          })
        );
      }
      
      const results = await Promise.all(promises);
      const successCount = results.filter(r => r.success).length;
      
      if (successCount !== concurrentCount) {
        throw new Error(`并发测试失败: ${successCount}/${concurrentCount} 成功`);
      }
      
      console.log(`  🚀 并发发送 ${concurrentCount} 封邮件成功`);
    });

    // P4-2: 模板渲染性能测试
    await this.runTest('模板渲染性能测试', 'P4', async () => {
      const renderCount = 100;
      const startTime = Date.now();
      
      for (let i = 0; i < renderCount; i++) {
        this.templateManager.render({
          type: 'verification',
          version: i % 2 === 0 ? 'v1' : 'v2',
          data: {
            userEmail: `perf-test-${i}@example.com`,
            userName: `性能测试用户${i}`,
            verificationUrl: `https://example.com/verify?perf=${i}`,
            inviteCode: `PERF${i}`,
          },
        });
      }
      
      const duration = Date.now() - startTime;
      const avgTime = duration / renderCount;
      
      if (avgTime > 10) { // 平均每次渲染不应超过10ms
        throw new Error(`模板渲染性能不佳: 平均 ${avgTime.toFixed(2)}ms/次`);
      }
      
      console.log(`  ⚡ 渲染 ${renderCount} 次模板，平均 ${avgTime.toFixed(2)}ms/次`);
    });

    // P4-3: 内存使用监控（上下文管理）
    await this.runTest('内存使用监控', 'P4', async () => {
      const initialCount = inviteCodeContextManager.getContextCount();
      const testCount = 50;
      
      // 添加大量测试上下文
      for (let i = 0; i < testCount; i++) {
        inviteCodeContextManager.setContext(`memory-test-${i}@test.com`, `CODE${i}`);
      }
      
      const peakCount = inviteCodeContextManager.getContextCount();
      if (peakCount !== initialCount + testCount) {
        throw new Error('内存使用计数错误');
      }
      
      // 清理所有测试上下文
      for (let i = 0; i < testCount; i++) {
        inviteCodeContextManager.cleanupContext(`memory-test-${i}@test.com`);
      }
      
      const finalCount = inviteCodeContextManager.getContextCount();
      if (finalCount !== initialCount) {
        throw new Error('内存清理不完整');
      }
      
      console.log(`  🧠 内存使用监控通过: ${initialCount} -> ${peakCount} -> ${finalCount}`);
    });

    // P4-4: 错误恢复测试
    await this.runTest('错误恢复测试', 'P4', async () => {
      // 模拟网络错误后的恢复
      let errorCount = 0;
      let successCount = 0;
      
      for (let i = 0; i < 5; i++) {
        const result = await this.emailService.sendVerificationEmail({
          userEmail: i === 2 ? 'invalid-email' : '<EMAIL>', // 第3次故意失败
          userName: `恢复测试${i}`,
          verificationUrl: `https://example.com/verify?recovery=${i}`,
        });
        
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
        
        // 短暂延迟避免频率限制
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      if (successCount < 3) { // 至少应该有3次成功
        throw new Error(`错误恢复测试失败: ${successCount} 次成功, ${errorCount} 次失败`);
      }
      
      console.log(`  🛠️ 错误恢复测试通过: ${successCount} 次成功, ${errorCount} 次失败`);
    });
  }

  /**
   * 打印测试总结
   */
  private printSummary(): void {
    console.log('\\n' + '='.repeat(60));
    console.log('📊 测试结果总结');
    console.log('='.repeat(60));

    const levels = ['P0', 'P1', 'P2', 'P3', 'P4'];
    const summary: Record<string, { total: number; passed: number; failed: number }> = {};

    // 统计各级别结果
    levels.forEach(level => {
      const levelResults = this.results.filter(r => r.level === level);
      summary[level] = {
        total: levelResults.length,
        passed: levelResults.filter(r => r.passed).length,
        failed: levelResults.filter(r => !r.passed).length,
      };
    });

    // 打印各级别统计
    levels.forEach(level => {
      const stats = summary[level];
      const status = stats.failed === 0 ? '✅' : '❌';
      console.log(`${status} ${level}: ${stats.passed}/${stats.total} 通过`);
      
      if (stats.failed > 0) {
        const failedTests = this.results.filter(r => r.level === level && !r.passed);
        failedTests.forEach(test => {
          console.log(`  ❌ ${test.testName}: ${test.error}`);
        });
      }
    });

    // 总体统计
    const totalTests = this.results.length;
    const totalPassed = this.results.filter(r => r.passed).length;
    const totalFailed = totalTests - totalPassed;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log('\\n' + '-'.repeat(60));
    console.log(`📈 总体结果: ${totalPassed}/${totalTests} 通过 (${((totalPassed / totalTests) * 100).toFixed(1)}%)`);
    console.log(`⏱️  总耗时: ${totalDuration}ms`);
    console.log(`⚡ 平均耗时: ${(totalDuration / totalTests).toFixed(1)}ms/测试`);

    if (totalFailed > 0) {
      console.log(`\\n❌ 失败的测试:`);
      this.results.filter(r => !r.passed).forEach(test => {
        console.log(`  - [${test.level}] ${test.testName}: ${test.error}`);
      });
    }

    console.log('='.repeat(60));
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 开始运行 Resend 邮件服务 P0-P4 级别测试');
    console.log('⚠️  注意: 请确保 RESEND_API_KEY 环境变量已正确设置');

    try {
      await this.runP0Tests();
      await this.runP1Tests();
      await this.runP2Tests();
      await this.runP3Tests();
      await this.runP4Tests();
    } catch (error) {
      console.error('\\n💥 测试执行过程中发生严重错误:', error);
    }

    this.printSummary();
  }
}

/**
 * 主函数
 */
async function main() {
  // 检查环境变量
  if (!process.env.RESEND_API_KEY) {
    console.error('❌ 错误: 请设置 RESEND_API_KEY 环境变量');
    console.log('💡 提示: export RESEND_API_KEY=your_api_key_here');
    process.exit(1);
  }

  const testSuite = new EmailServiceTestSuite();
  await testSuite.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(error => {
    console.error('💥 测试运行失败:', error);
    process.exit(1);
  });
}

export { EmailServiceTestSuite };