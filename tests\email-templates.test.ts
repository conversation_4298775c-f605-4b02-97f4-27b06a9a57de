import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { EmailTemplateManager } from '../src/services/email-templates.js';
import { EmailTemplateData, EmailTemplateVersion } from '../src/types/email.js';

describe('邮件模板渲染测试', () => {
  let templateManager: EmailTemplateManager;

  beforeEach(() => {
    templateManager = new EmailTemplateManager();
  });

  describe('模板版本管理', () => {
    it('应该正确识别可用的模板版本', () => {
      expect(templateManager.isVersionAvailable('v1')).toBe(true);
      expect(templateManager.isVersionAvailable('v2')).toBe(true);
      expect(templateManager.isVersionAvailable('v999' as EmailTemplateVersion)).toBe(false);
    });

    it('应该返回所有可用版本列表', () => {
      const versions = templateManager.getAvailableVersions();
      
      expect(Array.isArray(versions)).toBe(true);
      expect(versions.length).toBeGreaterThan(0);
      expect(versions).toContain('v1');
      expect(versions).toContain('v2');
    });

    it('应该按版本号排序返回版本列表', () => {
      const versions = templateManager.getAvailableVersions();
      
      // 检查版本是否按顺序排列
      const versionNumbers = versions.map(v => parseInt(v.substring(1)));
      const sortedVersionNumbers = [...versionNumbers].sort((a, b) => a - b);
      
      expect(versionNumbers).toEqual(sortedVersionNumbers);
    });
  });

  describe('邮箱验证模板渲染', () => {
    const baseTemplateData: EmailTemplateData = {
      userEmail: '<EMAIL>',
      userName: '测试用户',
      verificationUrl: 'https://example.com/verify?token=test123'
    };

    describe('V1版本模板', () => {
      it('应该正确渲染基础邮箱验证模板', () => {
        const result = templateManager.render({
          type: 'verification',
          version: 'v1',
          data: baseTemplateData
        });

        // 检查模板结构完整性
        expect(result.subject).toBeTruthy();
        expect(result.html).toBeTruthy();
        expect(result.text).toBeTruthy();

        // 检查主题行
        expect(result.subject).toContain('邮箱验证');
        expect(result.subject).toContain('SpecificAI');

        // 检查HTML内容
        expect(result.html).toContain('测试用户');
        expect(result.html).toContain('<EMAIL>');
        expect(result.html).toContain('https://example.com/verify?token=test123');
        expect(result.html).toContain('验证邮箱');

        // 检查文本内容
        expect(result.text).toContain('测试用户');
        expect(result.text).toContain('<EMAIL>');
        expect(result.text).toContain('https://example.com/verify?token=test123');
      });

      it('应该在模板中包含邀请码信息', () => {
        const dataWithInviteCode = {
          ...baseTemplateData,
          inviteCode: 'TESTCODE2025'
        };

        const result = templateManager.render({
          type: 'verification',
          version: 'v1',
          data: dataWithInviteCode
        });

        expect(result.html).toContain('TESTCODE2025');
        expect(result.html).toContain('邀请码');
        expect(result.text).toContain('TESTCODE2025');
        expect(result.text).toContain('邀请码');
      });

      it('应该正确处理缺少用户名的情况', () => {
        const dataWithoutName = {
          userEmail: '<EMAIL>',
          verificationUrl: 'https://example.com/verify?token=test123'
        };

        const result = templateManager.render({
          type: 'verification',
          version: 'v1',
          data: dataWithoutName
        });

        expect(result.subject).toBeTruthy();
        expect(result.html).toBeTruthy();
        expect(result.text).toBeTruthy();

        // 应该使用邮箱地址作为默认称呼或使用通用称呼
        expect(result.html).toContain('<EMAIL>');
      });

      it('应该转义HTML特殊字符', () => {
        const dataWithSpecialChars = {
          ...baseTemplateData,
          userName: '测试用户<script>alert("xss")</script>',
          userEmail: 'test<script>@example.com'
        };

        const result = templateManager.render({
          type: 'verification',
          version: 'v1',
          data: dataWithSpecialChars
        });

        // 应该转义危险的HTML内容
        expect(result.html).not.toContain('<script>alert("xss")</script>');
        expect(result.html).not.toContain('test<script>@example.com');
        
        // 但正常内容应该保留
        expect(result.html).toContain('测试用户');
        expect(result.html).toContain('@example.com');
      });
    });

    describe('V2版本模板', () => {
      it('应该正确渲染增强版邮箱验证模板', () => {
        const result = templateManager.render({
          type: 'verification',
          version: 'v2',
          data: baseTemplateData
        });

        // 检查模板结构完整性
        expect(result.subject).toBeTruthy();
        expect(result.html).toBeTruthy();
        expect(result.text).toBeTruthy();

        // V2版本应该有更现代的设计元素
        expect(result.html).toContain('测试用户');
        expect(result.html).toContain('<EMAIL>');
        expect(result.html).toContain('https://example.com/verify?token=test123');
        
        // V2版本可能有不同的样式类或结构
        expect(result.html.length).toBeGreaterThan(100); // 确保不是空模板
      });

      it('应该与V1版本有不同的样式设计', () => {
        const v1Result = templateManager.render({
          type: 'verification',
          version: 'v1',
          data: baseTemplateData
        });

        const v2Result = templateManager.render({
          type: 'verification',
          version: 'v2',
          data: baseTemplateData
        });

        // 两个版本的HTML内容应该不完全相同（设计不同）
        expect(v1Result.html).not.toBe(v2Result.html);
        
        // 但都应该包含相同的基本信息
        expect(v1Result.html).toContain('测试用户');
        expect(v2Result.html).toContain('测试用户');
        expect(v1Result.html).toContain('<EMAIL>');
        expect(v2Result.html).toContain('<EMAIL>');
      });

      it('应该支持相同的数据字段', () => {
        const dataWithAllFields = {
          ...baseTemplateData,
          inviteCode: 'V2CODE2025'
        };

        const result = templateManager.render({
          type: 'verification',
          version: 'v2',
          data: dataWithAllFields
        });

        expect(result.html).toContain('测试用户');
        expect(result.html).toContain('<EMAIL>');
        expect(result.html).toContain('https://example.com/verify?token=test123');
        expect(result.html).toContain('V2CODE2025');
      });
    });
  });

  describe('密码重置模板渲染', () => {
    const resetTemplateData: EmailTemplateData = {
      userEmail: '<EMAIL>',
      userName: '测试用户',
      resetPasswordUrl: 'https://example.com/reset?token=reset123'
    };

    it('应该正确渲染密码重置模板（V1）', () => {
      const result = templateManager.render({
        type: 'password-reset',
        version: 'v1',
        data: resetTemplateData
      });

      expect(result.subject).toBeTruthy();
      expect(result.html).toBeTruthy();
      expect(result.text).toBeTruthy();

      // 检查主题行
      expect(result.subject).toContain('密码重置');

      // 检查内容
      expect(result.html).toContain('测试用户');
      expect(result.html).toContain('<EMAIL>');
      expect(result.html).toContain('https://example.com/reset?token=reset123');
      expect(result.html).toContain('重置密码');
    });

    it('应该正确渲染密码重置模板（V2）', () => {
      const result = templateManager.render({
        type: 'password-reset',
        version: 'v2',
        data: resetTemplateData
      });

      expect(result.subject).toBeTruthy();
      expect(result.html).toBeTruthy();
      expect(result.text).toBeTruthy();

      expect(result.html).toContain('测试用户');
      expect(result.html).toContain('<EMAIL>');
      expect(result.html).toContain('https://example.com/reset?token=reset123');
    });

    it('应该处理密码重置模板中的安全警告', () => {
      const result = templateManager.render({
        type: 'password-reset',
        version: 'v1',
        data: resetTemplateData
      });

      // 密码重置邮件应该包含安全提示
      expect(result.html).toContain('安全');
      expect(result.text).toContain('安全');
    });
  });

  describe('模板渻染错误处理', () => {
    it('应该处理不支持的模板类型', () => {
      expect(() => {
        templateManager.render({
          type: 'unsupported-type' as any,
          version: 'v1',
          data: baseTemplateData
        });
      }).toThrow();
    });

    it('应该处理不支持的模板版本', () => {
      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v999' as EmailTemplateVersion,
          data: baseTemplateData
        });
      }).toThrow();
    });

    it('应该处理缺少必要数据字段', () => {
      // 测试缺少邮箱地址
      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v1',
          data: {
            userName: '测试用户',
            verificationUrl: 'https://example.com/verify'
          } as any
        });
      }).toThrow();

      // 测试缺少验证URL
      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v1',
          data: {
            userEmail: '<EMAIL>',
            userName: '测试用户'
          } as any
        });
      }).toThrow();
    });

    it('应该处理空的数据字段', () => {
      const emptyData = {
        userEmail: '',
        userName: '',
        verificationUrl: ''
      };

      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v1',
          data: emptyData
        });
      }).toThrow();
    });

    it('应该处理null或undefined数据', () => {
      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v1',
          data: null as any
        });
      }).toThrow();

      expect(() => {
        templateManager.render({
          type: 'verification',
          version: 'v1',
          data: undefined as any
        });
      }).toThrow();
    });
  });

  describe('模板渲染性能测试', () => {
    it('单次模板渲染应该在合理时间内完成', () => {
      const startTime = Date.now();
      
      templateManager.render({
        type: 'verification',
        version: 'v1',
        data: baseTemplateData
      });
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(50); // 应该在50ms内完成
    });

    it('批量模板渲染应该保持良好性能', () => {
      const startTime = Date.now();
      const renderCount = 100;
      
      for (let i = 0; i < renderCount; i++) {
        templateManager.render({
          type: i % 2 === 0 ? 'verification' : 'password-reset',
          version: i % 2 === 0 ? 'v1' : 'v2',
          data: {
            userEmail: `test${i}@example.com`,
            userName: `测试用户${i}`,
            verificationUrl: `https://example.com/verify?token=test${i}`,
            resetPasswordUrl: `https://example.com/reset?token=reset${i}`,
            inviteCode: `CODE${i}`
          }
        });
      }
      
      const duration = Date.now() - startTime;
      const avgTime = duration / renderCount;
      
      expect(avgTime).toBeLessThan(5); // 平均每次渲染应该在5ms内
    });

    it('应该能处理包含大量数据的模板', () => {
      const largeData = {
        userEmail: '<EMAIL>',
        userName: '测试用户' + 'x'.repeat(1000), // 很长的用户名
        verificationUrl: 'https://example.com/verify?token=' + 'a'.repeat(1000), // 很长的URL
        inviteCode: 'CODE' + '1'.repeat(100) // 很长的邀请码
      };

      const startTime = Date.now();
      
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: largeData
      });
      
      const duration = Date.now() - startTime;
      
      expect(result.html).toBeTruthy();
      expect(result.text).toBeTruthy();
      expect(duration).toBeLessThan(100); // 即使数据量大也应该在100ms内完成
    });
  });

  describe('模板内容验证', () => {
    it('生成的HTML应该是有效的HTML结构', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: baseTemplateData
      });

      const html = result.html;
      
      // 检查基本HTML结构
      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html');
      expect(html).toContain('<head>');
      expect(html).toContain('<body>');
      expect(html).toContain('</html>');
      
      // 检查必要的meta标签
      expect(html).toContain('<meta charset="utf-8">');
      expect(html).toContain('<meta name="viewport"');
    });

    it('生成的文本版本应该包含必要信息', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: baseTemplateData
      });

      const text = result.text;
      
      // 文本版本应该包含所有重要信息
      expect(text).toContain('测试用户');
      expect(text).toContain('<EMAIL>');
      expect(text).toContain('https://example.com/verify?token=test123');
      expect(text).toContain('验证');
      
      // 文本版本不应该包含HTML标签
      expect(text).not.toContain('<');
      expect(text).not.toContain('>');
    });

    it('主题行应该简洁且信息明确', () => {
      const verificationResult = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: baseTemplateData
      });

      const resetResult = templateManager.render({
        type: 'password-reset',
        version: 'v1',
        data: {
          ...baseTemplateData,
          resetPasswordUrl: 'https://example.com/reset?token=reset123'
        }
      });

      // 主题行应该合理长度
      expect(verificationResult.subject.length).toBeGreaterThan(5);
      expect(verificationResult.subject.length).toBeLessThan(100);
      expect(resetResult.subject.length).toBeGreaterThan(5);
      expect(resetResult.subject.length).toBeLessThan(100);

      // 主题行应该包含关键词
      expect(verificationResult.subject.toLowerCase()).toContain('验证');
      expect(resetResult.subject.toLowerCase()).toContain('密码');
    });

    it('应该包含适当的CSS样式', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v2', // V2版本应该有更丰富的样式
        data: baseTemplateData
      });

      const html = result.html;
      
      // 应该包含CSS样式
      expect(html).toContain('<style>');
      expect(html).toContain('</style>');
      
      // 或者内联样式
      expect(html.includes('style=') || html.includes('<style>')).toBe(true);
    });

    it('应该具有良好的移动端适配', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v2',
        data: baseTemplateData
      });

      const html = result.html;
      
      // 应该包含viewport meta标签
      expect(html).toContain('name="viewport"');
      expect(html).toContain('width=device-width');
      
      // 应该有适当的响应式样式
      expect(html.includes('@media') || html.includes('max-width')).toBe(true);
    });
  });

  describe('本地化和国际化', () => {
    it('应该支持中文内容', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          userName: '张三',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      expect(result.html).toContain('张三');
      expect(result.text).toContain('张三');
      expect(result.subject).toContain('验证');
    });

    it('应该正确处理特殊字符和emoji', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          userName: '用户😊',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      expect(result.html).toContain('用户😊');
      expect(result.text).toContain('用户😊');
    });

    it('HTML应该包含正确的语言和编码声明', () => {
      const result = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: baseTemplateData
      });

      const html = result.html;
      
      expect(html).toContain('charset="utf-8"');
      expect(html).toContain('lang="zh-CN"');
    });
  });

  const baseTemplateData: EmailTemplateData = {
    userEmail: '<EMAIL>',
    userName: '测试用户',
    verificationUrl: 'https://example.com/verify?token=test123'
  };
});