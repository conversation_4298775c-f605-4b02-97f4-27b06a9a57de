/**
 * 邀请相关的API路由
 * 处理邮件邀请的发送和管理
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { emailService } from '../services/email-service.js';
import { createEmailInviteService } from '../services/email-invite-service.js';
import { inviteCodeService } from '../services/invite-code.js';
import { formatSuccessResponse, formatErrorResponse } from '../middleware/response-formatter.js';

const emailInviteService = createEmailInviteService(emailService);

// 验证邀请码的schema
const inviteCodeSchema = z.object({
  code: z.string().min(1, '邀请码不能为空').max(50, '邀请码长度不能超过50个字符')
});

// 发送邀请邮件的schema
const sendInviteSchema = z.object({
  recipientEmail: z.string().email('邮箱格式不正确'),
  recipientName: z.string().optional(),
  inviterName: z.string().optional(),
  companyName: z.string().optional(),
  inviteCode: z.string().min(1, '邀请码不能为空'),
  baseUrl: z.string().url('基础URL格式不正确').optional(),
  language: z.enum(['zh-CN', 'en-US', 'ja-JP', 'ko-KR']).optional().default('zh-CN'),
  version: z.enum(['v1', 'v2', 'v3']).optional().default('v3')
});

// 批量发送邀请邮件的schema
const batchInviteSchema = z.object({
  invites: z.array(sendInviteSchema).min(1, '至少需要一个邀请').max(50, '一次最多发送50个邀请'),
  defaultInviterName: z.string().optional(),
  defaultCompanyName: z.string().optional(),
  defaultBaseUrl: z.string().url('基础URL格式不正确').optional(),
  defaultLanguage: z.enum(['zh-CN', 'en-US', 'ja-JP', 'ko-KR']).optional().default('zh-CN'),
  defaultVersion: z.enum(['v1', 'v2', 'v3']).optional().default('v3')
});

/**
 * 注册邀请相关路由
 */
export async function registerInviteRoutes(app: FastifyInstance) {
  /**
   * 验证邀请码
   * POST /api/invites/verify-code
   */
  app.post('/verify-code', async (request: FastifyRequest<{ Body: z.infer<typeof inviteCodeSchema> }>, reply: FastifyReply) => {
    try {
      console.log('🔍 收到邀请码验证请求:', request.body);

      const { code } = inviteCodeSchema.parse(request.body);

      const result = await inviteCodeService.verifyCode(code);

      if (result.valid) {
        console.log(`✅ 邀请码验证成功: ${code}`);
        return reply.status(200).send(formatSuccessResponse({
          valid: true,
          message: '邀请码有效',
          code
        }));
      } else {
        console.log(`❌ 邀请码验证失败: ${code}, 错误: ${result.error}`);
        return reply.status(400).send(formatErrorResponse(
          result.error || '邀请码无效',
          400,
          { valid: false, code }
        ));
      }
    } catch (error) {
      console.error('❌ 验证邀请码异常:', error);
      
      if (error instanceof z.ZodError) {
        return reply.status(400).send(formatErrorResponse(
          '请求参数错误',
          400,
          { valid: false, errors: error.errors }
        ));
      }

      return reply.status(500).send(formatErrorResponse(
        '服务器内部错误',
        500,
        { valid: false }
      ));
    }
  });

  /**
   * 发送邀请邮件
   * POST /api/invites/send-email
   */
  app.post('/send-email', async (request: FastifyRequest<{ Body: z.infer<typeof sendInviteSchema> }>, reply: FastifyReply) => {
    try {
      console.log('📧 收到发送邀请邮件请求:', {
        ...request.body,
        // 隐藏敏感信息用于日志记录
        recipientEmail: request.body.recipientEmail ? '***@***.***' : undefined,
        inviteCode: request.body.inviteCode ? '***' : undefined
      });

      const data = sendInviteSchema.parse(request.body);
      
      // 生成邀请链接
      const baseUrl = data.baseUrl || process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
      const inviteUrl = emailInviteService.generateInviteUrl(baseUrl, data.inviteCode, {
        utm_source: 'email',
        utm_medium: 'invite',
        utm_campaign: 'user_invite'
      });

      // 发送邀请邮件
      const result = await emailInviteService.sendInviteEmail({
        recipientEmail: data.recipientEmail,
        recipientName: data.recipientName,
        inviterName: data.inviterName,
        companyName: data.companyName,
        inviteCode: data.inviteCode,
        inviteUrl,
        language: data.language,
        version: data.version
      });

      if (result.success) {
        console.log(`✅ 邀请邮件发送成功: ${data.recipientEmail}`);
        return reply.status(200).send(formatSuccessResponse({
          success: true,
          message: '邀请邮件发送成功',
          messageId: result.messageId,
          inviteCode: result.inviteCode,
          inviteUrl: result.inviteUrl
        }));
      } else {
        console.log(`❌ 邀请邮件发送失败: ${data.recipientEmail}, 错误: ${result.error}`);
        return reply.status(400).send(formatErrorResponse(
          result.error || '邮件发送失败',
          400,
          { success: false }
        ));
      }
    } catch (error) {
      console.error('❌ 发送邀请邮件异常:', error);
      
      if (error instanceof z.ZodError) {
        return reply.status(400).send(formatErrorResponse(
          '请求参数错误',
          400,
          { success: false, errors: error.errors }
        ));
      }

      return reply.status(500).send(formatErrorResponse(
        '服务器内部错误',
        500,
        { success: false }
      ));
    }
  });

  /**
   * 批量发送邀请邮件
   * POST /api/invites/send-batch
   */
  app.post('/send-batch', async (request: FastifyRequest<{ Body: z.infer<typeof batchInviteSchema> }>, reply: FastifyReply) => {
    try {
      console.log(`📧 收到批量发送邀请邮件请求，数量: ${request.body.invites?.length}`);

      const data = batchInviteSchema.parse(request.body);
      
      // 准备邀请数据
      const baseUrl = data.defaultBaseUrl || process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
      const invites = data.invites.map(invite => ({
        recipientEmail: invite.recipientEmail,
        recipientName: invite.recipientName,
        inviterName: invite.inviterName || data.defaultInviterName,
        companyName: invite.companyName || data.defaultCompanyName,
        inviteCode: invite.inviteCode,
        inviteUrl: emailInviteService.generateInviteUrl(baseUrl, invite.inviteCode, {
          utm_source: 'email',
          utm_medium: 'batch_invite',
          utm_campaign: 'bulk_user_invite'
        }),
        language: invite.language || data.defaultLanguage,
        version: invite.version || data.defaultVersion
      }));

      // 批量发送邮件
      const result = await emailInviteService.sendBatchInviteEmails(invites);

      console.log('📊 批量邀请邮件发送完成:', result.summary);

      return reply.status(200).send(formatSuccessResponse({
        success: true,
        message: `批量邀请完成，成功 ${result.summary.successful} 个，失败 ${result.summary.failed} 个`,
        summary: result.summary,
        successful: result.successful.map(r => ({
          recipientEmail: r.inviteCode, // 不暴露邮箱地址
          messageId: r.messageId,
          success: r.success
        })),
        failed: result.failed.map(r => ({
          inviteCode: r.inviteCode,
          error: r.error,
          success: r.success
        }))
      }));
    } catch (error) {
      console.error('❌ 批量发送邀请邮件异常:', error);
      
      if (error instanceof z.ZodError) {
        return reply.status(400).send(formatErrorResponse(
          '请求参数错误',
          400,
          { success: false, errors: error.errors }
        ));
      }

      return reply.status(500).send(formatErrorResponse(
        '服务器内部错误',
        500,
        { success: false }
      ));
    }
  });

  /**
   * 获取邀请邮件统计信息
   * GET /api/invites/stats?hours=24
   */
  app.get('/stats', async (request: FastifyRequest<{ Querystring: { hours?: string } }>, reply: FastifyReply) => {
    try {
      const hours = parseInt(request.query.hours || '24') || 24;
      
      if (hours < 1 || hours > 720) { // 最多30天
        return reply.status(400).send(formatErrorResponse(
          '时间范围必须在1-720小时之间',
          400,
          { success: false }
        ));
      }

      const stats = await emailInviteService.getInviteEmailStats(hours);

      console.log(`📊 获取邀请邮件统计信息 (${hours}小时):`, stats);

      return reply.status(200).send(formatSuccessResponse({
        success: true,
        message: '获取统计信息成功',
        data: {
          timeRange: `${hours} 小时`,
          ...stats
        }
      }));
    } catch (error) {
      console.error('❌ 获取邀请邮件统计异常:', error);
      
      return reply.status(500).send(formatErrorResponse(
        '服务器内部错误',
        500,
        { success: false }
      ));
    }
  });

  /**
   * 生成邀请链接
   * POST /api/invites/generate-url
   */
  app.post('/generate-url', async (request: FastifyRequest<{ Body: { inviteCode: string; baseUrl?: string; additionalParams?: any } }>, reply: FastifyReply) => {
    try {
      const { inviteCode, baseUrl, additionalParams } = request.body;

      if (!inviteCode) {
        return reply.status(400).send(formatErrorResponse(
          '邀请码不能为空',
          400,
          { success: false }
        ));
      }

      // 验证邀请码
      const verification = await inviteCodeService.verifyCode(inviteCode);
      if (!verification.valid) {
        return reply.status(400).send(formatErrorResponse(
          `邀请码无效: ${verification.error}`,
          400,
          { success: false }
        ));
      }

      const finalBaseUrl = baseUrl || process.env.FRONTEND_BASE_URL || 'http://localhost:3000';
      const inviteUrl = emailInviteService.generateInviteUrl(finalBaseUrl, inviteCode, {
        utm_source: 'api',
        utm_medium: 'direct',
        ...additionalParams
      });

      console.log(`🔗 生成邀请链接: ${inviteCode}`);

      return reply.status(200).send(formatSuccessResponse({
        success: true,
        message: '生成邀请链接成功',
        data: {
          inviteCode,
          inviteUrl,
          valid: true
        }
      }));
    } catch (error) {
      console.error('❌ 生成邀请链接异常:', error);
      
      return reply.status(500).send(formatErrorResponse(
        '服务器内部错误',
        500,
        { success: false }
      ));
    }
  });
}