#!/usr/bin/env node

/**
 * 数据库初始化检查脚本
 * 在容器启动时检查数据库表是否存在，如果不存在则创建
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';
import pkg from 'pg';
const { Client } = pkg;

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 数据库连接配置
const DB_CONFIG = {
  connectionString: process.env.DATABASE_URL,
  // 如果DATABASE_URL不存在，使用单独的环境变量
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'specific_ai_auth',
  user: process.env.DB_USER || 'specific_ai_auth',
  password: process.env.DB_PASSWORD,
};

// 日志函数
const log = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`),
};

/**
 * 检查数据库连接
 */
async function checkDatabaseConnection(client) {
  try {
    await client.query('SELECT 1');
    log.info('Database connection successful');
    return true;
  } catch (error) {
    log.error(`Database connection failed: ${error.message}`);
    return false;
  }
}

/**
 * 检查schema是否存在
 */
async function checkSchemaExists(client, schemaName) {
  try {
    const result = await client.query(
      'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
      [schemaName]
    );
    return result.rows.length > 0;
  } catch (error) {
    log.error(`Error checking schema: ${error.message}`);
    return false;
  }
}

/**
 * 检查必需的表是否存在
 */
async function checkRequiredTables(client) {
  const requiredTables = [
    'user',
    'session',
    'account',
    'verification',
    'organization',
    'member',
    'invitation',
    'subscription',
    'email_log',
    'rate_limit',
    'request_log',
    'security_event',
    'jwks' // JWT插件需要的表
  ];

  try {
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'auth' 
      AND table_type = 'BASE TABLE'
    `);
    
    const existingTables = result.rows.map(row => row.table_name);
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    log.info(`Found ${existingTables.length} existing tables: ${existingTables.join(', ')}`);
    
    if (missingTables.length > 0) {
      log.warn(`Missing tables: ${missingTables.join(', ')}`);
      return false;
    }
    
    log.success('All required tables exist');
    return true;
  } catch (error) {
    log.error(`Error checking tables: ${error.message}`);
    return false;
  }
}

/**
 * 执行初始化SQL脚本
 */
async function runInitializationScript(client) {
  try {
    log.info('Starting database initialization...');
    
    // 读取SQL文件
    const sqlPath = join(__dirname, 'init-database.sql');
    const sqlContent = readFileSync(sqlPath, 'utf8');
    
    // 分割SQL语句（简单实现，适用于我们的脚本）
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    log.info(`Executing ${statements.length} SQL statements...`);
    
    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          await client.query(statement);
          if (i % 10 === 0) {
            log.info(`Executed ${i + 1}/${statements.length} statements...`);
          }
        } catch (error) {
          // 某些语句可能因为对象已存在而失败，这是可以接受的
          if (!error.message.includes('already exists')) {
            log.warn(`Statement failed (non-critical): ${error.message}`);
          }
        }
      }
    }
    
    log.success('Database initialization completed successfully');
    return true;
  } catch (error) {
    log.error(`Database initialization failed: ${error.message}`);
    return false;
  }
}

/**
 * 验证数据库初始化结果
 */
async function validateInitialization(client) {
  try {
    log.info('Validating database initialization...');
    
    // 检查schema
    const schemaExists = await checkSchemaExists(client, 'auth');
    if (!schemaExists) {
      log.error('Auth schema not found after initialization');
      return false;
    }
    
    // 检查表
    const tablesExist = await checkRequiredTables(client);
    if (!tablesExist) {
      log.error('Some required tables missing after initialization');
      return false;
    }
    
    // 检查一些关键索引
    const indexResult = await client.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE schemaname = 'auth' 
      AND indexname IN ('idx_user_email', 'idx_session_token', 'idx_email_log_user_id')
    `);
    
    if (indexResult.rows.length < 3) {
      log.warn('Some indexes may be missing, but this is not critical');
    }
    
    log.success('Database validation completed successfully');
    return true;
  } catch (error) {
    log.error(`Validation failed: ${error.message}`);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  let client;
  
  try {
    log.info('Starting database initialization check...');
    
    // 验证环境变量
    if (!process.env.DATABASE_URL && !process.env.DB_PASSWORD) {
      log.error('DATABASE_URL or DB_PASSWORD environment variable is required');
      process.exit(1);
    }
    
    // 创建数据库客户端
    client = new Client(DB_CONFIG);
    await client.connect();
    
    // 检查数据库连接
    const connectionOk = await checkDatabaseConnection(client);
    if (!connectionOk) {
      log.error('Cannot proceed without database connection');
      process.exit(1);
    }
    
    // 检查schema是否存在
    const schemaExists = await checkSchemaExists(client, 'auth');
    
    // 检查表是否存在
    const tablesExist = schemaExists ? await checkRequiredTables(client) : false;
    
    if (schemaExists && tablesExist) {
      log.success('Database is already initialized. No action needed.');
    } else {
      log.info('Database needs initialization. Running setup script...');
      
      const initSuccess = await runInitializationScript(client);
      if (!initSuccess) {
        log.error('Database initialization failed');
        process.exit(1);
      }
      
      const validationSuccess = await validateInitialization(client);
      if (!validationSuccess) {
        log.error('Database validation failed after initialization');
        process.exit(1);
      }
    }
    
    log.success('Database initialization check completed successfully');
    
  } catch (error) {
    log.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
    }
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  log.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log.error(`Uncaught Exception: ${error.message}`);
  process.exit(1);
});

// 运行主函数
main();