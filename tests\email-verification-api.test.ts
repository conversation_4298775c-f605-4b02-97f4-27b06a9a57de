import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, jest } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import { buildApp } from '../src/app.js';
import { EmailTemplateManager } from '../src/services/email-templates.js';

// 模拟数据库
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  name: '测试用户',
  emailVerified: false
};

const mockVerifiedUser = {
  id: 'verified-user-id',
  email: '<EMAIL>',
  name: '已验证用户',
  emailVerified: true
};

jest.mock('../src/lib/drizzle.js', () => ({
  db: {
    insert: jest.fn().mockReturnValue({
      values: jest.fn().mockResolvedValue({ rowCount: 1 })
    }),
    update: jest.fn().mockReturnValue({
      set: jest.fn().mockReturnValue({
        where: jest.fn().mockResolvedValue({ rowCount: 1 })
      })
    }),
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockImplementation((limit: number) => {
            if (limit === 1) {
              // 根据不同的测试场景返回不同的用户数据
              return Promise.resolve([mockUser]);
            }
            return Promise.resolve([]);
          })
        })
      })
    })
  }
}));

// 模拟Better Auth
jest.mock('../src/auth.js', () => ({
  auth: {
    api: {
      getSession: jest.fn().mockResolvedValue({
        user: mockUser
      }),
      sendVerificationEmail: jest.fn().mockResolvedValue({
        success: true,
        data: { id: 'mock-email-id' }
      }),
      verifyEmail: jest.fn().mockResolvedValue({
        user: mockVerifiedUser
      })
    }
  }
}));

// 模拟邮件服务
jest.mock('../src/services/email-service.js', () => ({
  emailService: {
    sendVerificationEmail: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'mock-message-id',
      logId: 'mock-log-id'
    })
  }
}));

describe('邮箱验证 API 集成测试', () => {  
  let app: FastifyInstance;

  beforeAll(async () => {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test';
    process.env.BETTER_AUTH_URL = 'http://localhost:10086';
    process.env.RESEND_API_KEY = 'test-api-key';
    
    // 构建应用实例
    app = await buildApp();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/resend-verification', () => {
    it('应该成功重新发送验证邮件', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'authorization': 'Bearer mock-token',
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toContain('验证邮件已重新发送');
      expect(body.data).toHaveProperty('email', mockUser.email);
      expect(body.data).toHaveProperty('resendCount');
    });

    it('应该拒绝未认证的请求', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('UNAUTHORIZED');
      expect(body.message).toBe('请先登录');
    });

    it('应该拒绝已验证邮箱的用户', async () => {
      // 模拟已验证的用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'authorization': 'Bearer mock-token',
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('ALREADY_VERIFIED');
      expect(body.message).toBe('邮箱已经验证过了');
    });

    it('应该处理邮件发送失败的情况', async () => {
      // 模拟邮件发送失败
      const { auth } = require('../src/auth.js');
      auth.api.sendVerificationEmail.mockRejectedValueOnce(new Error('邮件服务不可用'));

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'authorization': 'Bearer mock-token',
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(500);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('EMAIL_SEND_FAILED');
      expect(body.message).toContain('发送验证邮件失败');
    });
  });

  describe('GET /api/auth/verify-email-status', () => {
    it('应该返回用户的邮箱验证状态', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status',
        headers: {
          'authorization': 'Bearer mock-token'
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('emailVerified', false);
      expect(body.data).toHaveProperty('email', mockUser.email);
      expect(body.data).toHaveProperty('userId', mockUser.id);
      expect(body.data).toHaveProperty('verificationRequired', true);
    });

    it('应该返回已验证用户的状态', async () => {
      // 模拟已验证的用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status',
        headers: {
          'authorization': 'Bearer mock-token'
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('emailVerified', true);
      expect(body.data).toHaveProperty('email', mockVerifiedUser.email);
      expect(body.data).toHaveProperty('userId', mockVerifiedUser.id);
      expect(body.data).toHaveProperty('verificationRequired', false);
    });

    it('应该拒绝未认证的请求', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status'
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('UNAUTHORIZED');
    });

    it('应该处理用户不存在的情况', async () => {
      // 模拟用户不存在
      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]) // 空数组表示用户不存在
          })
        })
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status',
        headers: {
          'authorization': 'Bearer mock-token'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('USER_NOT_FOUND');
    });
  });

  describe('GET /api/auth/verify-email-callback', () => {
    it('应该成功处理有效的验证令牌', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=valid-token&redirect=https://example.com/dashboard'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      // 检查HTML内容
      const html = response.body;
      expect(html).toContain('邮箱验证成功');
      expect(html).toContain(mockVerifiedUser.name);
      expect(html).toContain(mockVerifiedUser.email);
      expect(html).toContain('https://example.com/dashboard');
    });

    it('应该处理无效的验证令牌', async () => {
      // 模拟无效令牌
      const { auth } = require('../src/auth.js');
      auth.api.verifyEmail.mockResolvedValueOnce({
        user: null // 无效令牌返回null
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=invalid-token'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      // 检查错误HTML内容
      const html = response.body;
      expect(html).toContain('验证令牌无效或已过期');
      expect(html).toContain('重新发送验证邮件');
    });

    it('应该处理缺少令牌的情况', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      // 检查错误HTML内容
      const html = response.body;
      expect(html).toContain('缺少验证令牌');
    });

    it('应该使用默认重定向地址', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=valid-token'
      });

      expect(response.statusCode).toBe(200);
      
      // 检查HTML中包含默认重定向地址
      const html = response.body;
      expect(html).toContain('/dashboard');
    });

    it('应该处理Better Auth验证失败', async () => {
      // 模拟验证过程抛出异常
      const { auth } = require('../src/auth.js');
      auth.api.verifyEmail.mockRejectedValueOnce(new Error('验证服务异常'));

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=valid-token'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      // 检查错误HTML内容
      const html = response.body;
      expect(html).toContain('邮箱验证失败');
      expect(html).toContain('重新发送验证邮件');
    });
  });

  describe('邮箱验证中间件测试', () => {
    // 这里需要一个受保护的端点来测试中间件
    // 假设有一个需要邮箱验证的端点

    it('应该允许已验证用户访问受保护的端点', async () => {
      // 模拟已验证用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      // 这里需要实际的受保护端点，暂时跳过
      // const response = await app.inject({
      //   method: 'GET',
      //   url: '/api/protected-endpoint',
      //   headers: {
      //     'authorization': 'Bearer mock-token'
      //   }
      // });

      // expect(response.statusCode).toBe(200);
      expect(true).toBe(true); // 占位符
    });

    it('应该阻止未验证用户访问受保护的端点', async () => {
      // 模拟未验证用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockUser])
          })
        })
      });

      // 这里需要实际的受保护端点，暂时跳过
      // const response = await app.inject({
      //   method: 'GET',
      //   url: '/api/protected-endpoint',
      //   headers: {
      //     'authorization': 'Bearer mock-token'
      //   }
      // });

      // expect(response.statusCode).toBe(403);
      
      // const body = JSON.parse(response.body);
      // expect(body.error).toBe('EMAIL_NOT_VERIFIED');
      expect(true).toBe(true); // 占位符
    });
  });

  describe('频率限制测试', () => {
    it('应该允许正常频率的验证邮件请求', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'authorization': 'Bearer mock-token',
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(200);
      
      // 检查频率限制头部
      expect(response.headers).toHaveProperty('x-ratelimit-limit');
      expect(response.headers).toHaveProperty('x-ratelimit-remaining');
      expect(response.headers).toHaveProperty('x-ratelimit-reset');
    });

    it('应该在超过频率限制时返回429', async () => {
      // 这个测试需要模拟频率限制的触发
      // 实际实现需要根据具体的频率限制逻辑来调整
      
      // 模拟连续多次请求
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(
          app.inject({
            method: 'POST',
            url: '/api/auth/resend-verification',
            headers: {
              'authorization': 'Bearer mock-token',
              'content-type': 'application/json'
            }
          })
        );
      }

      const responses = await Promise.all(requests);
      
      // 至少有一个响应应该是成功的（第一个）
      expect(responses[0].statusCode).toBe(200);
      
      // 后续的响应可能会被限制（取决于具体实现）
      // 这里我们只是验证没有服务器错误
      responses.forEach(response => {
        expect(response.statusCode).toBeLessThan(500);
      });
    });
  });

  describe('错误处理和边界条件', () => {
    it('应该处理数据库连接失败', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('数据库连接失败'))
          })
        })
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status',
        headers: {
          'authorization': 'Bearer mock-token'
        }
      });

      expect(response.statusCode).toBe(500);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('INTERNAL_ERROR');
    });

    it('应该处理Better Auth服务异常', async () => {
      // 模拟Better Auth异常
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockRejectedValueOnce(new Error('认证服务异常'));

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-status',
        headers: {
          'authorization': 'Bearer mock-token'
        }
      });

      expect(response.statusCode).toBe(500);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('INTERNAL_ERROR');
    });

    it('应该处理无效的Authorization头', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-verification',
        headers: {
          'authorization': 'Invalid Token Format',
          'content-type': 'application/json'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('UNAUTHORIZED');
    });

    it('应该处理超长的查询参数', async () => {
      const longToken = 'a'.repeat(10000);
      const longRedirect = 'https://example.com/' + 'a'.repeat(10000);
      
      const response = await app.inject({
        method: 'GET',
        url: `/api/auth/verify-email-callback?token=${longToken}&redirect=${longRedirect}`
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      // 应该能处理而不崩溃
      const html = response.body;
      expect(html).toBeTruthy();
    });
  });

  describe('HTML模板渲染测试', () => {
    it('应该正确渲染成功页面模板', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=valid-token&redirect=https://example.com/dashboard'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      const html = response.body;
      
      // 检查必要的HTML结构
      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html');
      expect(html).toContain('<head>');
      expect(html).toContain('<body>');
      expect(html).toContain('</html>');
      
      // 检查成功页面特定内容
      expect(html).toContain('邮箱验证成功');
      expect(html).toContain('✅');
      expect(html).toContain(mockVerifiedUser.name);
      expect(html).toContain(mockVerifiedUser.email);
      expect(html).toContain('https://example.com/dashboard');
      
      // 检查JavaScript重定向代码
      expect(html).toContain('<script>');
      expect(html).toContain('countdown');
    });

    it('应该正确渲染错误页面模板', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=invalid-token'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      
      const html = response.body;
      
      // 检查基本HTML结构
      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html');
      expect(html).toContain('<head>');
      expect(html).toContain('<body>');
      expect(html).toContain('</html>');
      
      // 检查错误页面特定内容
      expect(html).toContain('邮箱验证失败');
      expect(html).toContain('❌');
      expect(html).toContain('验证令牌无效或已过期');
      expect(html).toContain('重新发送验证邮件');
    });

    it('应该对HTML内容进行安全转义', async () => {
      // 模拟用户名中包含特殊字符的情况
      const userWithSpecialChars = {
        ...mockVerifiedUser,
        name: '<script>alert("xss")</script>测试用户',
        email: 'test<script>@example.com'
      };

      const { auth } = require('../src/auth.js');
      auth.api.verifyEmail.mockResolvedValueOnce({
        user: userWithSpecialChars
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-email-callback?token=valid-token'
      });

      expect(response.statusCode).toBe(200);
      
      const html = response.body;
      
      // 应该转义特殊字符，防止XSS
      expect(html).not.toContain('<script>alert("xss")</script>');
      expect(html).toContain('测试用户'); // 正常内容应该保留
      expect(html).not.toContain('test<script>@example.com');
    });
  });
});