# Resend 邮件服务 API 密钥
# 从 https://resend.com/api-keys 获取
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx

# 数据库连接 URL
DATABASE_URL=postgresql://auth_user:password@localhost:5432/auth_db

# Better Auth 配置
BETTER_AUTH_SECRET=your-super-secret-key-here
BETTER_AUTH_URL=http://localhost:10086

# 前端 URL 配置（用于邮箱验证重定向）
FRONTEND_URL=http://localhost:3000

# JWT 配置（可选，默认使用 BETTER_AUTH_SECRET）
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=1h
JWT_ALGORITHM=RS256

# Google OAuth 配置（可选）
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 运行环境
NODE_ENV=development