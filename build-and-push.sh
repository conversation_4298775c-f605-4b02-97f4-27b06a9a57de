#!/bin/bash

set -e

echo "构建业务逻辑镜像 - 环境配置将通过K8s注入"

# 配置
DOCKERFILE_PATH="./Dockerfile"
BASE_IMAGE_NAME="specific-ai-auth"
PRIVATE_REGISTRY="192.168.50.112/specific-ai"
CONTEXT_PATH="."
TAG="latest"

# 完整镜像名
LOCAL_IMAGE_FULL_NAME="${BASE_IMAGE_NAME}:${TAG}"
REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"

echo "--------------------------------------------------"
echo "构建和推送 ${BASE_IMAGE_NAME} Docker 镜像"
echo "统一标签: ${TAG} (环境差异通过K8s配置注入)"
echo "--------------------------------------------------"

# 删除旧的本地镜像（如果存在）
if docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "^${BASE_IMAGE_NAME}:${TAG}$" >/dev/null; then
    echo "删除旧的本地镜像: ${LOCAL_IMAGE_FULL_NAME}"
    docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true
fi

# 构建新镜像
echo "构建 Docker 镜像: ${LOCAL_IMAGE_FULL_NAME}"
docker build --no-cache \
    -f "${DOCKERFILE_PATH}" \
    -t "${LOCAL_IMAGE_FULL_NAME}" \
    "${CONTEXT_PATH}"

# 标记并推送
echo "标记 Docker 镜像: ${REMOTE_IMAGE_FULL_NAME}"
docker tag "${LOCAL_IMAGE_FULL_NAME}" "${REMOTE_IMAGE_FULL_NAME}"

echo "推送 Docker 镜像到私有仓库: ${REMOTE_IMAGE_FULL_NAME}"
docker push "${REMOTE_IMAGE_FULL_NAME}"

# 清理本地镜像
echo "清理本地镜像..."
docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true

echo "推送完成: ${REMOTE_IMAGE_FULL_NAME}"
echo "--------------------------------------------------"