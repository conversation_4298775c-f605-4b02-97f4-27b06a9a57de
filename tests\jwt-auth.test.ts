import { describe, it, expect, beforeAll, afterAll } from "@jest/globals";
import { app, initializeApp } from "../src/app.js";
import { auth } from "../src/auth.js";
import { jwtTokenService } from "../src/services/jwt-service.js";

describe("JWT认证系统测试", () => {
  let testUserEmail: string;
  let testUserId: string;
  let cookieSession: string;
  let jwtToken: string;

  beforeAll(async () => {
    await initializeApp();
    await app.ready();

    // 创建测试用户
    testUserEmail = `jwt-test-${Date.now()}@example.com`;
    
    // 注册测试用户
    const signUpResponse = await app.inject({
      method: "POST",
      url: "/api/auth/sign-up/email",
      payload: {
        email: testUserEmail,
        password: "testpassword123",
        name: "JWT Test User",
      },
    });

    expect(signUpResponse.statusCode).toBe(200);
    const signUpData = JSON.parse(signUpResponse.body);
    testUserId = signUpData.user?.id;

    // 登录获取Cookie session
    const signInResponse = await app.inject({
      method: "POST",
      url: "/api/auth/sign-in/email",
      payload: {
        email: testUserEmail,
        password: "testpassword123",
      },
    });

    expect(signInResponse.statusCode).toBe(200);
    
    // 提取Cookie
    const setCookieHeader = signInResponse.headers["set-cookie"];
    if (Array.isArray(setCookieHeader)) {
      cookieSession = setCookieHeader.join("; ");
    } else if (typeof setCookieHeader === "string") {
      cookieSession = setCookieHeader;
    }
  });

  afterAll(async () => {
    await app.close();
  });

  describe("JWT Token服务", () => {
    it("应该能创建JWT Token", async () => {
      const user = {
        id: testUserId,
        email: testUserEmail,
      };

      const result = await jwtTokenService.createToken(user);
      expect(result.success).toBe(true);
      expect(result.token).toBeDefined();
      expect(result.expiresIn).toBe(3600);
    });

    it("应该能验证JWT Token格式", async () => {
      const validToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
      
      const result = await jwtTokenService.verifyToken(validToken);
      expect(result.success).toBe(true);
    });

    it("应该拒绝无效的JWT Token格式", async () => {
      const invalidToken = "invalid-token";
      
      const result = await jwtTokenService.verifyToken(invalidToken);
      expect(result.success).toBe(false);
      expect(result.error).toContain("无效的JWT Token格式");
    });

    it("应该能解析JWT Token Payload", () => {
      const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.example";
      
      const payload = jwtTokenService.parseTokenPayload(token);
      expect(payload).toBeNull(); // 因为这是一个示例token，实际解析会失败
    });
  });

  describe("JWT API端点", () => {
    it("POST /api/auth/jwt/create - 应该能创建JWT Token", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/create",
        headers: {
          cookie: cookieSession,
        },
        payload: {
          expiresIn: "1h",
          customClaims: {
            service: "test",
          },
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.token).toBeDefined();
      expect(data.tokenType).toBe("Bearer");
      
      jwtToken = data.token; // 保存用于后续测试
    });

    it("POST /api/auth/jwt/create - 没有认证应该返回401", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/create",
        payload: {
          expiresIn: "1h",
        },
      });

      expect(response.statusCode).toBe(401);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(false);
      expect(data.error).toBe("未认证");
    });

    it("POST /api/auth/jwt/verify - 应该能验证JWT Token", async () => {
      const testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
      
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/verify",
        payload: {
          token: testToken,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.valid).toBeDefined();
    });

    it("POST /api/auth/jwt/verify - 缺少token应该返回400", async () => {
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/verify",
        payload: {},
      });

      expect(response.statusCode).toBe(400);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(false);
      expect(data.error).toBe("Token参数缺失");
    });

    it("POST /api/auth/jwt/refresh - 应该能刷新JWT Token", async () => {
      const testRefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
      
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/refresh",
        payload: {
          refresh_token: testRefreshToken,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.token).toBeDefined();
    });

    it("POST /api/auth/jwt/revoke - 应该能撤销JWT Token", async () => {
      const testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
      
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/revoke",
        headers: {
          cookie: cookieSession,
        },
        payload: {
          token: testToken,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.message).toContain("撤销");
    });

    it("GET /api/auth/jwt/info - 应该能获取JWT信息", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
        headers: {
          cookie: cookieSession,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.authType).toBe("cookie");
      expect(data.user).toBeDefined();
      expect(data.canCreateToken).toBe(true);
    });
  });

  describe("双重认证中间件", () => {
    it("应该支持Cookie认证", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
        headers: {
          cookie: cookieSession,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.authType).toBe("cookie");
    });

    it("应该支持JWT认证", async () => {
      // 首先创建一个JWT token
      const createResponse = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/create",
        headers: {
          cookie: cookieSession,
        },
        payload: {
          expiresIn: "1h",
        },
      });

      expect(createResponse.statusCode).toBe(200);
      const createData = JSON.parse(createResponse.body);
      const token = createData.token;

      // 然后使用JWT token调用API
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
        headers: {
          authorization: `Bearer ${token}`,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      // 注意：实际的JWT认证可能需要Better Auth JWT插件的完整实现
      // 这里我们主要测试API端点的存在和基本功能
    });

    it("没有认证信息应该返回401", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
      });

      expect(response.statusCode).toBe(401);
      const data = JSON.parse(response.body);
      expect(data.success).toBe(false);
      expect(data.error).toBe("未认证");
    });
  });

  describe("JWT配置和环境", () => {
    it("应该有正确的JWT配置", () => {
      // 检查环境变量是否设置
      expect(process.env.BETTER_AUTH_SECRET).toBeDefined();
      expect(process.env.DATABASE_URL).toBeDefined();
    });

    it("应该有正确的JWT插件配置", async () => {
      // 检查auth实例是否包含JWT配置
      expect(auth).toBeDefined();
      
      // 测试JWT相关的API端点是否存在
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/verify",
        payload: {
          token: "test-token",
        },
      });

      // 端点存在，只是token无效
      expect(response.statusCode).toBe(200);
    });
  });

  describe("安全性测试", () => {
    it("应该拒绝恶意JWT Token", async () => {
      const maliciousToken = "malicious-token-attempt";
      
      const response = await app.inject({
        method: "POST",
        url: "/api/auth/jwt/verify",
        payload: {
          token: maliciousToken,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.body);
      expect(data.valid).toBe(false);
    });

    it("应该拒绝空的Authorization header", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
        headers: {
          authorization: "",
        },
      });

      expect(response.statusCode).toBe(401);
    });

    it("应该拒绝格式错误的Authorization header", async () => {
      const response = await app.inject({
        method: "GET",
        url: "/api/auth/jwt/info",
        headers: {
          authorization: "InvalidFormat token",
        },
      });

      expect(response.statusCode).toBe(401);
    });
  });
});