apiVersion: v1
kind: Secret
metadata:
  name: specific-ai-auth-secret
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
    environment: dev
type: Opaque
data:
  # 开发环境数据库连接配置 (base64编码)
  # 原值: **************************************************/auth_db
  DATABASE_URL: ********************************************************************************
  
  # 开发环境认证密钥 (base64编码)
  # 原值: SpecificAI2025Dev
  BETTER_AUTH_SECRET: U3BlY2lmaWNBSTIwMjVEZXY=
  
  # 开发环境邮件服务API密钥 (base64编码)
  # 原值: re_vR8DZX4k_6QxFuoTZ1ETCs7hviuwtgJ9H
  RESEND_API_KEY: cmVfcHJvZF92UjhEWlQ0a192UTZReFZ1T1VUWkFFVDFFVEVDWDdodml3dGdKOUh
  
  # 开发环境Google OAuth密钥 (base64编码)
  # 原值: your-dev-google-client-secret
  GOOGLE_CLIENT_SECRET: eW91ci1kZXYtZ29vZ2xlLWNsaWVudC1zZWNyZXQ=
  
  # 开发环境JWT密钥 (base64编码)
# 原值: SpecificAI2025-v1.0-PlanS
  JWT_SECRET: U3BlY2lmaWNBMjAyNS12MS4wLVBsYW5T