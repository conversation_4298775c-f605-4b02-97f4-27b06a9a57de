/**
 * 邀请码上下文管理服务
 * 用于在用户注册过程中存储和传递邀请码信息
 */

interface InviteCodeContext {
  email: string;
  inviteCode: string;
  timestamp: number;
}

/**
 * 邀请码上下文管理器
 * 使用内存存储临时保存邀请码信息，支持自动过期清理
 */
export class InviteCodeContextManager {
  private contexts = new Map<string, InviteCodeContext>();
  private readonly TTL = 10 * 60 * 1000; // 10分钟过期时间

  /**
   * 设置用户的邀请码上下文
   */
  setContext(email: string, inviteCode: string): void {
    const context: InviteCodeContext = {
      email,
      inviteCode,
      timestamp: Date.now(),
    };
    
    this.contexts.set(email, context);
    
    console.log(`🎫 设置邀请码上下文: ${email} -> ${inviteCode}`);
    
    // 定时清理过期的上下文
    setTimeout(() => {
      this.cleanupContext(email);
    }, this.TTL);
  }

  /**
   * 获取用户的邀请码上下文
   */
  getContext(email: string): string | null {
    const context = this.contexts.get(email);
    
    if (!context) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - context.timestamp > this.TTL) {
      this.contexts.delete(email);
      console.log(`⏰ 邀请码上下文已过期: ${email}`);
      return null;
    }

    return context.inviteCode;
  }

  /**
   * 清理用户的邀请码上下文
   */
  cleanupContext(email: string): void {
    if (this.contexts.has(email)) {
      this.contexts.delete(email);
      console.log(`🧹 清理邀请码上下文: ${email}`);
    }
  }

  /**
   * 清理所有过期的上下文
   */
  cleanupExpiredContexts(): void {
    const now = Date.now();
    const expiredEmails: string[] = [];
    
    // 使用 Array.from 来避免迭代器兼容性问题
    Array.from(this.contexts.entries()).forEach(([email, context]) => {
      if (now - context.timestamp > this.TTL) {
        expiredEmails.push(email);
      }
    });
    
    expiredEmails.forEach(email => {
      this.contexts.delete(email);
    });
    
    if (expiredEmails.length > 0) {
      console.log(`🧹 清理 ${expiredEmails.length} 个过期的邀请码上下文`);
    }
  }

  /**
   * 获取当前上下文数量（用于监控）
   */
  getContextCount(): number {
    return this.contexts.size;
  }
}

// 创建全局单例实例
export const inviteCodeContextManager = new InviteCodeContextManager();

// 定期清理过期上下文
setInterval(() => {
  inviteCodeContextManager.cleanupExpiredContexts();
}, 5 * 60 * 1000); // 每5分钟清理一次