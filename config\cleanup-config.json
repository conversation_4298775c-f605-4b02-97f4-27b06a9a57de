{"production": {"enableCleanup": true, "maxRetries": 3, "batchSize": 1000, "retentionPolicies": {"expiredPendingRegistrations": 7, "completedRegistrations": 30, "failedRegistrations": 14, "verificationLogs": 30, "emailLogs": 60, "rateLimiterRecords": 7}, "backupBeforeCleanup": true, "backupRetentionDays": 90, "enableMetrics": true, "enableAlerts": true, "alertThresholds": {"maxCleanupTimeMs": 300000, "maxRecordsPerBatch": 10000, "minCleanupSuccessRate": 0.95}}, "development": {"enableCleanup": true, "maxRetries": 2, "batchSize": 100, "retentionPolicies": {"expiredPendingRegistrations": 1, "completedRegistrations": 7, "failedRegistrations": 3, "verificationLogs": 7, "emailLogs": 14, "rateLimiterRecords": 1}, "backupBeforeCleanup": false, "backupRetentionDays": 30, "enableMetrics": true, "enableAlerts": false, "alertThresholds": {"maxCleanupTimeMs": 120000, "maxRecordsPerBatch": 1000, "minCleanupSuccessRate": 0.8}}, "test": {"enableCleanup": false, "maxRetries": 1, "batchSize": 10, "retentionPolicies": {"expiredPendingRegistrations": 0, "completedRegistrations": 1, "failedRegistrations": 1, "verificationLogs": 1, "emailLogs": 1, "rateLimiterRecords": 0}, "backupBeforeCleanup": false, "backupRetentionDays": 1, "enableMetrics": false, "enableAlerts": false, "alertThresholds": {"maxCleanupTimeMs": 60000, "maxRecordsPerBatch": 100, "minCleanupSuccessRate": 0.5}}}