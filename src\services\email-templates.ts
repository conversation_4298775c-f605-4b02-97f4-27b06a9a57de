import {
  EmailTemplate,
  EmailTemplateData,
  EmailTemplateVersion,
  EmailType,
  TemplateRenderOptions,
} from '../types/email.js';
import { i18nService, SupportedLanguage } from './i18n-service.js';
import { InviteEmailTemplate } from './invite-email-template.js';

/**
 * 基础邮件模板类 - 提供通用的HTML结构
 * 支持多语言和响应式设计
 */
class BaseEmailTemplate {
  protected readonly supportedLanguages: SupportedLanguage[] = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
  
  /**
   * 获取语言相关的文本
   */
  protected getLocalizedText(key: string, language: string = 'zh-CN', params?: Record<string, any>): string {
    try {
      const normalizedLanguage = this.normalizeLanguage(language);
      return i18nService.translate(key, normalizedLanguage, params);
    } catch (error) {
      console.warn(`Failed to get localized text for key: ${key}, language: ${language}`);
      return key; // 回退到键名
    }
  }
  
  /**
   * 标准化语言代码
   */
  protected normalizeLanguage(language: string): SupportedLanguage {
    const langMap: Record<string, SupportedLanguage> = {
      'chinese': 'zh-CN',
      'english': 'en-US',
      'japanese': 'ja-JP',
      'korean': 'ko-KR',
      'zh': 'zh-CN',
      'en': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR'
    };
    
    return langMap[language.toLowerCase()] || 'zh-CN';
  }
  protected getBaseStyles(): string {
    return `
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 0;
      background-color: #f7f9fc;
      line-height: 1.6;
    `;
  }
  
  /**
   * 获取响应式样式
   */
  protected getResponsiveStyles(): string {
    return `
      <style>
        @media only screen and (max-width: 600px) {
          .email-container { width: 100% !important; margin: 0 !important; }
          .email-content { padding: 20px !important; }
          .email-header { padding: 30px 20px !important; }
          .email-button { width: 100% !important; padding: 15px !important; font-size: 16px !important; }
          .feature-list { padding-left: 20px !important; }
        }
        
        @media (prefers-color-scheme: dark) {
          .email-container { background-color: #1a1a1a !important; }
          .email-content { background-color: #2d2d2d !important; color: #ffffff !important; }
          .email-text { color: #e0e0e0 !important; }
          .email-footer { background-color: #1a1a1a !important; color: #888888 !important; }
        }
      </style>
    `;
  }

  protected getHeaderHtml(title: string, subtitle?: string, logoUrl?: string): string {
    return `
      <div class="email-header" style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 40px 30px;
        text-align: center;
        border-radius: 8px 8px 0 0;
      ">
        ${logoUrl ? `<img src="${logoUrl}" alt="SpecificAI" style="height: 40px; margin-bottom: 20px;">` : ''}
        <h1 style="
          color: #ffffff;
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        ">${title}</h1>
        ${subtitle ? `<p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">${subtitle}</p>` : ''}
      </div>
    `;
  }

  protected getFooterHtml(language: string = 'zh-CN'): string {
    const footerText = this.getLocalizedText('email.footer.autoSent', language);
    const companyText = this.getLocalizedText('email.footer.company', language);
    const supportText = this.getLocalizedText('email.footer.support', language);
    
    return `
      <div class="email-footer" style="
        background-color: #f8f9fa;
        padding: 30px;
        text-align: center;
        border-radius: 0 0 8px 8px;
        border-top: 1px solid #e9ecef;
      ">
        <div style="margin-bottom: 20px;">
          <img src="https://cdn.specific-ai.com/logo-small.png" alt="SpecificAI" style="height: 24px; opacity: 0.6;">
        </div>
        <p style="color: #6c757d; font-size: 14px; margin: 0 0 10px 0;">
          ${companyText || 'SpecificAI - 让AI更智能，让工作更高效'}
        </p>
        <p style="color: #6c757d; font-size: 12px; margin: 0 0 15px 0;">
          ${footerText || '此邮件由 SpecificAI 系统自动发送，请勿回复'}
        </p>
        <div style="font-size: 12px; color: #6c757d;">
          <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;">${supportText || '联系支持'}</a>
          <span style="margin: 0 10px;">|</span>
          <a href="https://specific-ai.com/privacy" style="color: #667eea; text-decoration: none;">${this.getLocalizedText('email.footer.privacy', language) || '隐私政策'}</a>
        </div>
      </div>
    `;
  }

  protected getButtonHtml(url: string, text: string, color: string = '#667eea', icon?: string): string {
    return `
      <div style="text-align: center; margin: 35px 0;">
        <a href="${url}" 
           class="email-button"
           style="
             background: linear-gradient(135deg, ${color}, ${this.darkenColor(color, 20)});
             color: white;
             padding: 16px 40px;
             text-decoration: none;
             border-radius: 8px;
             font-size: 16px;
             font-weight: 600;
             display: inline-block;
             box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
             transition: all 0.3s ease;
             border: none;
             cursor: pointer;
           "
           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'"
           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.3)'">
          ${icon ? `<span style="margin-right: 8px;">${icon}</span>` : ''}
          ${text}
        </a>
      </div>
    `;
  }
  
  /**
   * 获取加深颜色的函数
   */
  protected darkenColor(color: string, percent: number): string {
    // 简单的颜色加深算法，实际项目中可以使用更复杂的颜色处理库
    if (color === '#667eea') return '#5a6fd8';
    if (color === '#28a745') return '#1e7e34';
    if (color === '#e74c3c') return '#c0392b';
    return color;
  }

  protected wrapInContainer(content: string, language: string = 'zh-CN'): string {
    return `
      <!DOCTYPE html>
      <html lang="${language}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>SpecificAI</title>
        ${this.getResponsiveStyles()}
      </head>
      <body style="margin: 0; padding: 20px; ${this.getBaseStyles()}">
        <div class="email-container" style="
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        ">
          ${content}
          ${this.getFooterHtml(language)}
        </div>
      </body>
      </html>
    `;
  }
  
  /**
   * 获取内容区域样式
   */
  protected getContentStyles(): string {
    return `
      padding: 40px 30px;
      background-color: #ffffff;
    `;
  }
}

/**
 * 邮箱验证邮件模板 - 支持多语言和现代化设计
 */
class EmailVerificationTemplate extends BaseEmailTemplate {
  
  /**
   * 获取功能列表HTML
   */
  protected getFeatureListHtml(language: string): string {
    const features = [
      this.getLocalizedText('email.verification.features.access', language) || '访问完整的产品功能',
      this.getLocalizedText('email.verification.features.notifications', language) || '接收重要的账户通知', 
      this.getLocalizedText('email.verification.features.security', language) || '享受更安全的账户保护'
    ];
    
    return `
      <div style="
        background-color: #f8f9fa;
        padding: 25px;
        border-radius: 8px;
        margin: 25px 0;
        border-left: 4px solid #667eea;
      ">
        <h4 style="
          color: #495057;
          margin-top: 0;
          font-size: 16px;
          font-weight: 600;
        ">${this.getLocalizedText('email.verification.afterVerification', language) || '验证后您可以：'}</h4>
        <ul class="feature-list" style="
          color: #6c757d;
          font-size: 14px;
          margin-bottom: 0;
          padding-left: 20px;
        ">
          ${features.map(feature => `<li style="margin-bottom: 8px;">${feature}</li>`).join('')}
        </ul>
      </div>
    `;
  }
  
  /**
   * 获取邀请码显示HTML
   */
  protected getInviteCodeHtml(inviteCode: string, language: string): string {
    const title = this.getLocalizedText('email.verification.inviteCode', language) || '您的邀请码';
    
    return `
      <div style="
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin: 25px 0;
        text-align: center;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
      ">
        <p style="
          margin: 0;
          font-size: 14px;
          font-weight: 600;
        ">
          🎫 <strong>${title}：</strong>
          <span style="
            font-size: 18px;
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
            margin-left: 10px;
          ">${inviteCode}</span>
        </p>
      </div>
    `;
  }
  renderV1(data: EmailTemplateData): EmailTemplate {
    const { userName, userEmail, verificationUrl, inviteCode, locale = 'zh-CN' } = data;
    const language = this.normalizeLanguage(locale);
    
    // 构建验证URL，包含邀请码参数
    const finalUrl = inviteCode 
      ? `${verificationUrl}${verificationUrl?.includes('?') ? '&' : '?'}invite_code=${encodeURIComponent(inviteCode)}`
      : verificationUrl!;

    // 获取本地化文本
    const welcomeTitle = this.getLocalizedText('email.verification.welcome', language) || '欢迎使用 SpecificAI';
    const greeting = this.getLocalizedText('email.verification.greeting', language, { name: userName || userEmail }) || `您好 ${userName || userEmail}，`;
    const message = this.getLocalizedText('email.verification.message', language) || '感谢您注册 SpecificAI 账户。请点击下面的链接来验证您的邮箱地址：';
    const buttonText = this.getLocalizedText('email.verification.button', language) || '验证邮箱';
    const alternativeText = this.getLocalizedText('email.verification.alternative', language) || '如果您无法点击上面的按钮，请复制并粘贴以下链接到您的浏览器：';
    const expiryText = this.getLocalizedText('email.verification.expiry', language) || '此链接将在 1 小时后过期。';
    const subject = this.getLocalizedText('email.verification.subject', language) || '验证您的邮箱地址 - SpecificAI';

    const html = this.wrapInContainer(`
      ${this.getHeaderHtml(welcomeTitle)}
      
      <div class="email-content" style="${this.getContentStyles()}">
        <p style="color: #495057; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
          ${greeting}
        </p>
        
        <p style="color: #495057; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
          ${message}
        </p>
        
        ${this.getButtonHtml(finalUrl, buttonText, '#667eea', '✉️')}
        
        <p style="color: #6c757d; font-size: 14px; line-height: 1.5; margin-bottom: 15px;">
          ${alternativeText}
        </p>
        <div style="
          color: #667eea;
          font-size: 14px;
          word-break: break-all;
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          border: 1px solid #e9ecef;
          font-family: 'Courier New', monospace;
        ">
          ${finalUrl}
        </div>
        
        <p style="color: #6c757d; font-size: 12px; text-align: center; margin: 25px 0;">
          ⏰ ${expiryText}
        </p>
        
        ${inviteCode ? this.getInviteCodeHtml(inviteCode, language) : ''}
      </div>
    `, language);

    const textContent = `${welcomeTitle}\n\n${greeting}\n\n${message}\n\n${this.getLocalizedText('email.verification.link', language) || '验证链接'}：${finalUrl}${inviteCode ? `\n\n${this.getLocalizedText('email.verification.inviteCode', language) || '邀请码'}：${inviteCode}` : ''}`;

    return {
      subject,
      html,
      text: textContent,
      preheader: this.getLocalizedText('email.verification.preheader', language) || '点击验证您的邮箱地址以激活账户',
    };
  }

  renderV2(data: EmailTemplateData): EmailTemplate {
    const { userName, userEmail, verificationUrl, inviteCode, locale = 'zh-CN' } = data;
    const language = this.normalizeLanguage(locale);
    
    // 构建验证URL，包含邀请码参数
    const finalUrl = inviteCode 
      ? `${verificationUrl}${verificationUrl?.includes('?') ? '&' : '?'}invite_code=${encodeURIComponent(inviteCode)}`
      : verificationUrl!;
      
    // 获取本地化文本
    const welcomeTitle = this.getLocalizedText('email.verification.welcomeV2', language) || '🎉 欢迎加入 SpecificAI';
    const subtitle = this.getLocalizedText('email.verification.subtitle', language) || '您的智能工作伙伴';
    const welcomeMessage = this.getLocalizedText('email.verification.welcomeMessage', language, { name: userName || userEmail }) || `欢迎 ${userName || userEmail} 加入我们！`;
    const securityMessage = this.getLocalizedText('email.verification.securityMessage', language) || '您已成功创建 SpecificAI 账户。为确保账户安全，请验证您的邮箱地址：';
    const buttonText = this.getLocalizedText('email.verification.buttonV2', language) || '🔗 验证邮箱地址';
    const linkLabel = this.getLocalizedText('email.verification.linkLabel', language) || '📋 无法点击按钮？展开查看验证链接';
    const expiryText = this.getLocalizedText('email.verification.expiry', language) || '验证链接将在 1 小时后过期';
    const subject = this.getLocalizedText('email.verification.subjectV2', language) || '🚀 验证邮箱，开始您的 SpecificAI 之旅';
      
    const html = this.wrapInContainer(`
      ${this.getHeaderHtml(welcomeTitle, subtitle, 'https://cdn.specific-ai.com/email-logo.png')}
      
      <div class="email-content" style="${this.getContentStyles()}">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 25px;
          border-radius: 12px;
          text-align: center;
          margin-bottom: 35px;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        ">
          <p style="
            color: white;
            font-size: 18px;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
          ">
            ${welcomeMessage}
          </p>
        </div>
        
        <p style="color: #495057; font-size: 16px; line-height: 1.6; margin-bottom: 35px;">
          ${securityMessage}
        </p>
        
        ${this.getButtonHtml(finalUrl, buttonText, '#667eea', '🔐')}
        
        ${this.getFeatureListHtml(language)}
        
        <details style="margin: 25px 0; cursor: pointer;">
          <summary style="
            color: #6c757d;
            cursor: pointer;
            font-size: 14px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
          ">
            ${linkLabel}
          </summary>
          <div style="
            color: #667eea;
            font-size: 12px;
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            border: 1px solid #e9ecef;
          ">
            ${finalUrl}
          </div>
        </details>
        
        ${inviteCode ? this.getInviteCodeHtml(inviteCode, language) : ''}
        
        <p style="color: #6c757d; font-size: 12px; text-align: center; margin-top: 35px;">
          ⏰ ${expiryText}
        </p>
      </div>
    `, language);

    const textContent = `${welcomeTitle}\n\n${welcomeMessage}\n\n${securityMessage}\n\n${this.getLocalizedText('email.verification.link', language) || '验证链接'}：${finalUrl}${inviteCode ? `\n\n🎫 ${this.getLocalizedText('email.verification.inviteCode', language) || '邀请码'}：${inviteCode}` : ''}`;

    return {
      subject,
      html,
      text: textContent,
      preheader: this.getLocalizedText('email.verification.preheaderV2', language) || '完成验证，开启您的智能工作之旅',
    };
  }

  /**
   * 渲染最新版本的验证邮件模板（V3）
   */
  renderV3(data: EmailTemplateData): EmailTemplate {
    const { userName, userEmail, verificationUrl, inviteCode, locale = 'zh-CN', companyName } = data;
    const language = this.normalizeLanguage(locale);
    
    // 构建验证URL，包含邀请码参数
    const finalUrl = inviteCode 
      ? `${verificationUrl}${verificationUrl?.includes('?') ? '&' : '?'}invite_code=${encodeURIComponent(inviteCode)}`
      : verificationUrl!;
    
    // 获取本地化文本
    const welcomeTitle = this.getLocalizedText('email.verification.welcomeV3', language) || '🚀 开启您的智能工作体验';
    const subtitle = this.getLocalizedText('email.verification.subtitleV3', language) || '专为您定制的AI解决方案';
    const personalGreeting = this.getLocalizedText('email.verification.personalGreeting', language, { name: userName || userEmail, company: companyName }) || `${userName || userEmail}，欢迎您${companyName ? `代表${companyName}` : ''}加入 SpecificAI！`;
    const mainMessage = this.getLocalizedText('email.verification.mainMessageV3', language) || '我们很高兴您选择了 SpecificAI 作为您的智能工作伙伴。为了确保账户安全并解锁所有功能，请验证您的邮箱地址。';
    const buttonText = this.getLocalizedText('email.verification.buttonV3', language) || '🔓 立即验证并开始使用';
    const subject = this.getLocalizedText('email.verification.subjectV3', language) || '🚀 欢迎使用 SpecificAI - 请验证您的邮箱';
    
    const html = this.wrapInContainer(`
      ${this.getHeaderHtml(welcomeTitle, subtitle)}
      
      <div class="email-content" style="${this.getContentStyles()}">
        <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">
          ${personalGreeting}
        </h2>
        
        <p style="color: #2d3748; font-size: 16px; line-height: 1.7; margin-bottom: 35px;">
          ${mainMessage}
        </p>
        
        ${this.getButtonHtml(finalUrl, buttonText, '#667eea')}
        
        ${inviteCode ? this.getInviteCodeHtml(inviteCode, language) : ''}
        
        <p style="color: #718096; font-size: 12px; text-align: center; margin-top: 40px;">
          ⏰ ${this.getLocalizedText('email.verification.expiryV3', language) || '安全链接24小时内有效'}
        </p>
      </div>
    `, language);

    const textContent = `${welcomeTitle}\n\n${personalGreeting}\n\n${mainMessage}\n\n${this.getLocalizedText('email.verification.link', language) || '验证链接'}：${finalUrl}${inviteCode ? `\n\n🎫 ${this.getLocalizedText('email.verification.inviteCode', language) || '邀请码'}：${inviteCode}` : ''}`;

    return {
      subject,
      html,
      text: textContent,
      preheader: this.getLocalizedText('email.verification.preheaderV3', language) || '立即验证，开启您的专属AI工作空间',
    };
  }
}

/**
 * 密码重置邮件模板
 */
class PasswordResetTemplate extends BaseEmailTemplate {
  renderV1(data: EmailTemplateData): EmailTemplate {
    const { userName, userEmail, resetPasswordUrl } = data;

    const html = this.wrapInContainer(`
      ${this.getHeaderHtml('重置您的密码')}
      
      <p style="color: #666; font-size: 16px; line-height: 1.5;">
        您好 ${userName || userEmail}，
      </p>
      
      <p style="color: #666; font-size: 16px; line-height: 1.5;">
        我们收到了您的密码重置请求。请点击下面的链接来重置您的密码：
      </p>
      
      ${this.getButtonHtml(resetPasswordUrl!, '重置密码', '#dc3545')}
      
      <p style="color: #999; font-size: 14px; line-height: 1.5;">
        如果您无法点击上面的按钮，请复制并粘贴以下链接到您的浏览器：
      </p>
      <p style="color: #007bff; font-size: 14px; word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
        ${resetPasswordUrl}
      </p>
      
      <p style="color: #999; font-size: 14px; line-height: 1.5;">
        此链接将在 1 小时后过期。
      </p>
      
      <p style="color: #999; font-size: 14px; line-height: 1.5;">
        如果您没有请求重置密码，请忽略此邮件。
      </p>
    `);

    return {
      subject: '重置您的密码 - SpecificAI',
      html,
      text: `重置密码请求。请访问以下链接重置您的密码：${resetPasswordUrl}`,
    };
  }

  renderV2(data: EmailTemplateData): EmailTemplate {
    const { userName, userEmail, resetPasswordUrl } = data;

    const html = this.wrapInContainer(`
      ${this.getHeaderHtml('🔐 密码重置请求')}
      
      <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; 
                  padding: 15px; border-radius: 6px; margin-bottom: 20px;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          ⚠️ <strong>安全提醒：</strong>我们收到了重置您账户密码的请求
        </p>
      </div>
      
      <p style="color: #666; font-size: 16px; line-height: 1.6;">
        您好 ${userName || userEmail}，
      </p>
      
      <p style="color: #666; font-size: 16px; line-height: 1.6;">
        如果这是您本人的操作，请点击下面的按钮重置密码：
      </p>
      
      ${this.getButtonHtml(resetPasswordUrl!, '🔑 重置我的密码', '#e74c3c')}
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h4 style="color: #495057; margin-top: 0; font-size: 16px;">🛡️ 安全提示：</h4>
        <ul style="color: #666; font-size: 14px; margin-bottom: 0;">
          <li>请确保在安全的网络环境下重置密码</li>
          <li>选择一个强密码，包含字母、数字和特殊字符</li>
          <li>不要与他人分享您的新密码</li>
        </ul>
      </div>
      
      <details style="margin: 20px 0;">
        <summary style="color: #666; cursor: pointer; font-size: 14px;">
          📋 无法点击按钮？展开查看重置链接
        </summary>
        <p style="color: #007bff; font-size: 12px; word-break: break-all; 
                  background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
          ${resetPasswordUrl}
        </p>
      </details>
      
      <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; 
                  padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #0c5460; margin: 0; font-size: 14px;">
          🚫 <strong>非本人操作？</strong>如果您没有请求重置密码，请立即忽略此邮件并检查账户安全
        </p>
      </div>
      
      <p style="color: #999; font-size: 12px; text-align: center; margin-top: 30px;">
        ⏰ 重置链接将在 1 小时后过期
      </p>
    `);

    return {
      subject: '🔐 SpecificAI 密码重置请求',
      html,
      text: `🔐 密码重置请求。如果是您本人操作，请访问以下链接重置密码：${resetPasswordUrl}。如非本人操作，请忽略此邮件。`,
    };
  }
}

/**
 * 邮箱验证确认模板
 */
class EmailVerificationConfirmationTemplate extends BaseEmailTemplate {
  renderSuccessPage(data: { userName?: string; userEmail: string; redirectUrl?: string }): string {
    const { userName, userEmail, redirectUrl } = data;
    
    return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>邮箱验证成功 - SpecificAI</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
          margin: 0; 
          padding: 20px; 
          background-color: #f8f9fa; 
          display: flex; 
          justify-content: center; 
          align-items: center; 
          min-height: 100vh; 
        }
        .container { 
          max-width: 500px; 
          background: white; 
          border-radius: 8px; 
          box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
          padding: 40px; 
          text-align: center; 
        }
        .success-icon { 
          width: 80px; 
          height: 80px; 
          margin: 0 auto 20px; 
          background: linear-gradient(135deg, #28a745, #20c997); 
          border-radius: 50%; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          font-size: 40px; 
          color: white; 
        }
        h1 { 
          color: #28a745; 
          margin-bottom: 10px; 
          font-size: 24px; 
        }
        p { 
          color: #666; 
          margin-bottom: 20px; 
          line-height: 1.6; 
        }
        .redirect-info { 
          background: #e7f3ff; 
          padding: 15px; 
          border-radius: 6px; 
          margin: 20px 0; 
          font-size: 14px; 
          color: #0056b3; 
        }
        .btn { 
          display: inline-block; 
          background: linear-gradient(135deg, #667eea, #764ba2); 
          color: white; 
          padding: 12px 30px; 
          text-decoration: none; 
          border-radius: 6px; 
          font-weight: 500; 
          margin: 10px 5px; 
          transition: transform 0.2s; 
        }
        .btn:hover { 
          transform: translateY(-2px); 
        }
        .footer { 
          margin-top: 30px; 
          font-size: 12px; 
          color: #999; 
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="success-icon">✓</div>
        <h1>邮箱验证成功！</h1>
        <p>恭喜 ${userName || userEmail}，您的邮箱已成功验证。</p>
        <p>现在您可以使用所有功能，包括接收重要通知和享受完整的账户保护。</p>
        
        ${redirectUrl ? `
          <div class="redirect-info">
            <p><strong>🚀 即将自动跳转到应用...</strong></p>
            <p>如果页面没有自动跳转，请点击下面的按钮：</p>
          </div>
          <a href="${redirectUrl}" class="btn">进入应用</a>
          <script>
            setTimeout(function() {
              window.location.href = '${redirectUrl}';
            }, 3000);
          </script>
        ` : `
          <a href="/" class="btn">返回首页</a>
        `}
        
        <div class="footer">
          <p>SpecificAI - 让AI更智能，让工作更高效</p>
        </div>
      </div>
    </body>
    </html>
    `;
  }

  renderErrorPage(data: { error: string; canResend?: boolean }): string {
    const { error, canResend } = data;
    
    return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>验证失败 - SpecificAI</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
          margin: 0; 
          padding: 20px; 
          background-color: #f8f9fa; 
          display: flex; 
          justify-content: center; 
          align-items: center; 
          min-height: 100vh; 
        }
        .container { 
          max-width: 500px; 
          background: white; 
          border-radius: 8px; 
          box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
          padding: 40px; 
          text-align: center; 
        }
        .error-icon { 
          width: 80px; 
          height: 80px; 
          margin: 0 auto 20px; 
          background: linear-gradient(135deg, #dc3545, #e74c3c); 
          border-radius: 50%; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          font-size: 40px; 
          color: white; 
        }
        h1 { 
          color: #dc3545; 
          margin-bottom: 10px; 
          font-size: 24px; 
        }
        p { 
          color: #666; 
          margin-bottom: 20px; 
          line-height: 1.6; 
        }
        .error-info { 
          background: #f8d7da; 
          padding: 15px; 
          border-radius: 6px; 
          margin: 20px 0; 
          font-size: 14px; 
          color: #721c24; 
        }
        .btn { 
          display: inline-block; 
          background: linear-gradient(135deg, #667eea, #764ba2); 
          color: white; 
          padding: 12px 30px; 
          text-decoration: none; 
          border-radius: 6px; 
          font-weight: 500; 
          margin: 10px 5px; 
          transition: transform 0.2s; 
        }
        .btn:hover { 
          transform: translateY(-2px); 
        }
        .btn-secondary { 
          background: #6c757d; 
        }
        .footer { 
          margin-top: 30px; 
          font-size: 12px; 
          color: #999; 
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="error-icon">✗</div>
        <h1>验证失败</h1>
        <p>很抱歉，邮箱验证过程中出现了问题。</p>
        
        <div class="error-info">
          <p><strong>错误原因：</strong>${error}</p>
        </div>
        
        <p>可能的解决方案：</p>
        <ul style="text-align: left; color: #666; font-size: 14px;">
          <li>验证链接可能已过期，请重新发送验证邮件</li>
          <li>检查邮箱地址是否正确</li>
          <li>确保网络连接正常</li>
        </ul>
        
        ${canResend ? `
          <a href="/api/auth/resend-verification" class="btn">重新发送验证邮件</a>
        ` : ''}
        <a href="/" class="btn btn-secondary">返回首页</a>
        
        <div class="footer">
          <p>如果问题持续存在，请联系技术支持</p>
          <p>SpecificAI - 让AI更智能，让工作更高效</p>
        </div>
      </div>
    </body>
    </html>
    `;
  }
}

/**
 * 邮件模板管理器
 */
export class EmailTemplateManager {
  private verificationTemplate = new EmailVerificationTemplate();
  private passwordResetTemplate = new PasswordResetTemplate();
  private confirmationTemplate = new EmailVerificationConfirmationTemplate();

  /**
   * 渲染最新版本的验证邮件模板（V3）
   */
  renderV3(data: EmailTemplateData): EmailTemplate {
    // 委托给 verificationTemplate 的 renderV3 方法
    return this.verificationTemplate.renderV3(data);
  }

  /**
   * 渲染邮件模板
   */
  render(options: TemplateRenderOptions & { language?: string }): EmailTemplate {
    const { type, version, data, language } = options;

    switch (type) {
      case 'verification':
        switch (version) {
          case 'v3':
            return this.verificationTemplate.renderV3(data);
          case 'v2':
            return this.verificationTemplate.renderV2(data);
          default:
            return this.verificationTemplate.renderV1(data);
        }
          
      case 'password-reset':
        return version === 'v2'
          ? this.passwordResetTemplate.renderV2(data)
          : this.passwordResetTemplate.renderV1(data);

      case 'invite':
        const inviteTemplate = new InviteEmailTemplate();
        return inviteTemplate.render(version, data, language);
          
      default:
        throw new Error(`Unsupported email type: ${type}`);
    }
  }

  /**
   * 获取可用的模板版本列表
   */
  getAvailableVersions(): EmailTemplateVersion[] {
    return ['v1', 'v2', 'v3'];
  }

  /**
   * 检查版本是否可用
   */
  isVersionAvailable(version: EmailTemplateVersion): boolean {
    return this.getAvailableVersions().includes(version);
  }

  /**
   * 渲染邮箱验证成功页面
   */
  renderVerificationSuccessPage(data: { userName?: string; userEmail: string; redirectUrl?: string }): string {
    return this.confirmationTemplate.renderSuccessPage(data);
  }

  /**
   * 渲染邮箱验证失败页面
   */
  renderVerificationErrorPage(data: { error: string; canResend?: boolean }): string {
    return this.confirmationTemplate.renderErrorPage(data);
  }
}