#!/bin/bash

set -e

# 解析命令行参数
ENVIRONMENT="dev"  # 默认为开发环境

if [ "$1" == "--pd" ] || [ "$1" == "-pd" ]; then
    ENVIRONMENT="pd"
    echo "使用生产环境配置"
elif [ "$1" == "--dev" ] || [ "$1" == "-dev" ]; then
    ENVIRONMENT="dev"
    echo "使用开发环境配置"
fi

# 配置
BASE_IMAGE_NAME="specific-ai-auth"
PRIVATE_REGISTRY="192.168.50.112"
TAG="latest"
REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"

# 容器名称
if [ "$ENVIRONMENT" == "pd" ]; then
    CONTAINER_NAME="specific-ai-auth-prod"
else
    CONTAINER_NAME="specific-ai-auth-dev"
fi

echo "--------------------------------------------------"
echo "部署 ${BASE_IMAGE_NAME} 服务"
echo "环境: ${ENVIRONMENT}"
echo "容器名称: ${CONTAINER_NAME}"
echo "使用镜像: ${REMOTE_IMAGE_FULL_NAME}"
echo "--------------------------------------------------"

# 停止并删除现有容器（如果存在）
if docker ps -a --format "{{.Names}}" | grep -E "^${CONTAINER_NAME}$" >/dev/null; then
    echo "停止并删除现有容器: ${CONTAINER_NAME}"
    docker stop "${CONTAINER_NAME}" || true
    docker rm "${CONTAINER_NAME}" || true
fi

# 清理本地同名容器（如果存在）
LOCAL_CONTAINERS=$(docker ps -a --filter "name=${BASE_IMAGE_NAME}" --format "{{.Names}}" | grep -v "^${CONTAINER_NAME}$" || true)
if [ ! -z "$LOCAL_CONTAINERS" ]; then
    echo "清理本地相关容器: $LOCAL_CONTAINERS"
    echo "$LOCAL_CONTAINERS" | xargs -r docker stop || true
    echo "$LOCAL_CONTAINERS" | xargs -r docker rm || true
fi

# 拉取最新镜像
echo "拉取最新镜像: ${REMOTE_IMAGE_FULL_NAME}"
docker pull "${REMOTE_IMAGE_FULL_NAME}"

# 启动新容器 - 分别处理开发和生产环境
echo "启动新容器: ${CONTAINER_NAME}"
if [ "$ENVIRONMENT" == "pd" ]; then
    # 生产环境：自动重启
    echo "使用生产环境变量"
    docker run -d \
        --name "${CONTAINER_NAME}" \
        -p 10086:10086 \
        --restart unless-stopped \
        -e DATABASE_URL="***************************************************************/auth_db?schema=auth" \
        -e BETTER_AUTH_SECRET="SpecificAI2025" \
        -e BETTER_AUTH_URL="http://localhost:10086" \
        -e PORT="10086" \
        -e NODE_ENV="production" \
        -e CORS_ORIGIN="*" \
        -e BUSINESS_BASE_API="http://***************:8000" \
        -e RESEND_API_KEY="re_AZ8gjh3D_FhfBmvDEpvLecPMGwkdT23sf" \
        "${REMOTE_IMAGE_FULL_NAME}"
else
    # 开发环境：不自动重启
    echo "使用开发环境变量"
    docker run -d \
        --name "${CONTAINER_NAME}" \
        -p 10086:10086 \
        -e DATABASE_URL="**************************************************/auth_db" \
        -e BETTER_AUTH_SECRET="SpecificAI2025" \
        -e BETTER_AUTH_URL="http://localhost:10086" \
        -e PORT="10086" \
        -e NODE_ENV="development" \
        -e CORS_ORIGIN="*" \
        -e BUSINESS_BASE_API="http://***************:8000" \
        -e RESEND_API_KEY="re_BV5rXaxg_6gXB5mGcTSrkMvqiNFNS8xxD" \
        -e GOOGLE_CLIENT_ID="569447095443-fli63bo0l9ro1nlrk3l4nj54uo8m9qbe.apps.googleusercontent.com" \
        -e GOOGLE_CLIENT_SECRET="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" \
        "${REMOTE_IMAGE_FULL_NAME}"
fi

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 验证环境变量是否正确传递
echo "验证环境变量传递..."
docker exec "${CONTAINER_NAME}" printenv | grep -E "(DATABASE_URL|BETTER_AUTH_SECRET|NODE_ENV)" || echo "环境变量检查完成"

# 健康检查
echo "执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost:10086/api/health >/dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 健康检查失败，服务可能未正常启动"
        echo "📋 容器日志:"
        docker logs --tail=50 "${CONTAINER_NAME}"
        echo ""
        echo "🔍 环境变量检查:"
        docker exec "${CONTAINER_NAME}" printenv | grep -E "(DATABASE_URL|BETTER_AUTH_SECRET|NODE_ENV|PORT)" || echo "未找到相关环境变量"
        exit 1
    fi
    echo "⏳ 健康检查尝试 $i/30..."
    sleep 2
done

echo "--------------------------------------------------"
echo "🎉 部署完成！"
echo ""
echo "📍 服务信息:"
echo "  - 环境: ${ENVIRONMENT}"
echo "  - 容器名称: ${CONTAINER_NAME}"
echo "  - 服务地址: http://localhost:10086"
echo "  - 健康检查: http://localhost:10086/api/health"
echo ""
echo "📊 容器状态:"
docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker logs -f ${CONTAINER_NAME}"
echo "  查看环境变量: docker exec ${CONTAINER_NAME} printenv"
echo "  进入容器: docker exec -it ${CONTAINER_NAME} /bin/sh"
echo "  停止服务: docker stop ${CONTAINER_NAME}"
echo "  重启服务: docker restart ${CONTAINER_NAME}"
echo "  删除容器: docker rm -f ${CONTAINER_NAME}"
echo "--------------------------------------------------"
echo ""
echo "📋 最新容器日志 (最后50行):"
echo "--------------------------------------------------"
docker logs --tail=50 "${CONTAINER_NAME}"
echo "--------------------------------------------------" 