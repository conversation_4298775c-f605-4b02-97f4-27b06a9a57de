import { FastifyRequest, FastifyReply } from "fastify";
import { randomUUID } from "crypto";
import { db } from "../lib/drizzle.js";
import { requestLog, securityEvent } from "../lib/auth-schema.js";
import { auth } from "../auth.js";
import { sql } from "drizzle-orm";

/**
 * 请求日志配置接口
 */
export interface RequestLogConfig {
  enableRequestBody?: boolean; // 是否记录请求体
  enableResponseBody?: boolean; // 是否记录响应体
  maxBodySize?: number; // 最大记录的请求/响应体大小（字节）
  sensitiveFields?: string[]; // 需要脱敏的字段
  excludePaths?: string[]; // 排除记录的路径
  logLevel?: 'minimal' | 'standard' | 'detailed'; // 日志级别
  enablePerformanceMetrics?: boolean; // 是否启用性能指标
  enableSecurityMonitoring?: boolean; // 是否启用安全监控
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: RequestLogConfig = {
  enableRequestBody: false,
  enableResponseBody: false,
  maxBodySize: 1024 * 10, // 10KB
  sensitiveFields: ['password', 'token', 'secret', 'key', 'authorization'],
  excludePaths: ['/api/health', '/docs', '/favicon.ico'],
  logLevel: 'standard',
  enablePerformanceMetrics: true,
  enableSecurityMonitoring: true,
};

/**
 * 请求监控中间件类
 */
export class RequestLoggerService {
  private config: RequestLogConfig;

  constructor(config: Partial<RequestLogConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 创建请求日志中间件
   */
  createMiddleware() {
    return {
      // 请求开始时的钩子
      onRequest: async (request: FastifyRequest, reply: FastifyReply) => {
        const startTime = Date.now();
        (request as any).startTime = startTime;
        (request as any).logId = randomUUID();

        // 检查是否应该记录此请求
        if (this.shouldSkipLogging(request)) {
          return;
        }

        // 安全监控检查
        if (this.config.enableSecurityMonitoring) {
          await this.performSecurityChecks(request);
        }
      },

      // 请求结束时的钩子
      onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
        const startTime = (request as any).startTime || Date.now();
        const responseTime = Date.now() - startTime;
        const logId = (request as any).logId;

        // 检查是否应该记录此请求
        if (this.shouldSkipLogging(request)) {
          return;
        }

        try {
          await this.logRequest(request, reply, responseTime, logId);
        } catch (error) {
          console.error('记录请求日志失败:', error);
        }
      },

      // 错误处理钩子
      onError: async (request: FastifyRequest, reply: FastifyReply, error: Error) => {
        const startTime = (request as any).startTime || Date.now();
        const responseTime = Date.now() - startTime;
        const logId = (request as any).logId;

        try {
          await this.logRequest(request, reply, responseTime, logId, error);
          
          // 记录安全事件（如果是安全相关错误）
          if (this.config.enableSecurityMonitoring && this.isSecurityError(error)) {
            await this.recordSecurityEvent(request, error);
          }
        } catch (logError) {
          console.error('记录错误请求日志失败:', logError);
        }
      },
    };
  }

  /**
   * 记录请求日志
   */
  private async logRequest(
    request: FastifyRequest,
    reply: FastifyReply,
    responseTime: number,
    logId?: string,
    error?: Error
  ): Promise<void> {
    try {
      // 获取用户信息
      let userId: string | null = null;
      try {
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === 'string') {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(', '));
          }
        });
        
        const session = await auth.api.getSession({ headers });
        userId = session?.user?.id || null;
      } catch {
        // 忽略获取用户信息的错误
      }

      // 准备日志数据
      const logData = {
        id: logId || randomUUID(),
        userId,
        method: request.method,
        path: request.url,
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'] as string,
        statusCode: reply.statusCode || (error ? 500 : 200),
        responseTime,
        requestSize: this.calculateRequestSize(request),
        responseSize: this.calculateResponseSize(reply),
        errorMessage: error?.message,
        referrer: request.headers.referer as string,
        createdAt: new Date(),
        metadata: this.buildMetadata(request, reply, error),
      };

      await db.insert(requestLog).values(logData);

      // 性能监控告警
      if (this.config.enablePerformanceMetrics && this.shouldAlertPerformance(responseTime, reply.statusCode)) {
        await this.recordPerformanceAlert(request, responseTime, reply.statusCode);
      }

    } catch (error) {
      console.error('记录请求日志时发生错误:', error);
    }
  }

  /**
   * 构建元数据
   */
  private buildMetadata(request: FastifyRequest, reply: FastifyReply, error?: Error): string {
    const metadata: any = {
      timestamp: new Date().toISOString(),
      logLevel: this.config.logLevel,
    };

    if (this.config.logLevel === 'detailed') {
      metadata.headers = this.sanitizeHeaders(request.headers);
      metadata.query = request.query;
    }

    if (this.config.enableRequestBody && request.body) {
      metadata.requestBody = this.sanitizeBody(request.body);
    }

    if (error) {
      metadata.error = {
        name: error.name,
        message: error.message,
        stack: this.config.logLevel === 'detailed' ? error.stack : undefined,
      };
    }

    return JSON.stringify(metadata);
  }

  /**
   * 执行安全检查
   */
  private async performSecurityChecks(request: FastifyRequest): Promise<void> {
    const suspiciousPatterns = [
      /(\.\.\/)|(\.\.\\)/g, // 路径遍历
      /<script[\s\S]*?>[\s\S]*?<\/script>/gi, // XSS
      /(union|select|insert|delete|update|drop|create|alter|exec|execute)/gi, // SQL注入
      /\/\*.*?\*\//g, // SQL注释
    ];

    const path = request.url;
    const userAgent = request.headers['user-agent'] as string || '';
    
    // 检查可疑路径
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(path) || pattern.test(userAgent)) {
        await this.recordSecurityEvent(request, null, 'suspicious_activity', 'medium', {
          reason: 'Suspicious pattern detected',
          pattern: pattern.toString(),
          path,
          userAgent,
        });
        break;
      }
    }

    // 检查异常的User-Agent
    if (this.isSuspiciousUserAgent(userAgent)) {
      await this.recordSecurityEvent(request, null, 'suspicious_activity', 'low', {
        reason: 'Suspicious User-Agent',
        userAgent,
      });
    }

    // 检查请求频率异常
    const clientIP = this.getClientIP(request);
    if (clientIP && await this.isHighFrequencyIP(clientIP)) {
      await this.recordSecurityEvent(request, null, 'suspicious_activity', 'medium', {
        reason: 'High frequency requests from IP',
        ipAddress: clientIP,
      });
    }
  }

  /**
   * 记录安全事件
   */
  private async recordSecurityEvent(
    request: FastifyRequest,
    error: Error | null,
    eventType: string = 'suspicious_activity',
    severity: string = 'medium',
    additionalDetails?: any
  ): Promise<void> {
    try {
      const eventData = {
        id: randomUUID(),
        userId: null, // 可以在有用户上下文时设置
        eventType,
        severity,
        ipAddress: this.getClientIP(request),
        userAgent: request.headers['user-agent'] as string,
        details: JSON.stringify({
          method: request.method,
          path: request.url,
          timestamp: new Date().toISOString(),
          error: error ? { name: error.name, message: error.message } : null,
          ...additionalDetails,
        }),
        resolved: false,
        createdAt: new Date(),
      };

      await db.insert(securityEvent).values(eventData);
    } catch (err) {
      console.error('记录安全事件失败:', err);
    }
  }

  /**
   * 记录性能告警
   */
  private async recordPerformanceAlert(
    request: FastifyRequest,
    responseTime: number,
    statusCode: number
  ): Promise<void> {
    const severity = responseTime > 5000 ? 'high' : responseTime > 2000 ? 'medium' : 'low';
    
    await this.recordSecurityEvent(
      request,
      null,
      'performance_issue',
      severity,
      {
        responseTime,
        statusCode,
        threshold: responseTime > 5000 ? '5s' : '2s',
      }
    );
  }

  /**
   * 工具方法
   */
  private shouldSkipLogging(request: FastifyRequest): boolean {
    const path = request.url?.split('?')[0];
    if (!path) return false;
    return this.config.excludePaths?.some(excludePath => 
      path.startsWith(excludePath)
    ) || false;
  }

  private getClientIP(request: FastifyRequest): string | undefined {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (request.headers['x-real-ip'] as string) ||
      request.ip ||
      request.socket.remoteAddress
    );
  }

  private calculateRequestSize(request: FastifyRequest): number | undefined {
    const contentLength = request.headers['content-length'];
    return contentLength ? parseInt(contentLength, 10) : undefined;
  }

  private calculateResponseSize(reply: FastifyReply): number | undefined {
    const contentLength = reply.getHeader('content-length');
    return contentLength ? parseInt(contentLength as string, 10) : undefined;
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    this.config.sensitiveFields?.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    });
    return sanitized;
  }

  private sanitizeBody(body: any): any {
    if (typeof body !== 'object') return body;
    
    const sanitized = JSON.parse(JSON.stringify(body));
    const sanitizeObject = (obj: any) => {
      if (typeof obj !== 'object' || obj === null) return obj;
      
      for (const [key, value] of Object.entries(obj)) {
        if (this.config.sensitiveFields?.some(field => 
          key.toLowerCase().includes(field.toLowerCase())
        )) {
          obj[key] = '***REDACTED***';
        } else if (typeof value === 'object') {
          sanitizeObject(value);
        }
      }
      return obj;
    };
    
    return sanitizeObject(sanitized);
  }

  private isSecurityError(error: Error): boolean {
    const securityErrorPatterns = [
      /unauthorized/i,
      /forbidden/i,
      /authentication/i,
      /permission/i,
      /access denied/i,
    ];
    
    return securityErrorPatterns.some(pattern => pattern.test(error.message));
  }

  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
    ];
    
    const legitimateBots = [
      /googlebot/i,
      /bingbot/i,
      /slackbot/i,
      /facebookexternalhit/i,
    ];
    
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    const isLegitimate = legitimateBots.some(pattern => pattern.test(userAgent));
    
    return isSuspicious && !isLegitimate;
  }

  private async isHighFrequencyIP(ipAddress: string): Promise<boolean> {
    // 简单的高频IP检测 - 检查最近5分钟内的请求数
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const result = await db
        .select()
        .from(requestLog)
        .where(sql`ip_address = ${ipAddress} AND created_at > ${fiveMinutesAgo}`)
        .limit(100);
      
      return result.length >= 100; // 5分钟内超过100个请求视为高频
    } catch {
      return false;
    }
  }

  private shouldAlertPerformance(responseTime: number, statusCode: number): boolean {
    // 响应时间超过2秒或状态码为5xx时触发性能告警
    return responseTime > 2000 || statusCode >= 500;
  }
}

/**
 * 创建默认的请求日志服务
 */
export const requestLoggerService = new RequestLoggerService();

/**
 * 创建不同级别的中间件
 */
export const minimalRequestLogger = new RequestLoggerService({
  logLevel: 'minimal',
  enableRequestBody: false,
  enableResponseBody: false,
  enablePerformanceMetrics: false,
}).createMiddleware();

export const standardRequestLogger = new RequestLoggerService({
  logLevel: 'standard',
  enablePerformanceMetrics: true,
  enableSecurityMonitoring: true,
}).createMiddleware();

export const detailedRequestLogger = new RequestLoggerService({
  logLevel: 'detailed',
  enableRequestBody: true,
  enableResponseBody: false,
  enablePerformanceMetrics: true,
  enableSecurityMonitoring: true,
}).createMiddleware();