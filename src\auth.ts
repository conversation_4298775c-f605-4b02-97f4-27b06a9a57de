import { betterAuth } from "better-auth";
import { openAPI } from "better-auth/plugins";
import { admin } from "better-auth/plugins/admin";
import { organization } from "better-auth/plugins/organization";
import { customSession } from "better-auth/plugins";
import { jwt } from "better-auth/plugins/jwt";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "./lib/drizzle.js";
import { emailService } from "./services/email-service.js";
import { inviteCodeContextManager } from "./services/invite-code-context.js";
import { checkMembershipLimit } from "./lib/organization.js";
import {
  member,
  organization as orgSchema,
  subscription,
  user,
} from "./lib/auth-schema.js";
import { eq, sql } from "drizzle-orm";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required");
}

if (!process.env.BETTER_AUTH_SECRET) {
  throw new Error("BETTER_AUTH_SECRET environment variable is required");
}

if (!process.env.RESEND_API_KEY) {
  throw new Error("RESEND_API_KEY environment variable is required");
}

// 动态获取所有 super_admin 和 admin 用户的ID
async function getDynamicAdminUserIds(): Promise<string[]> {
  try {
    console.log("🔍 Fetching dynamic admin user IDs...");
    
    const adminUsers = await db
      .select({ id: user.id, email: user.email, role: user.role })
      .from(user)
      .where(sql`${user.role} IN ('super_admin', 'admin')`);
    
    const adminUserIds = adminUsers.map(u => u.id);
    const adminEmails = adminUsers.map(u => u.email);
    const superAdmins = adminUsers.filter(u => u.role === 'super_admin');
    const regularAdmins = adminUsers.filter(u => u.role === 'admin');
    
    console.log(`✅ Found ${adminUserIds.length} admin users total:`);
    console.log(`🏆 Super admins: ${superAdmins.length} (${superAdmins.map(u => u.email).join(", ")})`);
    console.log(`👮 Regular admins: ${regularAdmins.length} (${regularAdmins.map(u => u.email).join(", ")})`);
    console.log(`🆔 All admin IDs: ${adminUserIds.join(", ")}`);
    
    return adminUserIds;
  } catch (error) {
    console.error("❌ Failed to fetch admin user IDs:", error);
    console.warn("⚠️ Continuing with empty admin user list - no dynamic admin access will be available");
    // 返回空数组避免启动失败，但记录错误
    return [];
  }
}

// 创建auth实例的异步函数
async function createAuthInstance() {
  // 获取动态admin用户IDs
  const dynamicAdminUserIds = await getDynamicAdminUserIds();

  return betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  secret: process.env.BETTER_AUTH_SECRET,
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:10086",
  basePath: "/api/auth",
  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:5173",
    "http://localhost:8000",
    "http://localhost:8080",
    "https://dev.goglobal.com",
    "https://www.goglobal.com",
  ],
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    minPasswordLength: 8,
    maxPasswordLength: 128,
    sendResetPasswordEmail: async ({
      user,
      url,
    }: {
      user: any;
      url: string;
    }) => {
      try {
        const result = await emailService.sendPasswordResetEmail({
          userEmail: user.email,
          userName: user.name,
          resetPasswordUrl: url,
        });

        if (!result.success) {
          console.error("密码重置邮件发送失败:", result.error);
          throw new Error(result.error || "密码重置邮件发送失败");
        }

        console.log("密码重置邮件发送成功:", {
          messageId: result.messageId,
          recipient: user.email,
        });
      } catch (error) {
        console.error("密码重置邮件发送错误:", error);
        throw error;
      }
    },
  },
  emailVerification: {
    sendOnSignUp: true,
    expiresIn: 3600,
    sendVerificationEmail: async ({
      user,
      url,
    }: {
      user: any;
      url: string;
    }) => {
      try {
        // 从邀请码上下文中获取邀请码（如果存在）
        const inviteCode = inviteCodeContextManager.getContext(user.email);
        
        // 修改验证URL指向前端地址
        const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
        const frontendVerifyUrl = `${frontendUrl}/verify-email?token=${encodeURIComponent(url.split('token=')[1] || '')}`;
        
        const result = await emailService.sendVerificationEmail({
          userEmail: user.email,
          userName: user.name,
          verificationUrl: frontendVerifyUrl,
          inviteCode: inviteCode || undefined,
        });

        if (!result.success) {
          console.error("邮箱验证邮件发送失败:", result.error);
          throw new Error(result.error || "邮箱验证邮件发送失败");
        }

        console.log("邮箱验证邮件发送成功:", {
          messageId: result.messageId,
          recipient: user.email,
          hasInviteCode: !!inviteCode,
        });
        
        // 邮件发送成功后清理上下文，避免内存泄漏
        if (inviteCode) {
          inviteCodeContextManager.cleanupContext(user.email);
        }
      } catch (error) {
        console.error("邮箱验证邮件发送错误:", error);
        throw error;
      }
    },
  },
  session: {
    freshAge: 60 * 60, // 1 hour
    expiresIn: 60 * 60 * 24 * 30, // 30 days
    updateAge: 60 * 60 * 24, // 24 hours
  },
  advanced: {
    crossSubDomainCookies: { enabled: true },
    defaultCookieAttributes: {
      secure: false,
      httpOnly: true,
      sameSite: "lax",
    },
  },

  plugins: [
    openAPI({
      enabled: true,
      path: "/auth-docs",
    }),
    jwt({
      jwks: {
        keyPairConfig: {
          alg: "PS256",
          modulusLength: 4096,
        },
      },
      jwt: {
        expirationTime: 60 * 60 * 24 * 7, // 7 days
        issuer: "specific-ai-auth",
        audience: "specific-ai-backend,specific-ai-frontend",
      },
    }),
    admin({
      defaultRole: "user",
      adminRoles: ["super_admin", "admin"],
      adminUserIds: dynamicAdminUserIds, // 使用动态获取的admin用户IDs
    }),
    organization({
      allowUserToCreateOrganization: false,
      organizationLimit: 0,
      creatorRole: "owner",
      membershipLimit: 100,
      hooks: {
        member: {
          create: {
            before: async (ctx: any) => {
              const memberStatus = await checkMembershipLimit(
                ctx.organizationId,
              );

              if (!memberStatus.canAddMember) {
                throw new Error(
                  `已达到 ${memberStatus.plan} 计划的成员上限 (${memberStatus.limit})。` +
                    (memberStatus.suggestedPlan
                      ? ` 请升级到 ${memberStatus.suggestedPlan} 计划以添加更多成员。`
                      : " 请联系管理员了解更多选项。"),
                );
              }

              return ctx;
            },
          },
        },
      },
    }),
    customSession(async (session, ctx) => {
      if (!session?.user) {
        return session;
      }

      try {
        // 单一优化查询：获取用户、组织、成员、订阅和成员计数信息
        const result = await db
          .select({
            // 用户信息
            userId: user.id,
            userName: user.name,
            userEmail: user.email,
            userEmailVerified: user.emailVerified,
            userImage: user.image,
            userCreatedAt: user.createdAt,
            userUpdatedAt: user.updatedAt,
            userRole: user.role,
            userBanned: user.banned,
            userBanReason: user.banReason,
            userBanExpires: user.banExpires,
            userCompanyName: user.companyName,
            userLanguage: user.language,
            // 组织信息
            orgId: orgSchema.id,
            orgName: orgSchema.name,
            orgSlug: orgSchema.slug,
            orgLogo: orgSchema.logo,
            orgCreatedAt: orgSchema.createdAt,
            orgMetadata: orgSchema.metadata,
            orgHasCompanyProfile: orgSchema.hasCompanyProfile,
            // 成员信息
            memberRole: member.role,
            memberCreatedAt: member.createdAt,
            // 订阅信息
            subscriptionPlan: subscription.plan,
            subscriptionMemberLimit: subscription.memberLimit,
          })
          .from(user)
          .leftJoin(member, eq(user.id, member.userId))
          .leftJoin(orgSchema, eq(member.organizationId, orgSchema.id))
          .leftJoin(subscription, eq(orgSchema.id, subscription.organizationId))
          .where(eq(user.id, session.user.id))
          .limit(1);

        const data = result[0];
        if (!data) {
          return session;
        }

        // 构建完整用户信息
        const completeUser = {
          ...session.user,
          name: data.userName,
          email: data.userEmail,
          emailVerified: data.userEmailVerified,
          image: data.userImage,
          createdAt: data.userCreatedAt,
          updatedAt: data.userUpdatedAt,
          role: data.userRole,
          banned: data.userBanned,
          banReason: data.userBanReason,
          banExpires: data.userBanExpires,
          companyName: data.userCompanyName,
          language: data.userLanguage,
        };

        // 如果没有组织信息，返回带完整用户信息的 session
        if (!data.orgId) {
          return {
            ...session,
            user: completeUser,
          };
        }

        // 获取当前组织成员数量（需要单独查询以获取准确计数）
        const [memberCountResult] = await db
          .select({ count: sql<number>`count(*)::int` })
          .from(member)
          .where(eq(member.organizationId, data.orgId));

        const currentMemberCount = memberCountResult?.count || 0;
        const memberLimit = data.subscriptionMemberLimit || 5; // 默认基础版限制
        const plan = data.subscriptionPlan || "basic";

        // 返回包含组织信息的完整 session
        return {
          ...session,
          user: completeUser,
          organization: {
            id: data.orgId,
            name: data.orgName,
            slug: data.orgSlug || "",
            logo: data.orgLogo,
            hasCompanyProfile: data.orgHasCompanyProfile || false,
            role: data.memberRole || "member",
            subscription: {
              plan,
              memberLimit,
              currentMemberCount,
              canAddMember: currentMemberCount < memberLimit,
              suggestedPlan: currentMemberCount >= memberLimit 
                ? (plan === "basic" ? "pro" : plan === "pro" ? "enterprise" : undefined)
                : undefined,
            },
          },
        };
      } catch (error) {
        console.error("Failed to extend session:", error);
        return session;
      }
    }),
  ],
  socialProviders: {
    google: { 
        prompt: "select_account", 
        clientId: process.env.GOOGLE_CLIENT_ID as string, 
        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string, 
    }, 
},
  });
}

// 创建auth实例
export const auth: any = await createAuthInstance();

export type Auth = typeof auth;
