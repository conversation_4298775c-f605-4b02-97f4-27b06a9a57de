-- ===================================
-- JWT插件数据库迁移SQL
-- 添加JWT插件需要的JWKS表
-- ===================================

-- 设置搜索路径
SET search_path TO auth, public;

-- ===================================
-- JWT插件核心表 - JWKS密钥存储
-- ===================================

-- JWKS表 - 存储JWT签名密钥对
CREATE TABLE IF NOT EXISTS auth.jwks (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    public_key TEXT NOT NULL,
    private_key TEXT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- 可选字段：密钥算法信息
    algorithm TEXT DEFAULT 'EdDSA',
    curve TEXT DEFAULT 'Ed25519',
    key_id TEXT UNIQUE DEFAULT gen_random_uuid()::TEXT,
    
    -- 密钥状态
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITHOUT TIME ZONE,
    
    -- 元数据
    metadata JSONB DEFAULT '{}'
);

-- ===================================
-- 索引优化
-- ===================================

-- JWKS表索引
CREATE INDEX IF NOT EXISTS idx_jwks_key_id ON auth.jwks(key_id);
CREATE INDEX IF NOT EXISTS idx_jwks_active ON auth.jwks(active);
CREATE INDEX IF NOT EXISTS idx_jwks_created_at ON auth.jwks(created_at);
CREATE INDEX IF NOT EXISTS idx_jwks_expires_at ON auth.jwks(expires_at) WHERE expires_at IS NOT NULL;

-- ===================================
-- 权限设置
-- ===================================

-- 为应用用户授予JWKS表权限
DO $$
BEGIN
    -- 检查用户是否存在
    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'specific_ai_auth') THEN
        -- 授予JWKS表权限
        GRANT SELECT, INSERT, UPDATE, DELETE ON auth.jwks TO specific_ai_auth;
        
        RAISE NOTICE 'JWKS table permissions granted to specific_ai_auth user';
    ELSE
        RAISE NOTICE 'User specific_ai_auth does not exist, skipping permission grant';
    END IF;
END $$;

-- ===================================
-- 清理函数更新
-- ===================================

-- 更新清理函数以包含过期的JWKS密钥
CREATE OR REPLACE FUNCTION auth.cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的验证记录
    DELETE FROM auth.verification WHERE expires_at < NOW();
    
    -- 清理过期的会话记录
    DELETE FROM auth.session WHERE expires_at < NOW();
    
    -- 清理过期的JWKS密钥
    DELETE FROM auth.jwks WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    -- 清理30天前的请求日志
    DELETE FROM auth.request_log WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 清理7天前的频率限制记录
    DELETE FROM auth.rate_limit WHERE window_end < NOW() - INTERVAL '7 days';
    
    -- 清理90天前的邮件日志
    DELETE FROM auth.email_log WHERE created_at < NOW() - INTERVAL '90 days';
    
    RAISE NOTICE 'Expired data cleanup completed at %', NOW();
END;
$$ LANGUAGE 'plpgsql';

-- ===================================
-- 验证安装
-- ===================================

-- 检查JWKS表是否成功创建
SELECT 
    'JWKS table created successfully' as status,
    COUNT(*) as current_keys
FROM auth.jwks;

-- 显示表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'auth' 
AND table_name = 'jwks'
ORDER BY ordinal_position;

-- 显示完成信息
SELECT 'JWT plugin database migration completed successfully!' as status;