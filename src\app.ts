import Fastify, { FastifyRequest, FastifyReply } from "fastify";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import swagger from "@fastify/swagger";
import swaggerUi from "@fastify/swagger-ui";
import { auth } from "./auth.js";
import { registerAuthRoutes } from "./routes/auth.js";
import { registerOrganizationRoutes } from "./routes/organization.js";
// import { registerPendingRegistrationRoutes } from "./routes/pending-registration.js";
import { jwtRoutes } from "./routes/jwt.js";
import { validateEnvConfig } from "./lib/env.js";
// 不再需要导入插件，使用内联中间件
// import {
//   responseFormatterPlugin,
//   formatSuccessResponse,
//   formatErrorResponse,
// } from "./middleware/response-formatter.js";

// 创建Fastify实例
export const app = Fastify({
  logger: false,
});

// 应用初始化函数
export const initializeApp = async () => {
  // 验证环境变量配置
  validateEnvConfig();

  // 验证数据库连接
  try {
    console.log("🔌 Testing database connection...");
    await auth.api.getSession({ headers: new Headers() });
    console.log("✅ Database connection verified");
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    throw new Error(
      `Database connection failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }

  // 注册安全中间件
  await app.register(helmet, {
    hsts: false,
    contentSecurityPolicy: {
      directives: {
        ...helmet.contentSecurityPolicy.getDefaultDirectives(),
        "script-src": [
          "'self'",
          "'unsafe-inline'",
          "https://cdn.jsdelivr.net",
          "https://unpkg.com",
        ],
        "style-src": ["'self'", "'unsafe-inline'"],
        "img-src": ["'self'", "data:", "*"],
        "connect-src": ["'self'", "*"],
        "font-src": ["'self'", "data:", "*"],
        "frame-src": ["'self'", "*"],
      },
    },
    crossOriginOpenerPolicy: false,
  });

  // 注册CORS中间件
  await app.register(cors, {
    origin: (origin, callback) => {
      const corsStrict = process.env.CORS_STRICT === "true";
      if (!corsStrict) {
        callback(null, true);
        return;
      }

      const allowedOrigins = process.env.CORS_ORIGIN
        ? process.env.CORS_ORIGIN.split(",").map((o) => o.trim())
        : [];

      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.warn(`[${new Date().toISOString()}] CORS blocked origin: ${origin}`);
        callback(new Error("Not allowed by CORS"), false);
      }
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
      "Origin",
      "Cookie",
      "Set-Cookie",
      "x-better-auth-return-to",
    ],
    exposedHeaders: ["Set-Cookie", "Authorization"],
    maxAge: 86400,
  });

  // 注册Swagger文档
  await app.register(swagger, {
    openapi: {
      openapi: "3.0.0",
      info: {
        title: "Better Auth Service API",
        description: "基于Better Auth + Fastify的最简邮箱认证系统API文档",
        version: "1.0.0",
      },
      tags: [
        { name: "Auth", description: "认证相关API" },
        { name: "Registration", description: "临时注册系统API" },
        { name: "JWT", description: "JWT Token管理API" },
        { name: "Organization", description: "组织管理API" },
        { name: "Admin", description: "管理员功能API" },
        { name: "Health", description: "健康检查" },
      ],
      components: {
        securitySchemes: {
          BearerAuth: { type: "http", scheme: "bearer", bearerFormat: "JWT" },
          CookieAuth: {
            type: "apiKey",
            in: "cookie",
            name: "better-auth.session_token",
          },
        },
      },
      security: [{ BearerAuth: [] }, { CookieAuth: [] }],
    },
  });

  // 注册Swagger UI
  await app.register(swaggerUi, {
    routePrefix: "/docs",
    uiConfig: {
      docExpansion: "full",
      deepLinking: false,
      url: "./json",
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
    transformSpecification: (swaggerObject, request) => {
      const protoHeader = request.headers["x-forwarded-proto"];
      const hostHeader = request.headers["x-forwarded-host"];

      const protocol =
        (Array.isArray(protoHeader) ? protoHeader[0] : protoHeader) ||
        request.protocol;
      const host =
        (Array.isArray(hostHeader) ? hostHeader[0] : hostHeader) ||
        request.hostname;

      let port = host.split(":")[1] || "";
      if (!port) {
        const address = app.server.address();
        if (address && typeof address === "object" && address.port) {
          port = String(address.port);
        }
      }

      const portString =
        (protocol === "http" && port === "80") ||
        (protocol === "https" && port === "443") ||
        !port
          ? ""
          : `:${port}`;
      const serverHost = host.split(":")[0];

      const currentServerUrl = `${protocol}://${serverHost}${portString}`;

      const transformedSpec = JSON.parse(JSON.stringify(swaggerObject));

      transformedSpec.servers = [
        {
          url: currentServerUrl,
          description: "Current Server",
        },
      ];

      return transformedSpec;
    },
    transformSpecificationClone: true,
  });

  // 简洁的日志中间件
  app.addHook("onRequest", async (request, _reply) => {
    (request as any).startTime = Date.now();
  });

  app.addHook("onResponse", async (request, reply) => {
    const responseTime = Date.now() - ((request as any).startTime || Date.now());
    const timestamp = new Date().toISOString();
    
    let logMessage = `[${timestamp}] ${request.method} ${request.url} → ${reply.statusCode} (${responseTime}ms)`;
    
    // 对于非GET请求，显示请求体（敏感信息脱敏）
    if (request.method !== "GET" && request.body) {
      let bodyStr = "";
      try {
        const body = typeof request.body === "string" ? JSON.parse(request.body) : request.body;
        const sanitizedBody = { ...body };
        
        // 脱敏敏感字段
        if (sanitizedBody.password) sanitizedBody.password = "***";
        if (sanitizedBody.email) sanitizedBody.email = sanitizedBody.email.replace(/(.{1,3}).*@/, "$1***@");
        if (sanitizedBody.token) sanitizedBody.token = "***";
        
        bodyStr = ` body:${JSON.stringify(sanitizedBody)}`;
      } catch {
        bodyStr = " body:[invalid-json]";
      }
      logMessage += bodyStr;
    }
    
    console.log(logMessage);
  });

  // 直接添加响应格式化中间件到主应用实例
  console.log('[ResponseFormatter] Adding middleware to main app instance');
  app.addHook(
    "onSend",
    async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      const url = request.url;
      
      // 调试日志
      console.log(`[ResponseFormatter] Processing: ${request.method} ${url}`);

      try {
        // 检查是否为Better Auth原生路径，如果是则不处理
        const betterAuthPaths = [
          "/api/auth/sign-in",
          "/api/auth/sign-up", 
          "/api/auth/sign-out",
          "/api/auth/get-session",
          "/api/auth/reset-password",
          "/api/auth/callback",
          "/api/auth/jwks",
        ];
        
        // 注意：不包括 /api/auth/verify-email 因为我们有自定义验证逻辑
        
        // 精确匹配，避免 /api/auth/sign-up-invite 被误识别为 /api/auth/sign-up
        const isBetterAuth = betterAuthPaths.some(path => {
          // 精确匹配路径或者以路径+查询参数开头
          return url === path || url.startsWith(path + '?') || url.startsWith(path + '/');
        });
        console.log(`[ResponseFormatter] Checking if ${url} is Better Auth path: ${isBetterAuth}`);
        
        if (isBetterAuth) {
          console.log(`[ResponseFormatter] Skipping Better Auth path: ${url}`);
          return payload;
        }
        
        // 如果响应已经是标准格式，直接返回
        if (payload && typeof payload === "object" && "code" in payload && "data" in payload && "success" in payload && "message" in payload) {
          console.log(`[ResponseFormatter] Already standard format: ${url}`);
          return payload;
        }

        // 解析payload
        let data;
        try {
          data = typeof payload === "string" ? JSON.parse(payload) : payload;
        } catch {
          data = payload;
        }
        
        console.log(`[ResponseFormatter] Formatting response for: ${url}, status: ${reply.statusCode}`);

        // 根据状态码判断是成功还是错误响应
        const statusCode = reply.statusCode;
        const isSuccess = statusCode >= 200 && statusCode < 300;

        let formattedResponse;

        if (isSuccess) {
          // 成功响应 - 自定义接口使用标准格式
          formattedResponse = {
            code: statusCode,
            data,
            success: true,
            message: "success"
          };
        } else {
          // 错误响应
          let errorMessage = "Internal Server Error";
          let errorData = null;

          if (data && typeof data === "object") {
            if (data.message) {
              errorMessage = data.message;
            } else if (data.error) {
              errorMessage = data.error;
            }
            errorData = data;
          } else if (typeof data === "string") {
            errorMessage = data;
          }

          formattedResponse = {
            code: statusCode,
            data: errorData,
            success: false,
            message: errorMessage
          };
        }
        
        console.log(`[ResponseFormatter] Formatted response:`, formattedResponse);
        return JSON.stringify(formattedResponse);
      } catch (error) {
        // 如果格式化失败，返回默认错误格式
        console.error(`[ResponseFormatter] Error formatting response for ${url}:`, error);
        const errorResponse = {
          code: 500,
          data: null,
          success: false,
          message: "Response formatting error"
        };
        return JSON.stringify(errorResponse);
      }
    },
  );

  // 注册自定义认证路由
  await registerAuthRoutes(app);

  // 注册临时注册路由
  // await registerPendingRegistrationRoutes(app);

  // 注册组织管理路由
  await registerOrganizationRoutes(app);

  // 注册JWT Token管理路由
  await app.register(jwtRoutes, { prefix: "/api/auth" });



  // Better Auth 路由 - 使用更精确的路径匹配
  app.all("/api/auth/*", async (request, reply) => {
    // 检查是否为自定义路由，如果是则跳过
    const customPaths = [
      "/api/auth/validate-invite-code",
      "/api/auth/sign-up-invite", 
      "/api/auth/create-super-admin",
      "/api/auth/create-admin",
      "/api/auth/user/organizations",
      "/api/auth/user/language",
      "/api/auth/resend-verification",
      "/api/auth/resend-pending-verification",
      "/api/auth/verify-email-status",
      "/api/auth/verify-email-callback",
      "/api/auth/verify-pending-registration",
      "/api/auth/check-email-verification",
      "/api/auth/languages",
      "/api/auth/switch-language",
      "/api/auth/organization",
      "/api/auth/jwt",
      "/api/auth/verify-email" // 添加这个路径，让自定义验证逻辑处理
    ];
    
    const isCustomPath = customPaths.some(path => 
      request.url.startsWith(path) || request.url.includes(path)
    );
    
    if (isCustomPath) {
      // 跳过，让自定义路由处理
      return reply.callNotFound();
    }
    
    try {
      const url = new URL(request.url, `http://${request.headers.host}`);
      const headers = new Headers();
      for (const [key, value] of Object.entries(request.headers)) {
        if (typeof value === "string") {
          headers.set(key, value);
        } else if (Array.isArray(value)) {
          headers.set(key, value.join(", "));
        }
      }

      let body: string | undefined;
      if (
        request.method !== "GET" &&
        request.method !== "HEAD" &&
        request.body
      ) {
        if (typeof request.body === "object" && request.body !== null) {
          body = JSON.stringify(request.body);
          if (!headers.has("content-type")) {
            headers.set("content-type", "application/json");
          }
        } else {
          body = String(request.body);
        }
      }

      const webRequest = new Request(url.toString(), {
        method: request.method,
        headers,
        body,
      });

      const response = await auth.handler(webRequest);

      reply.status(response.status);
      response.headers.forEach((value: string, key: string) => {
        if (key.toLowerCase() === "set-cookie") {
          const cookies = (response.headers as any).getSetCookie();
          reply.header(key, cookies);
        } else {
          reply.header(key, value);
        }
      });

      const contentType = response.headers.get("content-type");
      const responseText = await response.text();

      if (!contentType || !contentType.includes("application/json")) {
        return reply.send(responseText);
      }

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      return reply.send(responseData);

      // if (response.status >= 200 && response.status < 300) {
      //   return formatSuccessResponse(responseData, "success", response.status);
      // } else {
      //   const errorMessage =
      //     responseData?.message || responseData?.error || "Request failed";
      //   return formatErrorResponse(errorMessage, response.status, responseData);
      // }
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Better Auth handler error:`, error);
      return reply
        .status(500)
        .send({
          error: "INTERNAL_ERROR",
          message: error instanceof Error
            ? error.message
            : "Authentication handler failed",
        });
    }
  });

  // 健康检查端点
  app.get(
    "/api/health",
    {
      schema: {
        description: "健康检查端点",
        tags: ["Health"],
        response: {
          200: {
            type: "object",
            properties: {
              status: { type: "string" },
              timestamp: { type: "string" },
              uptime: { type: "number" },
              environment: { type: "string" },
            },
          },
        },
      },
    },
    async (_request, _reply) => {
      return {
        status: "ok",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || "development",
      };
    },
  );

  // 根路径重定向到文档
  app.get(
    "/",
    {
      schema: {
        description: "根路径，重定向到API文档",
        tags: ["Health"],
        response: {
          302: {
            description: "Redirect to /docs",
            type: "null",
          },
        },
      },
    },
    async (_request, reply) => {
      reply.redirect("/docs");
    },
  );

  // 全局错误处理
  app.setErrorHandler(async (error, _request, reply) => {
    console.error(`[${new Date().toISOString()}] Error:`, error);
    const statusCode = error.statusCode || 500;

    if (error.validation) {
      reply.status(400).send({
        error: "Validation Failed",
        message: error.message,
        details: error.validation,
        statusCode: 400,
      });
      return;
    }

    reply.status(statusCode).send({
      error: error.name || "Internal Server Error",
      message:
        process.env.NODE_ENV === "production" && statusCode >= 500
          ? "An unexpected error occurred"
          : error.message,
      statusCode,
    });
  });

  // 404 处理
  app.setNotFoundHandler(async (request, reply) => {
    reply.status(404).send({
      error: "Not Found",
      message: `Route ${request.method}:${request.url} not found`,
      statusCode: 404,
    });
  });
};

export default app;
