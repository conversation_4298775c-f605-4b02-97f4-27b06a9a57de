/**
 * 数据清理服务
 * 自动清理过期的临时注册记录和相关验证日志
 */

import { db } from "../lib/drizzle.js";
import { 
  pendingRegistration, 
  registrationVerificationLog,
  emailLog,
  rateLimiter,
  verification
} from "../lib/auth-schema.js";
import { eq, and, lt, sql, inArray } from "drizzle-orm";

// 清理配置接口
export interface CleanupConfig {
  // 基本清理配置
  enableCleanup: boolean;
  maxRetries: number;
  batchSize: number;
  
  // 数据保留策略
  retentionPolicies: {
    expiredPendingRegistrations: number; // 过期后额外保留天数
    completedRegistrations: number; // 已完成注册记录保留天数
    failedRegistrations: number; // 失败注册记录保留天数
    verificationLogs: number; // 验证日志保留天数
    emailLogs: number; // 邮件日志保留天数
    rateLimiterRecords: number; // 频率限制记录保留天数
  };
  
  // 备份配置
  backupBeforeCleanup: boolean;
  backupRetentionDays: number;
  
  // 监控配置
  enableMetrics: boolean;
  enableAlerts: boolean;
  alertThresholds: {
    maxCleanupTimeMs: number;
    maxRecordsPerBatch: number;
    minCleanupSuccessRate: number;
  };
}

// 清理结果接口
export interface CleanupResult {
  success: boolean;
  totalProcessed: number;
  recordsCleaned: {
    expiredPendingRegistrations: number;
    oldCompletedRegistrations: number;
    oldFailedRegistrations: number;
    orphanedVerificationLogs: number;
    oldEmailLogs: number;
    oldRateLimiterRecords: number;
    oldBetterAuthVerifications: number;
  };
  processingTimeMs: number;
  errors: string[];
  warnings: string[];
  backupInfo?: {
    created: boolean;
    location?: string;
    recordCount: number;
  };
}

// 清理统计接口
export interface CleanupStats {
  lastCleanupAt: Date;
  totalCleanupsRun: number;
  totalRecordsCleaned: number;
  averageProcessingTimeMs: number;
  successRate: number;
  recentErrors: Array<{
    timestamp: Date;
    error: string;
    context: string;
  }>;
}

/**
 * 数据清理服务类
 */
export class DataCleanupService {
  private config: CleanupConfig;
  private stats: CleanupStats;
  
  constructor(config?: Partial<CleanupConfig>) {
    this.config = {
      enableCleanup: true,
      maxRetries: 3,
      batchSize: 1000,
      retentionPolicies: {
        expiredPendingRegistrations: 7, // 过期后再保留7天
        completedRegistrations: 30, // 已完成的保留30天
        failedRegistrations: 14, // 失败的保留14天
        verificationLogs: 30, // 验证日志保留30天
        emailLogs: 60, // 邮件日志保留60天
        rateLimiterRecords: 7, // 频率限制记录保留7天
      },
      backupBeforeCleanup: true,
      backupRetentionDays: 90,
      enableMetrics: true,
      enableAlerts: true,
      alertThresholds: {
        maxCleanupTimeMs: 300000, // 5分钟
        maxRecordsPerBatch: 10000,
        minCleanupSuccessRate: 0.95,
      },
      ...config,
    };
    
    this.stats = {
      lastCleanupAt: new Date(0),
      totalCleanupsRun: 0,
      totalRecordsCleaned: 0,
      averageProcessingTimeMs: 0,
      successRate: 1.0,
      recentErrors: [],
    };
  }
  
  /**
   * 执行完整的数据清理流程
   */
  async runCleanup(): Promise<CleanupResult> {
    const startTime = Date.now();
    const result: CleanupResult = {
      success: false,
      totalProcessed: 0,
      recordsCleaned: {
        expiredPendingRegistrations: 0,
        oldCompletedRegistrations: 0,
        oldFailedRegistrations: 0,
        orphanedVerificationLogs: 0,
        oldEmailLogs: 0,
        oldRateLimiterRecords: 0,
        oldBetterAuthVerifications: 0,
      },
      processingTimeMs: 0,
      errors: [],
      warnings: [],
    };
    
    if (!this.config.enableCleanup) {
      result.warnings.push("数据清理功能已禁用");
      return result;
    }
    
    try {
      console.log("开始执行数据清理任务...");
      
      // 1. 创建备份（如果启用）
      if (this.config.backupBeforeCleanup) {
        const backupResult = await this.createBackup();
        result.backupInfo = backupResult;
        if (!backupResult.created) {
          result.warnings.push("备份创建失败，但继续执行清理");
        }
      }
      
      // 2. 清理过期的临时注册记录
      const expiredCount = await this.cleanupExpiredPendingRegistrations();
      result.recordsCleaned.expiredPendingRegistrations = expiredCount;
      result.totalProcessed += expiredCount;
      
      // 3. 清理旧的已完成注册记录
      const completedCount = await this.cleanupOldCompletedRegistrations();
      result.recordsCleaned.oldCompletedRegistrations = completedCount;
      result.totalProcessed += completedCount;
      
      // 4. 清理旧的失败注册记录
      const failedCount = await this.cleanupOldFailedRegistrations();
      result.recordsCleaned.oldFailedRegistrations = failedCount;
      result.totalProcessed += failedCount;
      
      // 5. 清理孤立的验证日志
      const orphanedLogsCount = await this.cleanupOrphanedVerificationLogs();
      result.recordsCleaned.orphanedVerificationLogs = orphanedLogsCount;
      result.totalProcessed += orphanedLogsCount;
      
      // 6. 清理旧的邮件日志
      const emailLogsCount = await this.cleanupOldEmailLogs();
      result.recordsCleaned.oldEmailLogs = emailLogsCount;
      result.totalProcessed += emailLogsCount;
      
      // 7. 清理旧的频率限制记录
      const rateLimiterCount = await this.cleanupOldRateLimiterRecords();
      result.recordsCleaned.oldRateLimiterRecords = rateLimiterCount;
      result.totalProcessed += rateLimiterCount;
      
      // 8. 清理 Better Auth 的旧验证记录
      const betterAuthVerificationsCount = await this.cleanupOldBetterAuthVerifications();
      result.recordsCleaned.oldBetterAuthVerifications = betterAuthVerificationsCount;
      result.totalProcessed += betterAuthVerificationsCount;
      
      // 9. 清理旧的备份文件
      await this.cleanupOldBackups();
      
      result.success = true;
      result.processingTimeMs = Date.now() - startTime;
      
      // 更新统计信息
      await this.updateStats(result);
      
      console.log(`数据清理完成: 处理了 ${result.totalProcessed} 条记录，耗时 ${result.processingTimeMs}ms`);
      
      // 检查告警条件
      this.checkAlertConditions(result);
      
    } catch (error) {
      result.success = false;
      result.processingTimeMs = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : String(error);
      result.errors.push(errorMsg);
      
      console.error("数据清理失败:", errorMsg);
      
      // 记录错误到统计中
      this.stats.recentErrors.push({
        timestamp: new Date(),
        error: errorMsg,
        context: "runCleanup",
      });
      
      // 保持最近错误记录数量限制
      if (this.stats.recentErrors.length > 10) {
        this.stats.recentErrors = this.stats.recentErrors.slice(-10);
      }
    }
    
    return result;
  }
  
  /**
   * 清理过期的临时注册记录
   */
  private async cleanupExpiredPendingRegistrations(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.expiredPendingRegistrations * 24 * 60 * 60 * 1000
      );
      
      // 首先清理相关的验证日志（通过级联删除会自动处理）
      const result = await db.transaction(async (tx) => {
        // 获取要删除的记录ID列表
        const toDelete = await tx
          .select({ id: pendingRegistration.id })
          .from(pendingRegistration)
          .where(
            and(
              lt(pendingRegistration.expiresAt, cutoffDate),
              inArray(pendingRegistration.status, ['pending', 'expired'])
            )
          )
          .limit(this.config.batchSize);
        
        if (toDelete.length === 0) {
          return 0;
        }
        
        const ids = toDelete.map(r => r.id);
        
        // 删除相关的验证日志
        await tx
          .delete(registrationVerificationLog)
          .where(inArray(registrationVerificationLog.pendingRegistrationId, ids));
        
        // 删除临时注册记录
        const deleteResult = await tx
          .delete(pendingRegistration)
          .where(inArray(pendingRegistration.id, ids));
        
        return deleteResult.length || 0;
      });
      
      console.log(`清理了 ${result} 条过期的临时注册记录`);
      return result;
      
    } catch (error) {
      console.error("清理过期临时注册记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理旧的已完成注册记录
   */
  private async cleanupOldCompletedRegistrations(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.completedRegistrations * 24 * 60 * 60 * 1000
      );
      
      const result = await db.transaction(async (tx) => {
        // 获取要删除的记录
        const toDelete = await tx
          .select({ id: pendingRegistration.id })
          .from(pendingRegistration)
          .where(
            and(
              eq(pendingRegistration.status, 'verified'),
              lt(pendingRegistration.verifiedAt, cutoffDate)
            )
          )
          .limit(this.config.batchSize);
        
        if (toDelete.length === 0) {
          return 0;
        }
        
        const ids = toDelete.map(r => r.id);
        
        // 删除相关验证日志
        await tx
          .delete(registrationVerificationLog)
          .where(inArray(registrationVerificationLog.pendingRegistrationId, ids));
        
        // 删除已完成的注册记录
        const deleteResult = await tx
          .delete(pendingRegistration)
          .where(inArray(pendingRegistration.id, ids));
        
        return deleteResult.length || 0;
      });
      
      console.log(`清理了 ${result} 条旧的已完成注册记录`);
      return result;
      
    } catch (error) {
      console.error("清理已完成注册记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理旧的失败注册记录
   */
  private async cleanupOldFailedRegistrations(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.failedRegistrations * 24 * 60 * 60 * 1000
      );
      
      const result = await db.transaction(async (tx) => {
        const toDelete = await tx
          .select({ id: pendingRegistration.id })
          .from(pendingRegistration)
          .where(
            and(
              eq(pendingRegistration.status, 'failed'),
              lt(pendingRegistration.updatedAt, cutoffDate)
            )
          )
          .limit(this.config.batchSize);
        
        if (toDelete.length === 0) {
          return 0;
        }
        
        const ids = toDelete.map(r => r.id);
        
        await tx
          .delete(registrationVerificationLog)
          .where(inArray(registrationVerificationLog.pendingRegistrationId, ids));
        
        const deleteResult = await tx
          .delete(pendingRegistration)
          .where(inArray(pendingRegistration.id, ids));
        
        return deleteResult.length || 0;
      });
      
      console.log(`清理了 ${result} 条旧的失败注册记录`);
      return result;
      
    } catch (error) {
      console.error("清理失败注册记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理孤立的验证日志
   */
  private async cleanupOrphanedVerificationLogs(): Promise<number> {
    try {
      const result = await db.execute(sql`
        DELETE FROM auth.registration_verification_log 
        WHERE pending_registration_id NOT IN (
          SELECT id FROM auth.pending_registration
        )
        AND created_at < NOW() - INTERVAL '${sql.raw(this.config.retentionPolicies.verificationLogs.toString())} days'
      `);
      
      const deletedCount = result.length || 0;
      console.log(`清理了 ${deletedCount} 条孤立的验证日志`);
      return deletedCount;
      
    } catch (error) {
      console.error("清理孤立验证日志失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理旧的邮件日志
   */
  private async cleanupOldEmailLogs(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.emailLogs * 24 * 60 * 60 * 1000
      );
      
      const result = await db
        .delete(emailLog)
        .where(lt(emailLog.createdAt, cutoffDate));
      
      const deletedCount = result.length || 0;
      console.log(`清理了 ${deletedCount} 条旧的邮件日志`);
      return deletedCount;
      
    } catch (error) {
      console.error("清理邮件日志失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理旧的频率限制记录
   */
  private async cleanupOldRateLimiterRecords(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.rateLimiterRecords * 24 * 60 * 60 * 1000
      );
      
      const result = await db
        .delete(rateLimiter)
        .where(
          and(
            lt(rateLimiter.createdAt, cutoffDate),
            eq(rateLimiter.blocked, false) // 保留仍被阻止的记录
          )
        );
      
      const deletedCount = result.length || 0;
      console.log(`清理了 ${deletedCount} 条旧的频率限制记录`);
      return deletedCount;
      
    } catch (error) {
      console.error("清理频率限制记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 清理 Better Auth 的旧验证记录
   */
  private async cleanupOldBetterAuthVerifications(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.retentionPolicies.verificationLogs * 24 * 60 * 60 * 1000
      );
      
      const result = await db
        .delete(verification)
        .where(
          and(
            lt(verification.expiresAt, cutoffDate),
            lt(verification.createdAt, cutoffDate)
          )
        );
      
      const deletedCount = result.length || 0;
      console.log(`清理了 ${deletedCount} 条 Better Auth 旧验证记录`);
      return deletedCount;
      
    } catch (error) {
      console.error("清理 Better Auth 验证记录失败:", error);
      return 0;
    }
  }
  
  /**
   * 创建数据备份
   */
  private async createBackup(): Promise<{ created: boolean; location?: string; recordCount: number }> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupTable = `pending_registration_backup_${timestamp}`;
      
      // 创建备份表并复制数据
      const result = await db.execute(sql`
        CREATE TABLE auth.${sql.raw(backupTable)} AS 
        SELECT * FROM auth.pending_registration 
        WHERE expires_at < NOW() OR status IN ('verified', 'failed', 'expired')
      `);
      
      // 获取备份记录数量
      const countResult = await db.execute(sql`
        SELECT COUNT(*) as count FROM auth.${sql.raw(backupTable)}
      `);
      
      const recordCount = Number(countResult[0]?.count || 0);
      
      console.log(`创建备份表 ${backupTable}，包含 ${recordCount} 条记录`);
      
      return {
        created: true,
        location: `auth.${backupTable}`,
        recordCount,
      };
      
    } catch (error) {
      console.error("创建备份失败:", error);
      return {
        created: false,
        recordCount: 0,
      };
    }
  }
  
  /**
   * 清理旧的备份文件
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const cutoffDate = new Date(
        Date.now() - this.config.backupRetentionDays * 24 * 60 * 60 * 1000
      );
      const cutoffTimestamp = cutoffDate.toISOString().replace(/[:.]/g, '-');
      
      // 查找旧的备份表
      const tablesResult = await db.execute(sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'auth' 
          AND table_name LIKE 'pending_registration_backup_%'
          AND table_name < ${'pending_registration_backup_' + cutoffTimestamp}
      `);
      
      // 删除旧的备份表
      for (const row of tablesResult) {
        const tableName = row.table_name as string;
        try {
          await db.execute(sql`DROP TABLE auth.${sql.raw(tableName)}`);
          console.log(`删除旧备份表: ${tableName}`);
        } catch (error) {
          console.warn(`删除备份表失败 ${tableName}:`, error);
        }
      }
      
    } catch (error) {
      console.error("清理旧备份失败:", error);
    }
  }
  
  /**
   * 更新清理统计信息
   */
  private async updateStats(result: CleanupResult): Promise<void> {
    this.stats.lastCleanupAt = new Date();
    this.stats.totalCleanupsRun += 1;
    this.stats.totalRecordsCleaned += result.totalProcessed;
    
    // 更新平均处理时间
    this.stats.averageProcessingTimeMs = 
      (this.stats.averageProcessingTimeMs * (this.stats.totalCleanupsRun - 1) + result.processingTimeMs) / 
      this.stats.totalCleanupsRun;
    
    // 更新成功率
    const successfulRuns = result.success ? this.stats.totalCleanupsRun : this.stats.totalCleanupsRun - 1;
    this.stats.successRate = successfulRuns / this.stats.totalCleanupsRun;
  }
  
  /**
   * 检查告警条件
   */
  private checkAlertConditions(result: CleanupResult): void {
    const alerts: string[] = [];
    
    if (result.processingTimeMs > this.config.alertThresholds.maxCleanupTimeMs) {
      alerts.push(`清理时间过长: ${result.processingTimeMs}ms > ${this.config.alertThresholds.maxCleanupTimeMs}ms`);
    }
    
    if (result.totalProcessed > this.config.alertThresholds.maxRecordsPerBatch) {
      alerts.push(`单次清理记录数过多: ${result.totalProcessed} > ${this.config.alertThresholds.maxRecordsPerBatch}`);
    }
    
    if (this.stats.successRate < this.config.alertThresholds.minCleanupSuccessRate) {
      alerts.push(`清理成功率过低: ${this.stats.successRate} < ${this.config.alertThresholds.minCleanupSuccessRate}`);
    }
    
    if (result.errors.length > 0) {
      alerts.push(`清理过程中出现错误: ${result.errors.join(', ')}`);
    }
    
    if (alerts.length > 0 && this.config.enableAlerts) {
      console.warn("数据清理告警:", alerts);
      // 这里可以集成实际的告警系统（邮件、Slack、钉钉等）
      this.sendAlert(alerts);
    }
  }
  
  /**
   * 发送告警（可扩展）
   */
  private sendAlert(alerts: string[]): void {
    // 实现告警发送逻辑
    // 例如：发送邮件、Slack通知、钉钉消息等
    console.error("【数据清理告警】", {
      timestamp: new Date().toISOString(),
      alerts,
      stats: this.stats,
    });
  }
  
  /**
   * 获取清理统计信息
   */
  getStats(): CleanupStats {
    return { ...this.stats };
  }
  
  /**
   * 获取清理配置
   */
  getConfig(): CleanupConfig {
    return { ...this.config };
  }
  
  /**
   * 更新清理配置
   */
  updateConfig(newConfig: Partial<CleanupConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
  
  /**
   * 执行数据库健康检查
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    issues: string[];
    metrics: {
      pendingRegistrationCount: number;
      expiredRegistrationCount: number;
      verificationLogCount: number;
      emailLogCount: number;
    };
  }> {
    const issues: string[] = [];
    
    try {
      // 检查待清理的记录数量
      const metricsResult = await db.execute(sql`
        SELECT 
          (SELECT COUNT(*) FROM auth.pending_registration) as pending_count,
          (SELECT COUNT(*) FROM auth.pending_registration WHERE expires_at < NOW()) as expired_count,
          (SELECT COUNT(*) FROM auth.registration_verification_log) as verification_log_count,
          (SELECT COUNT(*) FROM auth.email_log) as email_log_count
      `);
      
      const metrics = metricsResult[0] as any;
      const result = {
        pendingRegistrationCount: Number(metrics.pending_count || 0),
        expiredRegistrationCount: Number(metrics.expired_count || 0),
        verificationLogCount: Number(metrics.verification_log_count || 0),
        emailLogCount: Number(metrics.email_log_count || 0),
      };
      
      // 检查是否有异常情况
      if (result.expiredRegistrationCount > 10000) {
        issues.push(`过期注册记录过多: ${result.expiredRegistrationCount}`);
      }
      
      if (result.verificationLogCount > 100000) {
        issues.push(`验证日志记录过多: ${result.verificationLogCount}`);
      }
      
      return {
        healthy: issues.length === 0,
        issues,
        metrics: result,
      };
      
    } catch (error) {
      return {
        healthy: false,
        issues: [`健康检查失败: ${error instanceof Error ? error.message : String(error)}`],
        metrics: {
          pendingRegistrationCount: 0,
          expiredRegistrationCount: 0,
          verificationLogCount: 0,
          emailLogCount: 0,
        },
      };
    }
  }
}

// 创建默认实例
export const dataCleanupService = new DataCleanupService();