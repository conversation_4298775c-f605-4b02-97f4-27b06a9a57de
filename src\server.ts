import "dotenv/config";
import app, { initializeApp } from "./app.js";

const start = async () => {
  try {
    console.log("🔧 Initializing application...");

    // 初始化应用
    await initializeApp();
    console.log("✅ Application initialized successfully");

    // 获取端口配置
    const port = parseInt(process.env.PORT || "3001", 10);
    const host = process.env.HOST || "0.0.0.0";

    console.log(`🚀 Starting server on ${host}:${port}...`);

    // 启动服务器
    await app.listen({ port, host });

    console.log(`🚀 Better Auth Service running on http://${host}:${port}`);
    console.log(`📚 API Documentation: http://${host}:${port}/docs`);
    console.log(`🏥 Health Check: http://${host}:${port}/api/health`);
    console.log(`🔐 Auth API: http://${host}:${port}/api/auth/*`);
  } catch (err) {
    console.error("❌ Error starting server:", err);
    if (err instanceof Error) {
      console.error("Error message:", err.message);
      console.error("Error stack:", err.stack);

      // 提供更详细的错误信息
      if (err.message.includes("Database connection failed")) {
        console.error("💡 Database connection tips:");
        console.error("   - Check if PostgreSQL is running");
        console.error("   - Verify DATABASE_URL in .env file");
        console.error('   - Ensure database "auth_db" exists');
        console.error("   - Check if user has proper permissions");
      }

      if (err.message.includes("EADDRINUSE")) {
        console.error(
          "💡 Port already in use. Try a different port or stop the conflicting service.",
        );
      }

      if (err.message.includes("BETTER_AUTH_SECRET")) {
        console.error("💡 Missing or invalid BETTER_AUTH_SECRET in .env file");
      }
    }
    process.exit(1);
  }
};

// 优雅关闭处理
const gracefulShutdown = async (signal: string) => {
  app.log.info(`Received ${signal}, shutting down gracefully...`);

  try {
    await app.close();
    app.log.info("Server closed successfully");
    process.exit(0);
  } catch (err) {
    app.log.error("Error during shutdown:", err);
    process.exit(1);
  }
};

// 监听关闭信号
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// 未捕获异常处理
process.on("uncaughtException", (err) => {
  app.log.error("Uncaught Exception:", err);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  app.log.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// 启动服务器
start();
