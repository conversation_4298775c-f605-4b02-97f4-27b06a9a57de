/**
 * 临时注册路由
 * 使用新的临时注册表架构处理用户注册流程
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";
import { pendingRegistrationService } from "../services/pending-registration.js";
import { 
  i18nMiddleware,
  createI18nErrorResponse,
  createI18nSuccessResponse,
} from "../middleware/i18n-middleware.js";
import { auth } from "../auth.js";
// import bcrypt from "bcryptjs"; // 改为动态导入

// 验证模式
const pendingRegistrationSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(8, "密码长度至少8位"),
  name: z.string().min(1, "用户名不能为空"),
  company_name: z.string().min(1, "公司名称不能为空"),
  invite_code: z.string().min(1, "邀请码不能为空"),
  language: z.string().optional(),
});

const resendVerificationSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
});

export async function registerPendingRegistrationRoutes(app: FastifyInstance) {
  // 注册国际化中间件
  app.addHook("preHandler", i18nMiddleware);

  /**
   * 新版邀请码注册端点（使用临时注册表）
   */
  app.post(
    "/api/auth/register-pending",
    {
      schema: {
        description: "使用邀请码注册新用户（临时注册表版本）",
        tags: ["Auth", "Registration"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
            name: { type: "string", minLength: 1 },
            company_name: { type: "string", minLength: 1 },
            invite_code: { type: "string", minLength: 1 },
            language: { type: "string" },
          },
          required: [
            "email",
            "password",
            "name", 
            "company_name",
            "invite_code",
          ],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  pendingId: { type: "string" },
                  email: { type: "string" },
                  verificationSent: { type: "boolean" },
                  nextSteps: {
                    type: "object",
                    properties: {
                      requireEmailVerification: { type: "boolean" },
                      verificationEmailSent: { type: "boolean" },
                      instructions: { type: "string" },
                      canResendEmail: { type: "boolean" },
                      troubleshooting: { type: "string" },
                    },
                  },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
          password: string;
          name: string;
          company_name: string;
          invite_code: string;
          language?: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      const startTime = Date.now();
      
      try {
        // 验证请求数据
        const validatedData = pendingRegistrationSchema.parse(request.body);
        
        app.log.info(`临时注册请求: ${validatedData.email}, 邀请码: ${validatedData.invite_code}`);
        
        // 加密密码
        const bcrypt = await import('bcryptjs');
        const hashedPassword = await bcrypt.default.hash(validatedData.password, 12);
        
        // 获取客户端信息
        const ipAddress = getClientIP(request);
        const userAgent = request.headers['user-agent'];
        
        // 创建临时注册记录
        const result = await pendingRegistrationService.createPendingRegistration({
          email: validatedData.email,
          password: hashedPassword,
          name: validatedData.name,
          companyName: validatedData.company_name,
          inviteCode: validatedData.invite_code,
          language: validatedData.language || "chinese",
          ipAddress,
          userAgent,
        });
        
        const processingTime = Date.now() - startTime;
        
        if (!result.success) {
          app.log.warn(`临时注册失败: ${validatedData.email}, 原因: ${result.error}`, {
            processingTime,
          });
          
          // 根据错误类型返回不同的状态码
          if (result.error?.includes("邀请码无效")) {
            return reply.status(400).send(createI18nErrorResponse(
              request,
              "INVALID_INVITE_CODE",
              result.error
            ));
          }
          
          if (result.error?.includes("已注册")) {
            return reply.status(409).send(createI18nErrorResponse(
              request,
              "USER_EXISTS", 
              result.error
            ));
          }
          
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "REGISTRATION_FAILED",
            result.error || "注册失败，请重试"
          ));
        }
        
        app.log.info(`临时注册成功: ${validatedData.email}, pendingId: ${result.pendingId}`, {
          processingTime,
          verificationSent: result.verificationSent,
        });
        
        // 返回成功响应
        return reply.status(200).send(createI18nSuccessResponse(
          request,
          "auth.register.pendingSuccess",
          {
            pendingId: result.pendingId,
            email: validatedData.email,
            verificationSent: result.verificationSent,
            nextSteps: result.nextSteps,
          }
        ));
        
      } catch (error) {
        const processingTime = Date.now() - startTime;
        app.log.error("临时注册异常:", {
          error: error instanceof Error ? error.message : String(error),
          email: request.body?.email,
          processingTime,
        });
        
        if (error instanceof z.ZodError) {
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "VALIDATION_ERROR",
            error.errors.map((e) => e.message).join(", ")
          ));
        }
        
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          error instanceof Error ? error.message : "注册失败，请重试"
        ));
      }
    }
  );

  /**
   * 邮箱验证完成端点
   */
  app.get(
    "/api/auth/verify-pending-registration",
    {
      schema: {
        description: "完成临时注册的邮箱验证",
        tags: ["Auth", "Registration"],
        querystring: {
          type: "object",
          properties: {
            token: { type: "string" },
            redirect: { type: "string" },
          },
          required: ["token"],
        },
      },
    },
    async (
      request: FastifyRequest<{
        Querystring: {
          token: string;
          redirect?: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { token, redirect } = request.query;
        
        app.log.info(`处理临时注册邮箱验证: token=${token.substring(0, 8)}...`);
        
        // 验证并完成注册
        const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration(token);
        
        if (!result.success) {
          app.log.warn(`临时注册验证失败: ${result.error}`);
          
          // 返回错误页面
          const errorHtml = renderErrorPage({
            title: "邮箱验证失败",
            message: result.error || "验证过程失败",
            canRetry: true,
          });
          
          return reply.type("text/html").send(errorHtml);
        }
        
        app.log.info(`临时注册验证成功: userId=${result.userId}, orgId=${result.organizationId}`);
        
        // 构建重定向URL - 应该重定向到前端URL
        const redirectUrl = redirect || 
          `${process.env.FRONTEND_URL || "http://localhost:3000"}/dashboard?welcome=true`;
        
        // 返回成功页面
        const successHtml = renderSuccessPage({
          title: "邮箱验证成功",
          message: "恭喜！您的账户已成功创建并验证。",
          redirectUrl,
          userId: result.userId,
          organizationId: result.organizationId,
        });
        
        return reply.type("text/html").send(successHtml);
        
      } catch (error) {
        app.log.error("处理临时注册验证异常:", error);
        
        const errorHtml = renderErrorPage({
          title: "验证处理失败",
          message: "服务器处理验证请求时发生错误，请稍后重试。",
          canRetry: false,
        });
        
        return reply.type("text/html").send(errorHtml);
      }
    }
  );

  /**
   * 重发验证邮件端点
   */
  app.post(
    "/api/auth/resend-pending-verification",
    {
      schema: {
        description: "重新发送临时注册的验证邮件",
        tags: ["Auth", "Registration"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
          },
          required: ["email"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  email: { type: "string" },
                  verificationSent: { type: "boolean" },
                  nextSteps: {
                    type: "object",
                    properties: {
                      instructions: { type: "string" },
                      canResendEmail: { type: "boolean" },
                    },
                  },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          429: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
              retryAfter: { type: "number" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { email } = resendVerificationSchema.parse(request.body);
        
        app.log.info(`重发临时注册验证邮件请求: ${email}`);
        
        // 重发验证邮件
        const result = await pendingRegistrationService.resendVerificationEmail(email);
        
        if (!result.success) {
          app.log.warn(`重发验证邮件失败: ${email}, 原因: ${result.error}`);
          
          if (result.error?.includes("稍后再重发")) {
            return reply.status(429).send(createI18nErrorResponse(
              request,
              "RATE_LIMIT_EXCEEDED",
              result.error,
              120
            ));
          }
          
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "RESEND_FAILED",
            result.error || "重发失败"
          ));
        }
        
        app.log.info(`重发临时注册验证邮件成功: ${email}`);
        
        return reply.status(200).send(createI18nSuccessResponse(
          request,
          "auth.register.resendSuccess",
          {
            email,
            verificationSent: result.verificationSent,
            nextSteps: result.nextSteps,
          }
        ));
        
      } catch (error) {
        app.log.error("重发验证邮件异常:", error);
        
        if (error instanceof z.ZodError) {
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "VALIDATION_ERROR",
            error.errors.map((e) => e.message).join(", ")
          ));
        }
        
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          error instanceof Error ? error.message : "重发失败，请重试"
        ));
      }
    }
  );

  /**
   * 查询临时注册状态端点
   */
  app.post(
    "/api/auth/check-pending-registration",
    {
      schema: {
        description: "查询临时注册状态",
        tags: ["Auth", "Registration"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
          },
          required: ["email"],
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { email } = request.body;
        
        // 这里可以添加查询临时注册状态的逻辑
        // 暂时返回基本响应
        return reply.status(200).send(createI18nSuccessResponse(
          request,
          "common.success",
          {
            email,
            status: "pending", // 这里应该从数据库查询实际状态
            message: "状态查询功能开发中",
          }
        ));
        
      } catch (error) {
        app.log.error("查询临时注册状态异常:", error);
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          "查询失败，请重试"
        ));
      }
    }
  );

  /**
   * 管理员查看临时注册统计端点
   */
  app.get(
    "/api/auth/admin/pending-registration-stats",
    {
      schema: {
        description: "获取临时注册统计信息（管理员专用）",
        tags: ["Auth", "Admin"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 验证管理员权限
        const session = await auth.api.getSession({ 
          headers: convertRequestHeaders(request.headers) 
        });
        
        if (!session?.user) {
          return reply.status(401).send(createI18nErrorResponse(
            request,
            "UNAUTHORIZED",
            "请先登录"
          ));
        }
        
        // 这里应该检查用户是否为管理员
        // 暂时跳过权限检查
        
        // 获取统计信息
        const stats = await pendingRegistrationService.getRegistrationStats();
        
        return reply.status(200).send(createI18nSuccessResponse(
          request,
          "common.success",
          { stats }
        ));
        
      } catch (error) {
        app.log.error("获取临时注册统计异常:", error);
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          "获取统计信息失败"
        ));
      }
    }
  );

  // 辅助方法

  /**
   * 渲染错误页面
   */
  function renderErrorPage(options: {
    title: string;
    message: string;
    canRetry: boolean;
  }): string {
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${options.title}</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                 background: #f5f5f5; margin: 0; padding: 20px; }
          .container { max-width: 500px; margin: 50px auto; background: white; padding: 40px; 
                       border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
          .error-icon { font-size: 48px; color: #ff6b6b; margin-bottom: 20px; }
          .title { font-size: 24px; color: #333; margin-bottom: 16px; }
          .message { font-size: 16px; color: #666; margin-bottom: 30px; line-height: 1.5; }
          .button { background: #007bff; color: white; padding: 12px 24px; border: none; 
                    border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
          .button:hover { background: #0056b3; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="error-icon">⚠️</div>
          <h1 class="title">${options.title}</h1>
          <p class="message">${options.message}</p>
          ${options.canRetry ? '<a href="javascript:history.back()" class="button">返回重试</a>' : ''}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 渲染成功页面
   */
  function renderSuccessPage(options: {
    title: string;
    message: string;
    redirectUrl: string;
    userId?: string;
    organizationId?: string;
  }): string {
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${options.title}</title>
        <meta http-equiv="refresh" content="3;url=${options.redirectUrl}">
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                 background: #f5f5f5; margin: 0; padding: 20px; }
          .container { max-width: 500px; margin: 50px auto; background: white; padding: 40px; 
                       border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
          .success-icon { font-size: 48px; color: #28a745; margin-bottom: 20px; }
          .title { font-size: 24px; color: #333; margin-bottom: 16px; }
          .message { font-size: 16px; color: #666; margin-bottom: 30px; line-height: 1.5; }
          .button { background: #28a745; color: white; padding: 12px 24px; border: none; 
                    border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
          .button:hover { background: #218838; }
          .redirect-info { font-size: 14px; color: #999; margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success-icon">✅</div>
          <h1 class="title">${options.title}</h1>
          <p class="message">${options.message}</p>
          <a href="${options.redirectUrl}" class="button">立即前往</a>
          <p class="redirect-info">页面将在 3 秒后自动跳转...</p>
        </div>
      </body>
      </html>
    `;
  }
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: FastifyRequest): string | undefined {
  return (
    (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (request.headers['x-real-ip'] as string) ||
    request.ip ||
    request.socket.remoteAddress
  );
}

/**
 * 转换请求头格式
 */
function convertRequestHeaders(headers: any): Headers {
  const convertedHeaders = new Headers();
  Object.entries(headers).forEach(([key, value]) => {
    if (typeof value === "string") {
      convertedHeaders.set(key, value);
    } else if (Array.isArray(value)) {
      convertedHeaders.set(key, value.join(", "));
    }
  });
  return convertedHeaders;
}