/**
 * 邀请码外部API服务
 * 与业务数据库的邀请码系统集成
 */

export interface InviteCodeVerifyResponse {
  code: number;
  success: boolean;
  message: string;
  data: boolean;
}

export interface InviteCodeUpdateResponse {
  code: number;
  success: boolean;
  message: string;
  data: {
    message: string;
    code: string;
  };
}

export class InviteCodeService {
  private readonly baseUrl: string;
  private readonly timeout: number = 30000; // 增加超时时间到30秒

  constructor() {
    this.baseUrl = process.env.BUSINESS_BASE_API!;

    if (!this.baseUrl) {
      throw new Error("BUSINESS_BASE_API environment variable is required");
    }

    // 验证URL格式和可达性
    console.log("🔧 InviteCodeService initialized with baseUrl:", this.baseUrl);
  }

  /**
   * 验证邀请码是否有效
   * @param code 邀请码
   * @returns 验证结果
   */
  async verifyCode(code: string): Promise<{ valid: boolean; error?: string }> {
    try {
      console.log("🔍 Starting invite code verification for:", code);

      // 临时测试邀请码
      if (code === "TEST2025") {
        console.log("✅ 测试邀请码验证通过");
        return { valid: true };
      }

      const response = await this.makeRequest(
        "/invitations/verify_code",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            "User-Agent": "Better-Auth-Service/1.0",
          },
          body: JSON.stringify({ code }),
        },
      );

      const data: InviteCodeVerifyResponse = await response.json();

      console.log("📥 Verification response:", {
        status: response.status,
        data: data,
      });

      if (!response.ok) {
        return {
          valid: false,
          error: `API请求失败: ${response.status} ${response.statusText}`,
        };
      }

      if (data.code !== 200 || !data.success) {
        return {
          valid: false,
          error: data.message || "邀请码验证失败",
        };
      }

      return {
        valid: data.data === true,
        error: data.data === true ? undefined : "邀请码无效或已使用",
      };
    } catch (error) {
      console.error("❌ 验证邀请码失败:", error);
      return {
        valid: false,
        error: this.getErrorMessage(error),
      };
    }
  }

  /**
   * 更新邀请码状态（标记为已使用）
   * @param code 邀请码
   * @returns 更新结果
   */
  async updateStatus(
    code: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log("🔄 Starting invite code status update for:", code);

      // 使用query参数方式
      const response = await this.makeRequest(
        `/invitations/update_status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            "User-Agent": "Better-Auth-Service/1.0",
          },
          body: JSON.stringify({ code }),
        },
      );

      const data: InviteCodeUpdateResponse = await response.json();

      console.log("📥 Update response:", {
        status: response.status,
        data: data,
      });

      if (!response.ok) {
        return {
          success: false,
          error: `API请求失败: ${response.status} ${response.statusText}`,
        };
      }

      if (data.code !== 200 || !data.success) {
        return {
          success: false,
          error: data.message || "更新邀请码状态失败",
        };
      }

      console.log("✅ updateStatus succeeded");
      return { success: true };
    } catch (error) {
      console.error("❌ 更新邀请码状态失败:", error);
      return {
        success: false,
        error: this.getErrorMessage(error),
      };
    }
  }

  /**
   * 发起HTTP请求（带重试机制）
   * @param endpoint API端点
   * @param options 请求选项
   * @param retries 重试次数
   * @returns Response对象
   */
  private async makeRequest(
    endpoint: string,
    options: RequestInit,
    retries: number = 1, // 减少重试次数，避免过度重试
  ): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;

    console.log("🔄 Making API request:", {
      url,
      method: options.method,
      hasBody: !!options.body,
      timeout: this.timeout,
    });

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.warn(`⏰ Request timeout after ${this.timeout}ms: ${url}`);
      controller.abort();
    }, this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        // 添加额外的fetch选项来提高兼容性
        keepalive: false,
      });

      clearTimeout(timeoutId);

      console.log("📥 API response received:", {
        url,
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        contentType: response.headers.get("content-type"),
      });

      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      console.error("❌ API request failed:", {
        url,
        method: options.method,
        error: error instanceof Error ? error.message : String(error),
        errorName: error instanceof Error ? error.name : "Unknown",
        retriesLeft: retries,
      });

      // 如果是网络错误且还有重试次数，则重试
      if (retries > 0 && this.isRetryableError(error)) {
        const retryDelay = 2000; // 增加重试延迟到2秒
        console.warn(
          `🔄 Retrying request in ${retryDelay / 1000}s (${retries} retries left): ${endpoint}`,
        );
        await this.delay(retryDelay);
        return this.makeRequest(endpoint, options, retries - 1);
      }

      throw error;
    }
  }

  /**
   * 判断是否为可重试的错误
   * @param error 错误对象
   * @returns 是否可重试
   */
  private isRetryableError(error: any): boolean {
    // 只对特定的网络错误进行重试，避免无意义的重试
    const retryableErrors = [
      "AbortError",
      "TimeoutError",
      "NetworkError",
      "TypeError",
    ];

    const isRetryable =
      retryableErrors.includes(error.name) ||
      error.message?.includes("fetch") ||
      error.message?.includes("network") ||
      error.message?.includes("timeout");

    console.log("🤔 Checking if error is retryable:", {
      errorName: error.name,
      errorMessage: error.message,
      isRetryable,
    });

    return isRetryable;
  }

  /**
   * 延迟函数
   * @param ms 延迟毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 获取错误消息
   * @param error 错误对象
   * @returns 错误消息
   */
  private getErrorMessage(error: any): string {
    if (error.name === "AbortError") {
      return "请求超时，请检查网络连接和服务器状态";
    }
    if (
      error.message?.includes("fetch") ||
      error.message?.includes("network")
    ) {
      return "网络连接失败，请检查网络后重试";
    }
    if (error.message?.includes("timeout")) {
      return "请求超时，服务器响应时间过长";
    }
    return error.message || "未知网络错误";
  }

  /**
   * 健康检查方法 - 测试API连接
   */
  async healthCheck(): Promise<{ healthy: boolean; error?: string }> {
    try {
      const testUrl = `${this.baseUrl}/health`; // 假设有健康检查端点
      console.log("🏥 Performing health check:", testUrl);

      const response = await fetch(testUrl, {
        method: "GET",
        signal: AbortSignal.timeout(5000), // 5秒快速健康检查
      });

      return {
        healthy: response.ok,
        error: response.ok
          ? undefined
          : `Health check failed: ${response.status}`,
      };
    } catch (error) {
      console.warn("⚠️ Health check failed:", error);
      return {
        healthy: false,
        error: `Health check error: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}

// 创建单例实例
export const inviteCodeService = new InviteCodeService();
