#!/bin/bash

# ===================================
# Drizzle ORM 自动化数据库迁移脚本
# 基于 auth-schema.ts 自动生成和执行迁移
# ===================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 获取当前脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

log_info "🚀 Drizzle ORM 自动化数据库迁移"
log_info "项目目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查环境变量
log_step "1. 检查环境配置"
if [ -f ".env" ]; then
    log_info "加载环境变量..."
    set -a
    source .env
    set +a
else
    log_error ".env 文件不存在"
    exit 1
fi

if [ -z "$DATABASE_URL" ]; then
    log_error "DATABASE_URL 环境变量未设置"
    exit 1
fi

log_success "环境配置检查完成"
log_info "数据库连接: ${DATABASE_URL%%@*}@[hidden]"

# 检查Drizzle配置
log_step "2. 检查Drizzle配置"
if [ ! -f "drizzle.config.ts" ]; then
    log_error "drizzle.config.ts 文件不存在"
    exit 1
fi

if [ ! -f "src/lib/auth-schema.ts" ]; then
    log_error "src/lib/auth-schema.ts 文件不存在"
    exit 1
fi

log_success "Drizzle配置文件检查完成"

# 生成迁移文件
log_step "3. 生成数据库迁移文件"
log_info "基于 auth-schema.ts 生成迁移..."

if pnpm drizzle-kit generate; then
    log_success "迁移文件生成成功"
else
    log_error "迁移文件生成失败"
    exit 1
fi

# 检查生成的迁移文件
MIGRATION_DIR="$PROJECT_ROOT/drizzle"
if [ -d "$MIGRATION_DIR" ]; then
    MIGRATION_COUNT=$(find "$MIGRATION_DIR" -name "*.sql" | wc -l)
    log_info "发现 $MIGRATION_COUNT 个迁移文件"
    
    if [ $MIGRATION_COUNT -gt 0 ]; then
        log_info "最新的迁移文件:"
        find "$MIGRATION_DIR" -name "*.sql" -exec basename {} \; | sort | tail -3
    fi
fi

# 执行数据库迁移
log_step "4. 执行数据库迁移"
log_info "将迁移应用到数据库..."

if pnpm drizzle-kit migrate; then
    log_success "数据库迁移执行成功"
else
    log_error "数据库迁移执行失败"
    exit 1
fi

# 验证表结构
log_step "5. 验证表结构"
log_info "检查数据库表是否正确创建..."

# 检查auth schema是否存在
SCHEMA_EXISTS=$(psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'auth');" | xargs)

if [ "$SCHEMA_EXISTS" = "t" ]; then
    log_success "auth schema 存在"
else
    log_error "auth schema 不存在"
    exit 1
fi

# 列出所有表
log_info "当前数据库表:"
psql "$DATABASE_URL" -c "
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'auth'
ORDER BY tablename;
"

# 检查JWT插件相关表
JWKS_EXISTS=$(psql "$DATABASE_URL" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'jwks');" | xargs)

if [ "$JWKS_EXISTS" = "t" ]; then
    log_success "JWKS表存在 - JWT插件支持已启用"
else
    log_warning "JWKS表不存在 - JWT插件可能无法正常工作"
fi

# 显示表统计
TABLE_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'auth';" | xargs)
log_info "auth schema 中共有 $TABLE_COUNT 个表"

# 生成Drizzle内省文件（可选）
log_step "6. 生成数据库内省文件"
log_info "生成当前数据库结构的内省文件..."

if pnpm drizzle-kit introspect; then
    log_success "数据库内省完成"
else
    log_warning "数据库内省失败（非关键错误）"
fi

# 完成
log_success "🎉 Drizzle ORM 自动化迁移完成！"

echo ""
log_info "📋 迁移摘要:"
echo "  ✅ Schema文件: src/lib/auth-schema.ts"
echo "  ✅ 迁移文件: drizzle/ 目录"
echo "  ✅ 数据库表: $TABLE_COUNT 个表"
echo "  ✅ JWT支持: $([ "$JWKS_EXISTS" = "t" ] && echo "已启用" || echo "未启用")"

echo ""
log_info "🚀 下一步操作:"
echo "1. 启动服务: pnpm dev"
echo "2. 测试JWT: pnpm jwt:demo"
echo "3. 运行测试: pnpm test"

echo ""
log_info "📚 Drizzle命令参考:"
echo "  • 生成迁移: pnpm drizzle-kit generate"
echo "  • 执行迁移: pnpm drizzle-kit migrate"  
echo "  • 数据库内省: pnpm drizzle-kit introspect"
echo "  • 启动Studio: pnpm drizzle-kit studio"