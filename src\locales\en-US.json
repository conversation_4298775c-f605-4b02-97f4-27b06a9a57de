{"common": {"success": "Success", "error": "Error", "loading": "Loading...", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "yes": "Yes", "no": "No", "user": "User", "team": "Team"}, "auth": {"register": {"success": "Registration successful! Verification email has been sent to your inbox", "emailSent": "Verification email sent", "emailSendFailed": "Registration successful, but verification email failed to send", "userExists": "This email is already registered", "invalidInviteCode": "Invalid or non-existent invite code", "validationError": "Input information is incorrect, please check and try again", "createFailed": "Registration failed, please try again", "instructions": {"checkEmail": "Please check your email and click the verification link to complete registration. If you don't receive the email, please check your spam folder.", "emailSendFailed": "Verification email failed to send, please resend the verification email later.", "troubleshooting": "If you continue to not receive verification emails, please contact customer service or try using a different email address.", "alreadyVerified": "Email has been verified, you can log in and use normally.", "canLogin": "You can log in and use normally."}}, "login": {"success": "Login successful", "failed": "<PERSON><PERSON> failed", "unauthorized": "Please log in first", "emailNotVerified": "Please verify your email first", "accountLocked": "Account has been locked", "invalidCredentials": "Incorrect email or password"}, "emailVerification": {"sent": "Verification email has been sent, please check your inbox", "resent": "Verification email has been resent, please check your inbox", "success": "Email verification successful", "failed": "Email verification failed, please resend verification email", "expired": "Verification link has expired, please resend", "invalid": "Invalid verification link", "alreadyVerified": "Email has already been verified", "required": "Email verification required", "tokenMissing": "Missing verification token", "tokenInvalid": "Verification token is invalid or expired", "sendFailed": "Failed to send verification email, please try again later"}, "inviteCode": {"valid": "Invite code is valid", "invalid": "Invalid invite code", "expired": "Invite code has expired", "used": "Invite code has been used", "notFound": "Invite code does not exist", "verificationFailed": "Verification failed, please try again"}, "user": {"created": "User created successfully", "updated": "User information updated successfully", "deleted": "User deleted successfully", "notFound": "User does not exist", "exists": "User already exists", "createFailed": "User creation failed", "updateFailed": "User information update failed", "deleteFailed": "User deletion failed", "languageUpdated": "Language settings updated successfully", "languageUpdateFailed": "Language settings update failed", "insufficientPermissions": "Insufficient permissions"}, "admin": {"superAdminCreated": "Super administrator created successfully", "adminCreated": "Administrator created successfully", "createFailed": "Creation failed, please try again", "invalidSecretKey": "Secret key verification failed", "onlySuperAdminCanCreate": "Only super administrators can create administrator accounts", "organizationNotFound": "SpecificAI organization does not exist, please create super administrator first"}, "session": {"expired": "Session has expired, please log in again", "invalid": "Invalid session", "required": "Login required", "extended": "Extended session information retrieved successfully", "getFailed": "Failed to get session information"}}, "organization": {"created": "Organization created successfully", "updated": "Organization information updated successfully", "deleted": "Organization deleted successfully", "notFound": "Organization does not exist", "getFailed": "Failed to get organization information", "memberLimitReached": "Member limit reached", "subscriptionRequired": "Subscription plan upgrade required"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "password": "Password must be at least 8 characters long", "confirmPassword": "Passwords do not match", "invalidFormat": "Invalid format"}, "errors": {"internal": "Internal server error, please try again later", "network": "Network connection failed, please check your network and try again", "timeout": "Request timeout, please try again", "notFound": "Requested resource not found", "unauthorized": "Unauthorized access", "forbidden": "Access denied", "badRequest": "Invalid request parameters", "conflict": "Resource conflict", "tooManyRequests": "Too many requests, please try again later", "serviceUnavailable": "Service temporarily unavailable"}, "email": {"verification": {"welcome": "Welcome to SpecificAI", "welcomeV2": "🎉 Welcome to SpecificAI", "welcomeV3": "🚀 Start Your Intelligent Work Experience", "subtitle": "Your Intelligent Work Partner", "subtitleV3": "AI Solutions Tailored for You", "greeting": "Hello {{name}},", "message": "Thank you for registering your SpecificAI account. Please click the link below to verify your email address:", "securityMessage": "You have successfully created a SpecificAI account. To ensure account security, please verify your email address:", "mainMessageV3": "We're excited that you've chosen SpecificA<PERSON> as your intelligent work partner. To ensure account security and unlock all features, please verify your email address.", "personalGreeting": "{{name}}, welcome{{company}} to SpecificAI!", "welcomeMessage": "Welcome {{name}} to join us!", "button": "<PERSON><PERSON><PERSON>", "buttonV2": "🔗 Verify Email Address", "buttonV3": "🔐 Verify Now & Start Using", "alternative": "If you cannot click the button above, please copy and paste the following link into your browser:", "expiry": "This link will expire in 1 hour.", "expiryV3": "Secure link valid for 24 hours", "inviteCode": "Your Invite Code", "afterVerification": "After verification, you can:", "quickStartTitle": "🎯 After verification, you can immediately experience", "troubleTitle": "🔧 Having Issues?", "troubleText": "If the button doesn't work, please copy the following link to your browser address bar:", "linkLabel": "📋 Can't click the button? Expand to view verification link", "link": "Verification Link", "subject": "Verify Your Email Address - SpecificAI", "subjectV2": "🚀 Verify Email & Start Your SpecificAI Journey", "subjectV3": "🚀 Welcome to SpecificAI - Please Verify Your Email", "preheader": "Click to verify your email address and activate your account", "preheaderV2": "Complete verification and start your intelligent work journey", "preheaderV3": "Verify now and unlock your dedicated AI workspace", "features": {"access": "Access complete product features", "notifications": "Receive important account notifications", "security": "Enjoy enhanced account security"}, "feature1": "💡 Intelligent Workflow Automation", "feature2": "📊 Data Insights and Analytics", "feature3": "🤖 Personalized AI Assistant", "feature4": "🔔 Real-time Collaboration and Notifications"}, "invite": {"subject": "Invitation to join {{company}} - SpecificAI", "subjectV2": "🎉 Welcome to {{company}} - SpecificAI", "subjectV3": "🌟 {{inviter}}'s Exclusive Invitation - Join {{company}}", "welcome": "Welcome to SpecificAI", "welcomeV2": "Welcome to SpecificAI Family", "welcomeV3": "You're Invited to <PERSON>in <PERSON>", "greeting": "Hello {{name}}, {{inviter}} has invited you to join {{company}}!", "personalGreeting": "Hello {{name}}!", "personalGreetingV3": "Dear {{name}}, your exclusive invitation is ready!", "description": "We invite you to join our platform and experience the advanced AI-powered working methods.", "inviterMessage": "{{inviter}} from {{company}} invites you to join SpecificAI and start a new era of intelligent work together.", "inviterMessageV3": "{{inviter}} from {{company}} has carefully prepared this exclusive invitation for you, bringing you into the AI-driven intelligent work world.", "codeLabel": "Your Invitation Code", "exclusiveCode": "Exclusive Invitation Code", "exclusive": "Exclusive Invitation", "joinNow": "Join Now", "getStarted": "Get Started", "joinExclusive": "Join Exclusive Space", "joinLink": "Join <PERSON>", "featuresTitle": "Amazing Features Preview", "premiumFeatures": "Exclusive Premium Features", "feature1": "AI Intelligent Assistant", "feature1Title": "AI Intelligent Assistant", "feature1Desc": "Personalized AI assistant to improve work efficiency", "feature2": "Data Analysis & Insights", "feature2Title": "Intelligent Data Analysis", "feature2Desc": "Deep data analysis to discover business opportunities", "feature3": "Automated Workflow Optimization", "feature3Title": "Intelligent Process Optimization", "feature3Desc": "Automate complex tasks and unleash creativity", "quickStart": "Quick Start Guide", "step1": "Click the link above to enter the registration page", "step2": "Enter your invitation code to complete registration", "step3": "Start experiencing advanced AI features immediately", "codeExpiry": "Invitation code valid for 24 hours", "footer": "If you have any questions, please contact our support team.", "footerV2": "Thank you for choosing SpecificAI, let's start a new chapter of intelligent work together!", "footerV3": "This is an exclusive invitation for your use only. Join SpecificAI and start your intelligent work journey.", "rights": "All rights reserved"}, "footer": {"autoSent": "This email was sent automatically by the SpecificAI system, please do not reply", "company": "SpecificAI - Making AI Smarter, Making Work More Efficient", "support": "Contact Support", "privacy": "Privacy Policy"}}}