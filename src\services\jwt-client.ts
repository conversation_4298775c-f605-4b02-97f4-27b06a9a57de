import type { JWTTokenResponse, JWTVerifyResponse } from "./jwt-service.js";

export interface JWTClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface JWTCreateTokenRequest {
  userId: string;
  email: string;
  role?: string;
  organizationId?: string;
  customClaims?: Record<string, any>;
}

/**
 * JWT客户端工具类
 * 用于服务端到服务端的JWT Token管理
 */
export class JWTClient {
  private config: JWTClientConfig;

  constructor(config: JWTClientConfig) {
    this.config = {
      timeout: 5000, // 默认5秒超时
      ...config,
    };
  }

  /**
   * 创建JWT Token
   * @param request Token创建请求
   * @returns JWT Token响应
   */
  async createToken(request: JWTCreateTokenRequest): Promise<JWTTokenResponse> {
    try {
      const response = await this.makeRequest("/api/auth/jwt/create", "POST", request);
      return response as JWTTokenResponse;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "创建Token失败",
      };
    }
  }

  /**
   * 验证JWT Token
   * @param token JWT Token字符串
   * @returns 验证结果
   */
  async verifyToken(token: string): Promise<JWTVerifyResponse> {
    try {
      const response = await this.makeRequest("/api/auth/jwt/verify", "POST", { token });
      return response as JWTVerifyResponse;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证Token失败",
      };
    }
  }

  /**
   * 刷新JWT Token
   * @param refreshToken 刷新Token
   * @returns 新的Token响应
   */
  async refreshToken(refreshToken: string): Promise<JWTTokenResponse> {
    try {
      const response = await this.makeRequest("/api/auth/jwt/refresh", "POST", {
        refresh_token: refreshToken,
      });
      return response as JWTTokenResponse;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "刷新Token失败",
      };
    }
  }

  /**
   * 撤销JWT Token
   * @param token JWT Token字符串
   * @returns 撤销结果
   */
  async revokeToken(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeRequest("/api/auth/jwt/revoke", "POST", { token });
      return response as { success: boolean; error?: string };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "撤销Token失败",
      };
    }
  }

  /**
   * 发起HTTP请求
   * @param endpoint API端点
   * @param method HTTP方法
   * @param data 请求数据
   * @returns 响应数据
   */
  private async makeRequest(
    endpoint: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any
  ): Promise<any> {
    const url = `${this.config.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...this.config.headers,
    };

    const requestInit: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(this.config.timeout!),
    };

    if (data && (method === "POST" || method === "PUT")) {
      requestInit.body = JSON.stringify(data);
    }

    const response = await fetch(url, requestInit);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const responseData = await response.json();
    return responseData;
  }
}

/**
 * 创建JWT客户端实例的工厂函数
 * @param config 客户端配置
 * @returns JWT客户端实例
 */
export function createJWTClient(config: JWTClientConfig): JWTClient {
  return new JWTClient(config);
}

/**
 * 默认JWT客户端实例
 * 使用环境变量中的配置
 */
export const defaultJWTClient = createJWTClient({
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:10086",
  timeout: 5000,
  headers: {
    "User-Agent": "SpecificAI-JWT-Client/1.0",
  },
});