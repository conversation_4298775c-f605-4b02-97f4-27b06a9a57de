# 邀请码注册接口测试用例

## 测试目标
验证修复后的 `/api/auth/sign-up-invite` 接口是否正确处理邮箱验证逻辑，并提供完整的用户体验流程。

## 测试用例

### 1. 正常注册流程（邮件发送成功）

**请求:**
```bash
curl -X POST http://localhost:10086/api/auth/sign-up-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123",
    "name": "测试用户",
    "company_name": "测试公司",
    "invite_code": "TEST2025",
    "language": "chinese"
  }'
```

**期望响应:**
```json
{
  "success": true,
  "message": "注册成功！验证邮件已发送到您的邮箱",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "测试用户",
    "company_name": "测试公司",
    "emailVerified": false
  },
  "nextSteps": {
    "requireEmailVerification": true,
    "verificationEmailSent": true,
    "instructions": "请查收邮件并点击验证链接完成注册。如果没有收到邮件，请检查垃圾邮件文件夹。",
    "canResendEmail": true,
    "resendEndpoint": "/api/auth/resend-verification"
  }
}
```

### 2. 注册成功但邮件发送失败

**模拟场景:** 邮件服务暂时不可用

**期望响应:**
```json
{
  "success": true,
  "message": "注册成功，但验证邮件发送失败",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "测试用户",
    "company_name": "测试公司",
    "emailVerified": false
  },
  "nextSteps": {
    "requireEmailVerification": true,
    "verificationEmailSent": false,
    "instructions": "验证邮件发送失败，请稍后重新发送验证邮件。",
    "canResendEmail": true,
    "resendEndpoint": "/api/auth/resend-verification",
    "emailError": "邮件发送失败的具体错误信息",
    "troubleshooting": "如果持续收不到验证邮件，请联系客服或尝试使用其他邮箱地址。"
  }
}
```

### 3. 邮箱验证状态查询

**请求:**
```bash
curl -X POST http://localhost:10086/api/auth/check-email-verification \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

**期望响应（未验证）:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "emailVerified": false,
    "email": "<EMAIL>",
    "name": "测试用户",
    "verificationRequired": true,
    "canResendEmail": true,
    "isRecentlyRegistered": true,
    "nextSteps": {
      "instructions": "请查收邮件并点击验证链接完成注册。如果没有收到邮件，请检查垃圾邮件文件夹。",
      "resendEndpoint": "/api/auth/resend-verification",
      "troubleshooting": "如果持续收不到验证邮件，请联系客服或尝试重新注册。"
    }
  }
}
```

### 4. 错误处理测试

#### 4.1 邀请码无效
**请求:** 使用无效邀请码
**期望响应:** 400 状态码，错误类型 `INVALID_INVITE_CODE`

#### 4.2 邮箱已注册
**请求:** 使用已存在的邮箱
**期望响应:** 400 状态码，错误类型 `USER_EXISTS`

#### 4.3 表单验证失败
**请求:** 缺少必填字段或格式错误
**期望响应:** 400 状态码，错误类型 `VALIDATION_ERROR`

## 测试验证点

### 1. 响应格式验证
- [x] 响应包含 `success` 字段
- [x] 响应包含 `message` 字段，提供用户友好的提示
- [x] 响应包含 `user` 对象，包含用户基本信息
- [x] 响应包含 `nextSteps` 对象，提供下一步操作指引
- [x] `user.emailVerified` 字段正确设置为 `false`

### 2. 邮箱验证状态
- [x] `nextSteps.requireEmailVerification` 正确设置为 `true`
- [x] `nextSteps.verificationEmailSent` 反映实际邮件发送状态
- [x] `nextSteps.canResendEmail` 设置为 `true`
- [x] `nextSteps.resendEndpoint` 提供重发邮件的接口地址

### 3. 错误处理
- [x] 邮件发送失败时，响应包含错误信息
- [x] 提供故障排除指引
- [x] 不同错误类型有明确的错误代码和消息

### 4. 用户体验
- [x] 消息文本清晰易懂
- [x] 提供明确的下一步操作指引
- [x] 支持用户自助重发验证邮件
- [x] 提供故障排除建议

## 集成测试流程

### 完整用户注册流程测试
1. **注册** → 调用 `/api/auth/sign-up-invite`
2. **查询状态** → 调用 `/api/auth/check-email-verification`
3. **重发邮件**（如需要） → 调用 `/api/auth/resend-verification`
4. **邮箱验证** → 用户点击邮件中的验证链接
5. **验证成功** → 用户可以正常登录

### 异常情况测试
1. **邮件服务故障** → 验证错误处理和用户指引
2. **网络中断** → 验证重试机制
3. **并发注册** → 验证数据一致性

## 性能测试

### 响应时间要求
- 正常注册流程：< 3秒
- 状态查询：< 1秒
- 错误响应：< 1秒

### 并发测试
- 支持 50 个并发注册请求
- 数据库事务正确处理并发情况
- 邀请码状态更新不冲突

## 安全测试

### 输入验证
- SQL 注入防护
- XSS 攻击防护
- 邮箱格式验证
- 密码强度验证

### 频率限制
- 同一邮箱注册频率限制
- 同一IP注册频率限制
- 验证邮件发送频率限制

---

**测试完成标准:**
- 所有测试用例通过
- 响应格式符合预期
- 错误处理完善
- 用户体验流程完整
- 性能指标达标
- 安全防护有效
