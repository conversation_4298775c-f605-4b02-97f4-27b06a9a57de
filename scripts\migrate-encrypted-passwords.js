#!/usr/bin/env node

import postgres from 'postgres';

/**
 * 数据迁移脚本：处理已加密的密码记录
 * 
 * 选项1：标记为需要密码重置
 * 选项2：删除并要求重新注册
 */
async function migrateEncryptedPasswords() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }

  console.log('🔧 开始迁移已加密的密码记录...');
  const sql = postgres(databaseUrl);

  try {
    // 1. 查找所有包含bcrypt格式密码的记录
    console.log('🔍 查找已加密的密码记录...');
    const encryptedRecords = await sql`
      SELECT id, email, password, status, created_at
      FROM auth.pending_registration 
      WHERE password ~ '^\\$2[abxy]\\$\\d{2}\\$'
      AND status = 'pending'
      ORDER BY created_at DESC
    `;
    
    console.log(`📊 找到 ${encryptedRecords.length} 条已加密的密码记录`);
    
    if (encryptedRecords.length === 0) {
      console.log('✅ 没有需要迁移的记录');
      return;
    }

    // 2. 显示记录详情
    console.log('\n📋 需要处理的记录:');
    encryptedRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. ${record.email} (${record.id}) - ${record.created_at}`);
    });

    // 3. 选择处理策略
    const strategy = process.argv[2] || 'reset';
    
    if (strategy === 'reset') {
      console.log('\n🔄 执行策略1: 标记为需要密码重置');
      
      // 批量更新记录状态
      const result = await sql`
        UPDATE auth.pending_registration 
        SET 
          status = 'password_reset_required',
          registration_step = 'password_migration',
          updated_at = CURRENT_TIMESTAMP,
          metadata = jsonb_build_object(
            'needsPasswordReset', true,
            'reason', 'bcrypt_migration',
            'migrationDate', CURRENT_TIMESTAMP,
            'originalError', 'encrypted_password_detected'
          )
        WHERE password ~ '^\\$2[abxy]\\$\\d{2}\\$'
        AND status = 'pending'
      `;
      
      console.log(`✅ 已更新 ${result.count} 条记录状态为 'password_reset_required'`);
      
    } else if (strategy === 'delete') {
      console.log('\n🗑️ 执行策略2: 删除记录（要求重新注册）');
      
      // 先备份到临时表
      await sql`
        CREATE TABLE IF NOT EXISTS auth.pending_registration_backup_encrypted AS
        SELECT *, CURRENT_TIMESTAMP as backup_date
        FROM auth.pending_registration 
        WHERE password ~ '^\\$2[abxy]\\$\\d{2}\\$'
        AND status = 'pending'
      `;
      
      // 删除记录
      const result = await sql`
        DELETE FROM auth.pending_registration 
        WHERE password ~ '^\\$2[abxy]\\$\\d{2}\\$'
        AND status = 'pending'
      `;
      
      console.log(`✅ 已删除 ${result.count} 条记录（已备份到 pending_registration_backup_encrypted 表）`);
      
    } else {
      console.error('❌ 无效的策略。使用 "reset" 或 "delete"');
      process.exit(1);
    }

    // 4. 验证迁移结果
    console.log('\n🔍 验证迁移结果...');
    const remainingEncrypted = await sql`
      SELECT COUNT(*) as count
      FROM auth.pending_registration 
      WHERE password ~ '^\\$2[abxy]\\$\\d{2}\\$'
      AND status = 'pending'
    `;
    
    if (remainingEncrypted[0].count === 0) {
      console.log('✅ 迁移成功！不再有pending状态的加密密码记录');
    } else {
      console.warn(`⚠️ 仍有 ${remainingEncrypted[0].count} 条pending状态的加密密码记录`);
    }

    // 5. 显示当前状态统计
    console.log('\n📊 当前数据状态:');
    const stats = await sql`
      SELECT 
        status,
        COUNT(*) as count,
        MAX(created_at) as latest_record
      FROM auth.pending_registration 
      GROUP BY status
      ORDER BY count DESC
    `;
    
    stats.forEach(stat => {
      console.log(`  ${stat.status}: ${stat.count} 条记录 (最新: ${stat.latest_record})`);
    });

  } catch (error) {
    console.error('❌ 迁移过程中出错:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

// 帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
📋 密码迁移脚本使用说明:

用法:
  node migrate-encrypted-passwords.js [strategy]

策略选项:
  reset   - 标记为需要密码重置（默认，推荐）
  delete  - 删除记录并要求重新注册

示例:
  node migrate-encrypted-passwords.js reset   # 标记为重置
  node migrate-encrypted-passwords.js delete  # 删除记录

注意:
- 执行前会显示需要处理的记录
- delete策略会先备份数据到 pending_registration_backup_encrypted 表
- 建议先使用reset策略，如果有问题再考虑delete
  `);
  process.exit(0);
}

// ES模块方式执行
migrateEncryptedPasswords().catch(console.error);