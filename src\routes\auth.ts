import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";
import { db } from "../lib/drizzle.js";
import {
  user,
  organization,
  member,
  subscription,
} from "../lib/auth-schema.js";
import { eq, sql } from "drizzle-orm";
import { inviteCodeService } from "../services/invite-code.js";
import { checkMembershipLimit } from "../lib/organization.js";
import { auth } from "../auth.js";
import { inviteCodeContextManager } from "../services/invite-code-context.js";
import { randomUUID } from "crypto";
import { emailService } from "../services/email-service.js";
import { requireEmailVerification } from "../middleware/email-verification.js";
import { EmailTemplateManager } from "../services/email-templates.js";
import {
  i18nMiddleware,
  createI18nErrorResponse,
  createI18nSuccessResponse,
} from "../middleware/i18n-middleware.js";
// 企业富化相关导入已移除 - 该功能已迁移到专门的企业服务中

// 创建国际化的验证模式
const createI18nSchema = (t: (key: string, params?: any) => string) => ({
  signUpInvite: z.object({
    email: z.string().email(t("validation.email")),
    password: z.string().min(8, t("validation.password")),
    name: z.string().min(1, t("validation.required")),
    company_name: z.string().min(1, t("validation.required")),
    invite_code: z.string().min(1, t("validation.required")),
    language: z.string().optional(),
  }),

  createSuperAdmin: z.object({
    email: z.string().email(t("validation.email")),
    password: z.string().min(1, t("validation.required")),
    name: z.string().min(1, t("validation.required")),
    secret_key: z.string().min(1, t("validation.required")),
  }),

  createAdmin: z.object({
    email: z.string().email(t("validation.email")),
    password: z.string().min(8, t("validation.password")),
    name: z.string().min(1, t("validation.required")),
  }),
});

// 保持向后兼容的默认模式（使用中文）
const signUpInviteSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(8, "密码长度至少8位"),
  name: z.string().min(1, "用户名不能为空"),
  company_name: z.string().min(1, "公司名称不能为空"),
  invite_code: z.string().min(1, "邀请码不能为空"),
  language: z.string().optional(),
});

const createSuperAdminSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(1, "密码不能为空"),
  name: z.string().min(1, "用户名不能为空"),
  secret_key: z.string().min(1, "密钥不能为空"),
});

const createAdminSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(8, "密码长度至少8位"),
  name: z.string().min(1, "用户名不能为空"),
});

export async function registerAuthRoutes(app: FastifyInstance) {
  // 注册国际化中间件
  app.addHook("preHandler", i18nMiddleware);

  // 创建超级管理员端点
  app.post(
    "/api/auth/create-super-admin",
    {
      schema: {
        description: "创建超级管理员用户",
        tags: ["Auth"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
            name: { type: "string", minLength: 1 },
            secret_key: { type: "string", minLength: 1 },
          },
          required: ["email", "password", "name", "secret_key"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              user: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  email: { type: "string" },
                  name: { type: "string" },
                  role: { type: "string" },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          403: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
          password: string;
          name: string;
          secret_key: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        // 验证请求数据
        const { email, password, name, secret_key } =
          createSuperAdminSchema.parse(request.body);

        // 验证密钥
        if (secret_key !== "SpecificAIDay1Global") {
          return reply.status(403).send({
            error: "INVALID_SECRET_KEY",
            message: request.t("auth.admin.invalidSecretKey"),
          });
        }

        // 检查用户是否已存在
        const existingUser = await db
          .select()
          .from(user)
          .where(eq(user.email, email))
          .limit(1);

        if (existingUser.length > 0) {
          return reply.status(400).send({
            error: "USER_EXISTS",
            message: request.t("auth.register.userExists"),
          });
        }

        // 创建超级管理员用户
        const result = await db.transaction(async (tx) => {
          // 使用 Better Auth 创建用户
          const authResponse = await auth.api.signUpEmail({
            body: {
              email,
              password,
              name,
            },
          });

          if (!authResponse?.user?.id) {
            throw new Error(request.t("auth.user.createFailed"));
          }

          const userId = authResponse.user.id;
          const now = new Date();

          // 更新用户信息：设置超级管理员角色（注意：超级管理员仍需验证邮箱）
          await tx
            .update(user)
            .set({
              role: "super_admin", // 设置为超级管理员
              updatedAt: now,
            })
            .where(eq(user.id, userId));

          // 检查或创建SpecificAI组织
          let specificAIOrg = await tx
            .select()
            .from(organization)
            .where(eq(organization.name, "SpecificAI"))
            .limit(1);

          let orgId: string;

          if (specificAIOrg.length === 0) {
            // 创建SpecificAI组织
            orgId = randomUUID();
            await tx.insert(organization).values({
              id: orgId,
              name: "SpecificAI",
              slug: "specificai",
              createdAt: now,
            });

            // 创建enterprise订阅计划
            await tx.insert(subscription).values({
              id: randomUUID(),
              organizationId: orgId,
              plan: "enterprise",
              memberLimit: 100,
              createdAt: now,
              updatedAt: now,
            });
          } else {
            orgId = specificAIOrg[0]!.id;
          }

          // 将super_admin用户设为组织owner
          await tx.insert(member).values({
            id: randomUUID(),
            organizationId: orgId,
            userId: userId,
            role: "owner",
            createdAt: now,
          });

          return {
            id: userId,
            email,
            name,
            role: "super_admin",
            organizationId: orgId,
            organizationName: "SpecificAI",
          };
        });

        app.log.info("超级管理员创建成功:", {
          userId: result.id,
          email: result.email,
          role: result.role,
          organizationId: result.organizationId,
          organizationName: result.organizationName,
        });

        return reply.status(200).send({
          success: true,
          message: request.t("auth.admin.superAdminCreated"),
          user: result,
        });
      } catch (error) {
        app.log.error("创建超级管理员失败:", error);

        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: error.errors.map((e) => e.message).join(", "),
          });
        }

        const errorMessage =
          error instanceof Error ? error.message : "创建失败，请重试";

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: errorMessage,
        });
      }
    }
  );

  // 创建管理员端点
  app.post(
    "/api/auth/create-admin",
    {
      schema: {
        description: "创建管理员用户（仅限超级管理员调用）",
        tags: ["Auth"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
            name: { type: "string", minLength: 1 },
          },
          required: ["email", "password", "name"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              user: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  email: { type: "string" },
                  name: { type: "string" },
                  role: { type: "string" },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          401: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          403: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
          password: string;
          name: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 检查当前用户是否为超级管理员
        const currentUser = await db
          .select({ role: user.role })
          .from(user)
          .where(eq(user.id, session.user.id))
          .limit(1);

        if (
          currentUser.length === 0 ||
          currentUser[0]?.role !== "super_admin"
        ) {
          return reply.status(403).send({
            error: "INSUFFICIENT_PERMISSIONS",
            message: "只有超级管理员才能创建管理员账户",
          });
        }

        // 验证请求数据
        const { email, password, name } = createAdminSchema.parse(request.body);

        // 检查用户是否已存在
        const existingUser = await db
          .select()
          .from(user)
          .where(eq(user.email, email))
          .limit(1);

        if (existingUser.length > 0) {
          return reply.status(400).send({
            error: "USER_EXISTS",
            message: "该邮箱已注册",
          });
        }

        // 创建管理员用户
        const result = await db.transaction(async (tx) => {
          // 使用 Better Auth 创建用户
          const authResponse = await auth.api.signUpEmail({
            body: {
              email,
              password,
              name,
            },
          });

          if (!authResponse?.user?.id) {
            throw new Error("用户创建失败");
          }

          const userId = authResponse.user.id;
          const now = new Date();

          // 更新用户信息：设置管理员角色（注意：管理员仍需验证邮箱）
          await tx
            .update(user)
            .set({
              role: "admin", // 设置为管理员
              updatedAt: now,
            })
            .where(eq(user.id, userId));

          // 查找SpecificAI组织
          const specificAIOrg = await tx
            .select()
            .from(organization)
            .where(eq(organization.name, "SpecificAI"))
            .limit(1);

          if (specificAIOrg.length === 0) {
            throw new Error("SpecificAI组织不存在，请先创建超级管理员");
          }

          const orgId = specificAIOrg[0]!.id;

          // 将admin用户设为组织member
          await tx.insert(member).values({
            id: randomUUID(),
            organizationId: orgId,
            userId: userId,
            role: "member",
            createdAt: now,
          });

          return {
            id: userId,
            email,
            name,
            role: "admin",
            organizationId: orgId,
            organizationName: "SpecificAI",
          };
        });

        app.log.info("管理员创建成功:", {
          userId: result.id,
          email: result.email,
          role: result.role,
          organizationId: result.organizationId,
          organizationName: result.organizationName,
          createdBy: session.user.id,
        });

        return reply.status(200).send({
          success: true,
          message: "管理员创建成功",
          user: result,
        });
      } catch (error) {
        app.log.error("创建管理员失败:", error);

        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: error.errors.map((e) => e.message).join(", "),
          });
        }

        const errorMessage =
          error instanceof Error ? error.message : "创建失败，请重试";

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: errorMessage,
        });
      }
    }
  );

  // 邀请码注册端点（重构版本 - 使用临时注册表）
  app.post(
    "/api/auth/sign-up-invite",
    {
      schema: {
        description: "使用邀请码注册新用户（重构版本，使用临时注册表）",
        tags: ["Auth"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
            name: { type: "string", minLength: 1 },
            company_name: { type: "string", minLength: 1 },
            invite_code: { type: "string", minLength: 1 },
            language: { type: "string" },
          },
          required: [
            "email",
            "password",
            "name",
            "company_name",
            "invite_code",
          ],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  pendingId: { type: "string" },
                  email: { type: "string" },
                  verificationSent: { type: "boolean" },
                  nextSteps: {
                    type: "object",
                    properties: {
                      requireEmailVerification: { type: "boolean" },
                      verificationEmailSent: { type: "boolean" },
                      instructions: { type: "string" },
                      canResendEmail: { type: "boolean" },
                      troubleshooting: { type: "string" },
                    },
                  },
                },
              },
              // 向后兼容性字段
              user: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  email: { type: "string" },
                  name: { type: "string" },
                  company_name: { type: "string" },
                  emailVerified: { type: "boolean" },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          409: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
          password: string;
          name: string;
          company_name: string;
          invite_code: string;
          language?: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      const startTime = Date.now();
      
      try {
        // 验证请求数据
        const validatedData = signUpInviteSchema.parse(request.body);
        const { email, password, name, company_name, invite_code, language } = validatedData;
        
        app.log.info(`临时注册请求（重构版本）: ${email}, 邀请码: ${invite_code}`);
        
        // 注意：不在这里加密密码，让Better Auth处理加密
        // const bcrypt = await import('bcryptjs');
        // const hashedPassword = await bcrypt.default.hash(password, 12);
        
        // 获取客户端信息
        const ipAddress = getClientIP(request);
        const userAgent = request.headers['user-agent'];
        
        // 使用 pending registration 服务创建临时注册记录
        const { pendingRegistrationService } = await import('../services/pending-registration.js');
        
        const result = await pendingRegistrationService.createPendingRegistration({
          email,
          password: password, // 传递明文密码，让Better Auth处理加密
          name,
          companyName: company_name,
          inviteCode: invite_code,
          language: language || "chinese",
          ipAddress,
          userAgent,
        });
        
        const processingTime = Date.now() - startTime;
        
        if (!result.success) {
          app.log.warn(`临时注册失败（重构版本）: ${email}, 原因: ${result.error}`, {
            processingTime,
          });
          
          // 根据错误类型返回不同的状态码
          if (result.error?.includes("邀请码无效")) {
            return reply.status(400).send(createI18nErrorResponse(
              request,
              "INVALID_INVITE_CODE",
              result.error
            ));
          }
          
          if (result.error?.includes("已注册")) {
            return reply.status(409).send(createI18nErrorResponse(
              request,
              "USER_EXISTS", 
              result.error
            ));
          }
          
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "REGISTRATION_FAILED",
            result.error || "注册失败，请重试"
          ));
        }
        
        app.log.info(`临时注册成功（重构版本）: ${email}, pendingId: ${result.pendingId}`, {
          processingTime,
          verificationSent: result.verificationSent,
        });
        
        // 构建响应数据，保持向后兼容性
        const responseData = {
          success: true,
          message: result.verificationSent
            ? request.t("auth.register.success")
            : request.t("auth.register.emailSendFailed"),
          data: {
            pendingId: result.pendingId,
            email,
            verificationSent: result.verificationSent,
            nextSteps: result.nextSteps,
          },
          // 向后兼容性字段
          user: {
            id: result.pendingId, // 临时使用 pendingId 作为 id
            email,
            name,
            company_name,
            emailVerified: false, // 新注册用户邮箱未验证
          },
          nextSteps: result.nextSteps, // 保持旧版本的字段结构
        };
        
        return reply.status(200).send(responseData);
        
      } catch (error) {
        const processingTime = Date.now() - startTime;
        app.log.error("临时注册异常（重构版本）:", {
          error: error instanceof Error ? error.message : String(error),
          email: request.body?.email,
          processingTime,
        });
        
        if (error instanceof z.ZodError) {
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "VALIDATION_ERROR",
            error.errors.map((e) => e.message).join(", ")
          ));
        }
        
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          error instanceof Error ? error.message : "注册失败，请重试"
        ));
      }
    }
  );

  // 验证邀请码端点
  app.post(
    "/api/auth/validate-invite-code",
    {
      schema: {
        description: "验证邀请码有效性",
        tags: ["Auth"],
        body: {
          type: "object",
          properties: {
            invite_code: { type: "string", minLength: 1 },
          },
          required: ["invite_code"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              valid: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{ Body: { invite_code: string } }>,
      reply: FastifyReply
    ) => {
      try {
        const { invite_code } = request.body;

        const validation = await inviteCodeService.verifyCode(invite_code);

        return reply.status(200).send({
          valid: validation.valid,
          message: validation.valid
            ? "邀请码有效"
            : validation.error || "邀请码无效",
        });
      } catch (error) {
        app.log.error("验证邀请码失败:", error);
        return reply.status(500).send({
          valid: false,
          message: "验证失败，请重试",
        });
      }
    }
  );

  // 获取用户组织信息端点
  app.get(
    "/api/auth/user/organizations",
    {
      schema: {
        description: "获取当前用户的组织信息",
        tags: ["Auth"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        response: {
          200: {
            type: "object",
            properties: {
              organizations: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    name: { type: "string" },
                    slug: { type: "string" },
                    role: { type: "string" },
                    subscription: {
                      type: "object",
                      properties: {
                        plan: { type: "string" },
                        memberLimit: { type: "number" },
                      },
                    },
                  },
                },
              },
            },
          },
          401: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 获取用户组织
        const memberOrganizations = await db
          .select({
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            role: member.role,
            subscriptionPlan: subscription.plan,
            subscriptionMemberLimit: subscription.memberLimit,
          })
          .from(member)
          .innerJoin(organization, eq(member.organizationId, organization.id))
          .leftJoin(
            subscription,
            eq(organization.id, subscription.organizationId)
          )
          .where(eq(member.userId, session.user.id));

        const organizations = memberOrganizations.map((org) => ({
          id: org.id,
          name: org.name,
          slug: org.slug,
          role: org.role,
          subscription: org.subscriptionPlan
            ? {
                plan: org.subscriptionPlan,
                memberLimit: org.subscriptionMemberLimit,
              }
            : null,
        }));

        return reply.status(200).send({
          organizations,
        });
      } catch (error) {
        app.log.error("获取用户组织失败:", error);
        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "获取组织信息失败",
        });
      }
    }
  );

  // // 扩展的session端点，包含组织和订阅信息
  // app.get(
  //   "/api/auth/session-extended",
  //   {
  //     schema: {
  //       description: "获取扩展的用户会话信息，包含组织和订阅数据",
  //       tags: ["Auth"],
  //       security: [{ BearerAuth: [] }, { CookieAuth: [] }],
  //       response: {
  //         200: {
  //           type: "object",
  //           properties: {
  //             user: {
  //               type: "object",
  //               properties: {
  //                 id: { type: "string" },
  //                 email: { type: "string" },
  //                 name: { type: "string" },
  //               },
  //             },
  //             organization: {
  //               type: "object",
  //               properties: {
  //                 id: { type: "string" },
  //                 name: { type: "string" },
  //                 slug: { type: "string" },
  //                 role: { type: "string" },
  //                 subscription: {
  //                   type: "object",
  //                   properties: {
  //                     plan: { type: "string" },
  //                     memberLimit: { type: "number" },
  //                     currentMemberCount: { type: "number" },
  //                     canAddMember: { type: "boolean" },
  //                     suggestedPlan: { type: "string" },
  //                   },
  //                 },
  //               },
  //             },
  //           },
  //         },
  //         401: {
  //           type: "object",
  //           properties: {
  //             error: { type: "string" },
  //             message: { type: "string" },
  //           },
  //         },
  //       },
  //     },
  //   },
  //   async (request: FastifyRequest, reply: FastifyReply) => {
  //     try {
  //       // 验证用户会话
  //       const headers = new Headers();
  //       Object.entries(request.headers).forEach(([key, value]) => {
  //         if (typeof value === "string") {
  //           headers.set(key, value);
  //         } else if (Array.isArray(value)) {
  //           headers.set(key, value.join(", "));
  //         }
  //       });

  //       const session = await auth.api.getSession({ headers });

  //       if (!session?.user) {
  //         return reply.status(401).send({
  //           error: "UNAUTHORIZED",
  //           message: "请先登录",
  //         });
  //       }

  //       try {
  //         // 获取用户的组织信息
  //         const memberData = await db
  //           .select({
  //             organizationId: organization.id,
  //             organizationName: organization.name,
  //             organizationSlug: organization.slug,
  //             role: member.role,
  //             createdAt: member.createdAt,
  //           })
  //           .from(member)
  //           .innerJoin(organization, eq(member.organizationId, organization.id))
  //           .where(eq(member.userId, session.user.id))
  //           .orderBy(sql`${member.createdAt} DESC`)
  //           .limit(1);

  //         if (memberData.length > 0) {
  //           const userMember = memberData[0]!;
  //           // 检查成员限制状态
  //           const memberStatus = await checkMembershipLimit(
  //             userMember.organizationId
  //           );

  //           // 返回扩展的session数据
  //           return reply.status(200).send({
  //             user: session.user,
  //             organization: {
  //               id: userMember.organizationId,
  //               name: userMember.organizationName,
  //               slug: userMember.organizationSlug,
  //               role: userMember.role,
  //               subscription: {
  //                 plan: memberStatus.plan,
  //                 memberLimit: memberStatus.limit,
  //                 currentMemberCount: memberStatus.currentCount,
  //                 canAddMember: memberStatus.canAddMember,
  //                 suggestedPlan: memberStatus.suggestedPlan,
  //               },
  //             },
  //           });
  //         }

  //         // 如果用户没有组织，只返回用户信息
  //         return reply.status(200).send({
  //           user: session.user,
  //         });
  //       } catch (error) {
  //         app.log.error("获取组织信息失败:", error);
  //         // 如果获取组织信息失败，至少返回基本的session
  //         return reply.status(200).send({
  //           user: session.user,
  //         });
  //       }
  //     } catch (error) {
  //       app.log.error("获取扩展session失败:", error);
  //       return reply.status(500).send({
  //         error: "INTERNAL_ERROR",
  //         message: "获取会话信息失败",
  //       });
  //     }
  //   }
  // );

  // 用户语言更新接口
  app.put(
    "/api/auth/user/language",
    {
      schema: {
        description: "更新用户语言设置",
        tags: ["Auth"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        body: {
          type: "object",
          properties: {
            language: { type: "string", minLength: 1 },
          },
          required: ["language"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              language: { type: "string" },
            },
          },
          400: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          401: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          language: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 验证语言参数
        const { language } = request.body;

        if (!language || language.trim().length === 0) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: request.t("validation.required"),
          });
        }

        // 标准化和验证语言参数
        const { i18nService } = await import("../services/i18n-service.js");
        const { normalizeLanguageCode } = await import(
          "../middleware/i18n-middleware.js"
        );

        const normalizedLanguage = normalizeLanguageCode(language.trim());
        if (
          !normalizedLanguage ||
          !i18nService.isSupportedLanguage(normalizedLanguage)
        ) {
          return reply.status(400).send({
            error: "INVALID_LANGUAGE",
            message: request.t("validation.invalidFormat"),
            supportedFormats: [
              "chinese",
              "english",
              "japanese",
              "korean",
              "zh-CN",
              "en-US",
              "ja-JP",
              "ko-KR",
            ],
          });
        }

        // 使用语言切换处理函数更新当前请求的语言上下文
        const { handleLanguageSwitch } = await import(
          "../middleware/i18n-middleware.js"
        );
        await handleLanguageSwitch(request, reply, normalizedLanguage as any);

        app.log.info("用户语言切换成功:", {
          userId: session.user.id,
          email: session.user.email,
          language: language.trim(),
          note: "仅影响当前会话，不更新数据库",
        });

        return reply.status(200).send({
          success: true,
          message: request.t("auth.user.languageUpdated"),
          language: normalizedLanguage, // 返回标准化后的语言代码
          originalLanguage: language.trim(), // 保留原始输入以供参考
        });
      } catch (error) {
        app.log.error("更新用户语言失败:", error);

        const errorMessage =
          error instanceof Error ? error.message : "更新失败，请重试";

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: errorMessage,
        });
      }
    }
  );

  // 重发验证邮件接口（传统版本，需要登录）
  app.post(
    "/api/auth/resend-verification",
    {
      schema: {
        description: "重新发送邮箱验证邮件（传统版本，需要登录）",
        tags: ["Auth"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  email: { type: "string" },
                  resendCount: { type: "number" },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          401: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          429: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
              retryAfter: { type: "number" },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            success: false,
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 检查用户是否已经验证邮箱
        const userData = await db
          .select({
            emailVerified: user.emailVerified,
            email: user.email,
            name: user.name,
          })
          .from(user)
          .where(eq(user.id, session.user.id))
          .limit(1);

        if (userData.length === 0) {
          return reply.status(400).send({
            success: false,
            error: "USER_NOT_FOUND",
            message: "用户不存在",
          });
        }

        const currentUser = userData[0]!;

        if (currentUser.emailVerified) {
          return reply.status(400).send({
            success: false,
            error: "ALREADY_VERIFIED",
            message: "邮箱已经验证过了",
          });
        }

        // TODO: 实现频率限制检查
        // 这里应该检查用户在一定时间内的重发次数，防止滥用

        try {
          // 使用 Better Auth 重新发送验证邮件
          const result = await auth.api.sendVerificationEmail({
            body: {
              email: currentUser.email,
              callbackURL: `${process.env.BETTER_AUTH_URL || "http://localhost:10086"}/api/auth/verify-email`,
            },
          });

          app.log.info("重发验证邮件请求（传统版本）:", {
            userId: session.user.id,
            email: currentUser.email,
            success: true,
          });

          return reply.status(200).send({
            success: true,
            message: "验证邮件已重新发送，请检查您的邮箱",
            data: {
              email: currentUser.email,
              resendCount: 1, // TODO: 实际应该从数据库获取
            },
          });
        } catch (emailError) {
          app.log.error("重发验证邮件失败（传统版本）:", emailError);
          return reply.status(500).send({
            success: false,
            error: "EMAIL_SEND_FAILED",
            message: "发送验证邮件失败，请稍后重试",
          });
        }
      } catch (error) {
        app.log.error("重发验证邮件接口错误（传统版本）:", error);
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: "服务器内部错误，请稍后重试",
        });
      }
    }
  );

  // 重发临时注册验证邮件接口（重构版本，不需要登录）
  app.post(
    "/api/auth/resend-pending-verification",
    {
      schema: {
        description: "重新发送临时注册的验证邮件（重构版本，不需要登录）",
        tags: ["Auth", "Registration"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
          },
          required: ["email"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  email: { type: "string" },
                  verificationSent: { type: "boolean" },
                  nextSteps: {
                    type: "object",
                    properties: {
                      instructions: { type: "string" },
                      canResendEmail: { type: "boolean" },
                    },
                  },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          429: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
              retryAfter: { type: "number" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { email } = request.body;
        
        if (!email || !email.trim()) {
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "VALIDATION_ERROR",
            "邮箱地址不能为空"
          ));
        }
        
        app.log.info(`重发临时注册验证邮件请求（重构版本）: ${email}`);
        
        // 使用 pending registration 服务重发验证邮件
        const { pendingRegistrationService } = await import('../services/pending-registration.js');
        const result = await pendingRegistrationService.resendVerificationEmail(email);
        
        if (!result.success) {
          app.log.warn(`重发临时注册验证邮件失败（重构版本）: ${email}, 原因: ${result.error}`);
          
          if (result.error?.includes("稍后再重发")) {
            return reply.status(429).send(createI18nErrorResponse(
              request,
              "RATE_LIMIT_EXCEEDED",
              result.error,
              120
            ));
          }
          
          return reply.status(400).send(createI18nErrorResponse(
            request,
            "RESEND_FAILED",
            result.error || "重发失败"
          ));
        }
        
        app.log.info(`重发临时注册验证邮件成功（重构版本）: ${email}`);
        
        return reply.status(200).send(createI18nSuccessResponse(
          request,
          "auth.register.resendSuccess",
          {
            email,
            verificationSent: result.verificationSent,
            nextSteps: result.nextSteps,
          }
        ));
        
      } catch (error) {
        app.log.error("重发临时注册验证邮件异常（重构版本）:", error);
        
        return reply.status(500).send(createI18nErrorResponse(
          request,
          "INTERNAL_ERROR",
          error instanceof Error ? error.message : "重发失败，请重试"
        ));
      }
    }
  );

  // 查询邮箱验证状态接口
  app.get(
    "/api/auth/verify-email-status",
    {
      schema: {
        description: "查询当前用户的邮箱验证状态",
        tags: ["Auth"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  emailVerified: { type: "boolean" },
                  email: { type: "string" },
                  userId: { type: "string" },
                  verificationRequired: { type: "boolean" },
                },
              },
            },
          },
          401: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            success: false,
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 获取用户邮箱验证状态
        const userData = await db
          .select({
            emailVerified: user.emailVerified,
            email: user.email,
          })
          .from(user)
          .where(eq(user.id, session.user.id))
          .limit(1);

        if (userData.length === 0) {
          return reply.status(401).send({
            success: false,
            error: "USER_NOT_FOUND",
            message: "用户不存在",
          });
        }

        const currentUser = userData[0]!;

        return reply.status(200).send({
          success: true,
          data: {
            emailVerified: currentUser.emailVerified,
            email: currentUser.email,
            userId: session.user.id,
            verificationRequired: !currentUser.emailVerified,
          },
        });
      } catch (error) {
        app.log.error("查询邮箱验证状态失败:", error);
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: "查询验证状态失败，请稍后重试",
        });
      }
    }
  );

  // 邮箱验证成功回调处理接口（传统版本）
  app.get(
    "/api/auth/verify-email-callback",
    {
      schema: {
        description: "邮箱验证成功后的回调处理（传统版本）",
        tags: ["Auth"],
        querystring: {
          type: "object",
          properties: {
            token: { type: "string" },
            redirect: { type: "string" },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Querystring: {
          token?: string;
          redirect?: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      const templateManager = new EmailTemplateManager();

      try {
        const { token, redirect } = request.query;

        if (!token) {
          const errorHtml = templateManager.renderVerificationErrorPage({
            error: "缺少验证令牌",
            canResend: true,
          });
          return reply.type("text/html").send(errorHtml);
        }

        try {
          // 使用 Better Auth 验证令牌
          const verifyResult = await auth.api.verifyEmail({
            body: { token },
          });

          if (!verifyResult.user) {
            const errorHtml = templateManager.renderVerificationErrorPage({
              error: "验证令牌无效或已过期",
              canResend: true,
            });
            return reply.type("text/html").send(errorHtml);
          }

          app.log.info("邮箱验证成功（传统版本）:", {
            userId: verifyResult.user.id,
            email: verifyResult.user.email,
          });

          // 构建重定向URL - 应该重定向到前端URL
          const redirectUrl =
            redirect ||
            `${process.env.FRONTEND_URL || "http://localhost:3000"}/dashboard`;

          // 返回成功页面
          const successHtml = templateManager.renderVerificationSuccessPage({
            userName: verifyResult.user.name,
            userEmail: verifyResult.user.email,
            redirectUrl,
          });

          return reply.type("text/html").send(successHtml);
        } catch (verifyError) {
          app.log.error("邮箱验证失败（传统版本）:", verifyError);
          const errorHtml = templateManager.renderVerificationErrorPage({
            error: "邮箱验证失败，请重新发送验证邮件",
            canResend: true,
          });
          return reply.type("text/html").send(errorHtml);
        }
      } catch (error) {
        app.log.error("邮箱验证回调处理错误（传统版本）:", error);
        const errorHtml = templateManager.renderVerificationErrorPage({
          error: "验证处理失败，请稍后重试",
          canResend: false,
        });
        return reply.type("text/html").send(errorHtml);
      }
    }
  );

  // 临时注册邮箱验证处理接口（GET版本 - 用于邮件链接直接访问）
  app.get(
    "/api/auth/verify-pending-registration",
    {
      schema: {
        description: "通过邮件链接完成临时注册的邮箱验证（GET版本）",
        tags: ["Auth", "Registration"],
        querystring: {
          type: "object",
          properties: {
            token: { type: "string" },
          },
          required: ["token"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  verified: { type: "boolean" },
                  userId: { type: "string" },
                  organizationId: { type: "string" },
                  email: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Querystring: {
          token: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { token } = request.query;
        
        if (!token || !token.trim()) {
          return reply.status(400).send({
            success: false,
            error: "INVALID_TOKEN",
            message: "验证token不能为空",
          });
        }
        
        app.log.info(`处理临时注册邮箱验证(GET): token=${token.substring(0, 8)}...`);
        
        // 使用 pending registration 服务验证并完成注册
        const { pendingRegistrationService } = await import('../services/pending-registration.js');
        const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration(token);
        
        if (!result.success) {
          app.log.warn(`临时注册验证失败: ${result.error}`);
          
          return reply.status(400).send({
            success: false,
            error: "VERIFICATION_FAILED",
            message: result.error || "验证过程失败",
          });
        }
        
        app.log.info(`临时注册验证成功: userId=${result.userId}, orgId=${result.organizationId}`);
        
        return reply.status(200).send({
          success: true,
          message: "邮箱验证成功，账户已激活",
          data: {
            verified: true,
            userId: result.userId,
            organizationId: result.organizationId,
            // Note: email and name would need to be retrieved separately from database if needed
          },
        });
        
      } catch (error) {
        app.log.error("临时注册验证异常(GET):", error);
        
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: "服务器处理验证请求时发生错误，请稍后重试",
        });
      }
    }
  );

  // 临时注册邮箱验证处理接口（POST版本 - 用于前端AJAX调用）
  app.post(
    "/api/auth/verify-pending-registration",
    {
      schema: {
        description: "完成临时注册的邮箱验证并创建正式用户",
        tags: ["Auth", "Registration"],
        body: {
          type: "object",
          properties: {
            token: { type: "string" },
          },
          required: ["token"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "object",
                properties: {
                  verified: { type: "boolean" },
                  userId: { type: "string" },
                  organizationId: { type: "string" },
                  email: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
          },
          400: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          500: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          token: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { token } = request.body;
        
        if (!token || !token.trim()) {
          return reply.status(400).send({
            success: false,
            error: "INVALID_TOKEN",
            message: "验证token不能为空",
          });
        }
        
        app.log.info(`处理临时注册邮箱验证: token=${token.substring(0, 8)}...`);
        
        // 使用 pending registration 服务验证并完成注册
        const { pendingRegistrationService } = await import('../services/pending-registration.js');
        const result = await pendingRegistrationService.verifyEmailAndCompleteRegistration(token);
        
        if (!result.success) {
          app.log.warn(`临时注册验证失败: ${result.error}`);
          
          return reply.status(400).send({
            success: false,
            error: "VERIFICATION_FAILED",
            message: result.error || "验证过程失败",
          });
        }
        
        app.log.info(`临时注册验证成功: userId=${result.userId}, orgId=${result.organizationId}`);
        
        return reply.status(200).send({
          success: true,
          message: "邮箱验证成功，账户已激活",
          data: {
            verified: true,
            userId: result.userId,
            organizationId: result.organizationId,
            // Note: email and name would need to be retrieved separately from database if needed
          },
        });
        
      } catch (error) {
        app.log.error("临时注册验证异常:", error);
        
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: "服务器处理验证请求时发生错误，请稍后重试",
        });
      }
    }
  );

  // 增强的邮箱验证状态查询接口（支持未登录用户）
  app.post(
    "/api/auth/check-email-verification",
    {
      schema: {
        description: "查询邮箱验证状态，支持注册后立即查询",
        tags: ["Auth"],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
          },
          required: ["email"],
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { email } = request.body;

        // 查询用户的邮箱验证状态
        const userData = await db
          .select({
            id: user.id,
            emailVerified: user.emailVerified,
            email: user.email,
            name: user.name,
            createdAt: user.createdAt,
          })
          .from(user)
          .where(eq(user.email, email))
          .limit(1);

        if (userData.length === 0) {
          return reply.status(404).send({
            success: false,
            error: "USER_NOT_FOUND",
            message: "该邮箱尚未注册",
          });
        }

        const userInfo = userData[0]!; // 已经检查过 userData.length > 0
        const isRecentlyRegistered =
          userInfo.createdAt &&
          Date.now() - new Date(userInfo.createdAt).getTime() < 10 * 60 * 1000; // 10分钟内

        return reply.status(200).send({
          success: true,
          message: "查询成功",
          data: {
            emailVerified: userInfo.emailVerified,
            email: userInfo.email,
            name: userInfo.name,
            verificationRequired: !userInfo.emailVerified,
            canResendEmail: !userInfo.emailVerified,
            isRecentlyRegistered,
            nextSteps: !userInfo.emailVerified
              ? {
                  instructions:
                    "请查收邮件并点击验证链接完成注册。如果没有收到邮件，请检查垃圾邮件文件夹。",
                  resendEndpoint: "/api/auth/resend-verification",
                  troubleshooting:
                    "如果持续收不到验证邮件，请联系客服或尝试重新注册。",
                }
              : {
                  instructions: "邮箱已验证，您可以正常登录使用。",
                  loginEndpoint: "/api/auth/sign-in",
                },
          },
        });
      } catch (error) {
        app.log.error("查询邮箱验证状态失败:", error);
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: "查询验证状态失败，请稍后重试",
        });
      }
    }
  );

  // 获取支持的语言列表接口
  app.get(
    "/api/auth/languages",
    {
      schema: {
        description: "获取系统支持的语言列表",
        tags: ["Auth"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              languages: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    code: { type: "string" },
                    name: { type: "string" },
                    nativeName: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { getSupportedLanguages } = await import(
          "../middleware/i18n-middleware.js"
        );
        const languages = getSupportedLanguages();

        return reply.status(200).send({
          success: true,
          message: request.t("common.success"),
          languages,
        });
      } catch (error) {
        app.log.error("获取语言列表失败:", error);
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: request.t("errors.internal"),
        });
      }
    }
  );

  // 语言切换接口（支持未登录用户）
  app.post(
    "/api/auth/switch-language",
    {
      schema: {
        description: "切换系统语言，支持已登录和未登录用户",
        tags: ["Auth"],
        body: {
          type: "object",
          properties: {
            language: {
              type: "string",
              enum: ["zh-CN", "en-US", "ja-JP", "ko-KR"],
            },
          },
          required: ["language"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              language: { type: "string" },
              userUpdated: { type: "boolean" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          language: string;
        };
      }>,
      reply: FastifyReply
    ) => {
      try {
        const { language } = request.body;
        const { handleLanguageSwitch, normalizeLanguageCode } = await import(
          "../middleware/i18n-middleware.js"
        );
        const { i18nService } = await import("../services/i18n-service.js");

        // 标准化语言代码
        const normalizedLanguage = normalizeLanguageCode(language);
        if (
          !normalizedLanguage ||
          !i18nService.isSupportedLanguage(normalizedLanguage)
        ) {
          return reply.status(400).send({
            success: false,
            error: "INVALID_LANGUAGE",
            message: request.t("validation.invalidFormat"),
            supportedFormats: [
              "chinese",
              "english",
              "japanese",
              "korean",
              "zh-CN",
              "en-US",
              "ja-JP",
              "ko-KR",
            ],
          });
        }

        // 执行语言切换（不依赖数据库，仅更新当前请求上下文）
        await handleLanguageSwitch(request, reply, normalizedLanguage as any);

        return reply.status(200).send({
          success: true,
          message: request.t("auth.user.languageUpdated"),
          language: normalizedLanguage, // 返回标准化后的语言代码
          originalLanguage: language, // 保留原始输入以供参考
          userUpdated: false, // 不再更新数据库，仅影响当前请求
        });
      } catch (error) {
        app.log.error("语言切换失败:", error);
        return reply.status(500).send({
          success: false,
          error: "INTERNAL_ERROR",
          message: request.t("errors.internal"),
        });
      }
    }
  );
}

/**
 * 生成组织slug
 */
function generateOrgSlug(name: string): string {
  return (
    name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim()
      .substring(0, 50) + // 限制长度
    "-" +
    Math.random().toString(36).substring(2, 10)
  ); // 添加随机后缀确保唯一性
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: FastifyRequest): string | undefined {
  return (
    (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (request.headers['x-real-ip'] as string) ||
    request.ip ||
    request.socket.remoteAddress
  );
}
