-- ===================================
-- 邮箱注册服务数据库迁移SQL
-- 包含Better Auth基础表和邮件相关扩展表
-- ===================================

-- 创建auth schema (如果不存在)
CREATE SCHEMA IF NOT EXISTS auth;

-- 设置搜索路径
SET search_path TO auth, public;

-- ===================================
-- Better Auth 核心表结构
-- ===================================

-- 用户表
CREATE TABLE IF NOT EXISTS auth.user (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    image TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    role TEXT DEFAULT 'user',
    banned BOOLEAN DEFAULT FALSE,
    ban_reason TEXT,
    ban_expires TIMESTAMP WITHOUT TIME ZONE,
    company_name TEXT,
    language TEXT NOT NULL DEFAULT 'chinese'
);

-- 会话表
CREATE TABLE IF NOT EXISTS auth.session (
    id TEXT PRIMARY KEY,
    expires_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    token TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    ip_address TEXT,
    user_agent TEXT,
    user_id TEXT NOT NULL REFERENCES auth.user(id) ON DELETE CASCADE,
    impersonated_by TEXT,
    active_organization_id TEXT
);

-- 账户表
CREATE TABLE IF NOT EXISTS auth.account (
    id TEXT PRIMARY KEY,
    account_id TEXT NOT NULL,
    provider_id TEXT NOT NULL,
    user_id TEXT NOT NULL REFERENCES auth.user(id) ON DELETE CASCADE,
    access_token TEXT,
    refresh_token TEXT,
    id_token TEXT,
    access_token_expires_at TIMESTAMP WITHOUT TIME ZONE,
    refresh_token_expires_at TIMESTAMP WITHOUT TIME ZONE,
    scope TEXT,
    password TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- 验证表
CREATE TABLE IF NOT EXISTS auth.verification (
    id TEXT PRIMARY KEY,
    identifier TEXT NOT NULL,
    value TEXT NOT NULL,
    expires_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- 组织表
CREATE TABLE IF NOT EXISTS auth.organization (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    logo TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    metadata TEXT,
    has_company_profile BOOLEAN NOT NULL DEFAULT FALSE
);

-- 成员表
CREATE TABLE IF NOT EXISTS auth.member (
    id TEXT PRIMARY KEY,
    organization_id TEXT NOT NULL REFERENCES auth.organization(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES auth.user(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- 邀请表
CREATE TABLE IF NOT EXISTS auth.invitation (
    id TEXT PRIMARY KEY,
    organization_id TEXT NOT NULL REFERENCES auth.organization(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    expires_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    inviter_id TEXT NOT NULL REFERENCES auth.user(id) ON DELETE CASCADE
);

-- 订阅表
CREATE TABLE IF NOT EXISTS auth.subscription (
    id TEXT PRIMARY KEY,
    organization_id TEXT NOT NULL UNIQUE REFERENCES auth.organization(id) ON DELETE CASCADE,
    plan TEXT NOT NULL,
    member_limit INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- ===================================
-- 邮件服务扩展表
-- ===================================

-- 邮件发送日志表
CREATE TABLE IF NOT EXISTS auth.email_log (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    user_id TEXT REFERENCES auth.user(id) ON DELETE SET NULL,
    email_type TEXT NOT NULL CHECK (email_type IN ('verification', 'password-reset', 'notification')),
    recipient_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    template_version TEXT NOT NULL DEFAULT 'v1',
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'delivered', 'bounced')),
    provider_message_id TEXT,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    sent_at TIMESTAMP WITHOUT TIME ZONE,
    delivered_at TIMESTAMP WITHOUT TIME ZONE
);

-- 频率限制表
CREATE TABLE IF NOT EXISTS auth.rate_limit (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    key TEXT NOT NULL,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('email_send', 'api_request', 'login_attempt', 'password_reset', 'verification_request')),
    count INTEGER NOT NULL DEFAULT 1,
    window_start TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    window_end TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    ip_address INET,
    user_id TEXT REFERENCES auth.user(id) ON DELETE CASCADE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- 请求日志表
CREATE TABLE IF NOT EXISTS auth.request_log (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    request_id TEXT NOT NULL,
    method TEXT NOT NULL,
    url TEXT NOT NULL,
    user_agent TEXT,
    ip_address INET,
    user_id TEXT REFERENCES auth.user(id) ON DELETE SET NULL,
    status_code INTEGER,
    response_time INTEGER, -- 毫秒
    request_size INTEGER, -- 字节
    response_size INTEGER, -- 字节
    error_message TEXT,
    security_flags TEXT[] DEFAULT '{}', -- 安全标记数组
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- 安全事件表
CREATE TABLE IF NOT EXISTS auth.security_event (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    event_type TEXT NOT NULL CHECK (event_type IN ('suspicious_login', 'rate_limit_exceeded', 'invalid_token', 'brute_force_attempt', 'xss_attempt', 'sql_injection_attempt')),
    severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    user_id TEXT REFERENCES auth.user(id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    request_data JSONB DEFAULT '{}',
    description TEXT,
    action_taken TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITHOUT TIME ZONE,
    resolved_by TEXT REFERENCES auth.user(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

-- ===================================
-- JWT插件核心表 - JWKS密钥存储
-- ===================================

-- JWKS表 - 存储JWT签名密钥对
CREATE TABLE IF NOT EXISTS auth.jwks (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    public_key TEXT NOT NULL,
    private_key TEXT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- 可选字段：密钥算法信息
    algorithm TEXT DEFAULT 'EdDSA',
    curve TEXT DEFAULT 'Ed25519',
    key_id TEXT UNIQUE DEFAULT gen_random_uuid()::TEXT,
    
    -- 密钥状态
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITHOUT TIME ZONE,
    
    -- 元数据
    metadata JSONB DEFAULT '{}'
);

-- ===================================
-- 索引优化
-- ===================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_user_email ON auth.user(email);
CREATE INDEX IF NOT EXISTS idx_user_email_verified ON auth.user(email_verified);
CREATE INDEX IF NOT EXISTS idx_user_role ON auth.user(role);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON auth.user(created_at);

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_session_token ON auth.session(token);
CREATE INDEX IF NOT EXISTS idx_session_user_id ON auth.session(user_id);
CREATE INDEX IF NOT EXISTS idx_session_expires_at ON auth.session(expires_at);

-- 验证表索引
CREATE INDEX IF NOT EXISTS idx_verification_identifier ON auth.verification(identifier);
CREATE INDEX IF NOT EXISTS idx_verification_expires_at ON auth.verification(expires_at);

-- 组织和成员表索引
CREATE INDEX IF NOT EXISTS idx_organization_slug ON auth.organization(slug);
CREATE INDEX IF NOT EXISTS idx_member_user_id ON auth.member(user_id);
CREATE INDEX IF NOT EXISTS idx_member_organization_id ON auth.member(organization_id);

-- 邮件日志表索引
CREATE INDEX IF NOT EXISTS idx_email_log_user_id ON auth.email_log(user_id);
CREATE INDEX IF NOT EXISTS idx_email_log_recipient ON auth.email_log(recipient_email);
CREATE INDEX IF NOT EXISTS idx_email_log_type_status ON auth.email_log(email_type, status);
CREATE INDEX IF NOT EXISTS idx_email_log_created_at ON auth.email_log(created_at);

-- 频率限制表索引
CREATE INDEX IF NOT EXISTS idx_rate_limit_key_type ON auth.rate_limit(key, operation_type);
CREATE INDEX IF NOT EXISTS idx_rate_limit_window ON auth.rate_limit(window_start, window_end);
CREATE INDEX IF NOT EXISTS idx_rate_limit_user_id ON auth.rate_limit(user_id);
CREATE INDEX IF NOT EXISTS idx_rate_limit_ip ON auth.rate_limit(ip_address);

-- 请求日志表索引
CREATE INDEX IF NOT EXISTS idx_request_log_user_id ON auth.request_log(user_id);
CREATE INDEX IF NOT EXISTS idx_request_log_ip ON auth.request_log(ip_address);
CREATE INDEX IF NOT EXISTS idx_request_log_created_at ON auth.request_log(created_at);
CREATE INDEX IF NOT EXISTS idx_request_log_method_url ON auth.request_log(method, url);

-- 安全事件表索引
CREATE INDEX IF NOT EXISTS idx_security_event_type_severity ON auth.security_event(event_type, severity);
CREATE INDEX IF NOT EXISTS idx_security_event_user_id ON auth.security_event(user_id);
CREATE INDEX IF NOT EXISTS idx_security_event_ip ON auth.security_event(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_event_resolved ON auth.security_event(resolved);
CREATE INDEX IF NOT EXISTS idx_security_event_created_at ON auth.security_event(created_at);

-- JWKS表索引
CREATE INDEX IF NOT EXISTS idx_jwks_key_id ON auth.jwks(key_id);
CREATE INDEX IF NOT EXISTS idx_jwks_active ON auth.jwks(active);
CREATE INDEX IF NOT EXISTS idx_jwks_created_at ON auth.jwks(created_at);
CREATE INDEX IF NOT EXISTS idx_jwks_expires_at ON auth.jwks(expires_at) WHERE expires_at IS NOT NULL;

-- ===================================
-- 触发器和函数
-- ===================================

-- 更新时间戳函数
CREATE OR REPLACE FUNCTION auth.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加自动更新触发器
DROP TRIGGER IF EXISTS update_user_updated_at ON auth.user;
CREATE TRIGGER update_user_updated_at 
    BEFORE UPDATE ON auth.user 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_session_updated_at ON auth.session;
CREATE TRIGGER update_session_updated_at 
    BEFORE UPDATE ON auth.session 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_account_updated_at ON auth.account;
CREATE TRIGGER update_account_updated_at 
    BEFORE UPDATE ON auth.account 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscription_updated_at ON auth.subscription;
CREATE TRIGGER update_subscription_updated_at 
    BEFORE UPDATE ON auth.subscription 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_email_log_updated_at ON auth.email_log;
CREATE TRIGGER update_email_log_updated_at 
    BEFORE UPDATE ON auth.email_log 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_rate_limit_updated_at ON auth.rate_limit;
CREATE TRIGGER update_rate_limit_updated_at 
    BEFORE UPDATE ON auth.rate_limit 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

DROP TRIGGER IF EXISTS update_security_event_updated_at ON auth.security_event;
CREATE TRIGGER update_security_event_updated_at 
    BEFORE UPDATE ON auth.security_event 
    FOR EACH ROW EXECUTE FUNCTION auth.update_updated_at_column();

-- ===================================
-- 数据清理函数
-- ===================================

-- 清理过期数据的函数
CREATE OR REPLACE FUNCTION auth.cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的验证记录
    DELETE FROM auth.verification WHERE expires_at < NOW();
    
    -- 清理过期的会话记录
    DELETE FROM auth.session WHERE expires_at < NOW();
    
    -- 清理过期的JWKS密钥
    DELETE FROM auth.jwks WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    -- 清理30天前的请求日志
    DELETE FROM auth.request_log WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 清理7天前的频率限制记录
    DELETE FROM auth.rate_limit WHERE window_end < NOW() - INTERVAL '7 days';
    
    -- 清理90天前的邮件日志
    DELETE FROM auth.email_log WHERE created_at < NOW() - INTERVAL '90 days';
    
    RAISE NOTICE 'Expired data cleanup completed at %', NOW();
END;
$$ LANGUAGE 'plpgsql';

-- ===================================
-- 权限设置
-- ===================================

-- 为应用用户授权（假设应用用户名为 specific_ai_auth）
DO $$
BEGIN
    -- 检查用户是否存在，如果不存在则创建
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'specific_ai_auth') THEN
        CREATE USER specific_ai_auth WITH PASSWORD 'your_secure_password_here';
    END IF;
    
    -- 授予schema使用权限
    GRANT USAGE ON SCHEMA auth TO specific_ai_auth;
    
    -- 授予所有表的权限
    GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA auth TO specific_ai_auth;
    
    -- 授予序列权限（用于ID生成）
    GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA auth TO specific_ai_auth;
    
    -- 为将来创建的表自动授权
    ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO specific_ai_auth;
    ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT USAGE, SELECT ON SEQUENCES TO specific_ai_auth;
END $$;

-- ===================================
-- 验证安装
-- ===================================

-- 检查表是否成功创建
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'auth'
ORDER BY tablename;

-- 检查索引是否成功创建
SELECT 
    schemaname,
    tablename,
    indexname
FROM pg_indexes 
WHERE schemaname = 'auth'
ORDER BY tablename, indexname;

-- 显示完成信息
SELECT 'Database initialization completed successfully!' as status;