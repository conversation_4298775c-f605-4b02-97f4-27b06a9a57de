/**
 * 订阅计划配置和管理
 */

export type PlanType = "basic" | "pro" | "enterprise";

export interface PlanConfig {
  name: string;
  displayName: string;
  memberLimit: number;
  features: string[];
  price?: {
    monthly: number;
    yearly: number;
    currency: string;
  };
}

/**
 * 订阅计划配置
 */
export const PLAN_CONFIGS: Record<PlanType, PlanConfig> = {
  basic: {
    name: "basic",
    displayName: "基础版",
    memberLimit: 5,
    features: ["basic_auth", "basic_organization", "email_support"],
    price: {
      monthly: 0,
      yearly: 0,
      currency: "CNY",
    },
  },
  pro: {
    name: "pro",
    displayName: "专业版",
    memberLimit: 20,
    features: [
      "basic_auth",
      "basic_organization",
      "advanced_permissions",
      "api_access",
      "priority_support",
    ],
    price: {
      monthly: 299,
      yearly: 2990,
      currency: "CNY",
    },
  },
  enterprise: {
    name: "enterprise",
    displayName: "企业版",
    memberLimit: 100,
    features: [
      "basic_auth",
      "basic_organization",
      "advanced_permissions",
      "api_access",
      "sso_integration",
      "custom_branding",
      "dedicated_support",
    ],
    price: {
      monthly: 999,
      yearly: 9990,
      currency: "CNY",
    },
  },
};

/**
 * 获取计划配置
 * @param planType 计划类型
 * @returns 计划配置
 */
export function getPlanConfig(planType: PlanType): PlanConfig {
  const config = PLAN_CONFIGS[planType];
  if (!config) {
    throw new Error(`Invalid plan type: ${planType}`);
  }
  return config;
}

/**
 * 获取计划的成员限制
 * @param planType 计划类型
 * @returns 成员限制数量
 */
export function getPlanMemberLimit(planType: PlanType): number {
  return getPlanConfig(planType).memberLimit;
}

/**
 * 验证计划类型是否有效
 * @param planType 计划类型
 * @returns 是否有效
 */
export function isValidPlanType(planType: string): planType is PlanType {
  return planType in PLAN_CONFIGS;
}

/**
 * 获取所有可用的计划
 * @returns 所有计划配置
 */
export function getAllPlans(): PlanConfig[] {
  return Object.values(PLAN_CONFIGS);
}

/**
 * 比较两个计划的等级
 * @param planA 计划A
 * @param planB 计划B
 * @returns -1: A < B, 0: A = B, 1: A > B
 */
export function comparePlans(planA: PlanType, planB: PlanType): number {
  const planOrder: PlanType[] = ["basic", "pro", "enterprise"];
  const indexA = planOrder.indexOf(planA);
  const indexB = planOrder.indexOf(planB);

  if (indexA < indexB) return -1;
  if (indexA > indexB) return 1;
  return 0;
}

/**
 * 检查计划是否支持某个功能
 * @param planType 计划类型
 * @param feature 功能名称
 * @returns 是否支持
 */
export function planSupportsFeature(
  planType: PlanType,
  feature: string,
): boolean {
  const config = getPlanConfig(planType);
  return config.features.includes(feature);
}

/**
 * 获取计划升级建议
 * @param currentPlan 当前计划
 * @param requiredMemberCount 需要的成员数量
 * @returns 建议的计划，如果当前计划足够则返回null
 */
export function getSuggestedPlanForMembers(
  currentPlan: PlanType,
  requiredMemberCount: number,
): PlanType | null {
  const currentLimit = getPlanMemberLimit(currentPlan);

  if (currentLimit >= requiredMemberCount) {
    return null; // 当前计划足够
  }

  // 找到满足要求的最低计划
  const plans: PlanType[] = ["basic", "pro", "enterprise"];

  for (const plan of plans) {
    if (getPlanMemberLimit(plan) >= requiredMemberCount) {
      return plan;
    }
  }

  // 如果企业版也不够，返回企业版（可能需要自定义方案）
  return "enterprise";
}
