# 注册验证全流程ER图

## 完整的实体关系图

```mermaid
erDiagram
    user {
        string id
        string name
        string email
        boolean email_verified
        string image
        string created_at
        string updated_at
        string role
        boolean banned
        string ban_reason
        string ban_expires
        string company_name
        string language
    }

    pending_registration {
        string id
        string email
        string password
        string name
        string company_name
        string language
        string invite_code
        boolean invite_code_verified
        string verification_token
        boolean verification_sent
        int verification_attempts
        string last_verification_sent_at
        string status
        string registration_step
        string created_at
        string updated_at
        string expires_at
        string verified_at
        string ip_address
        string user_agent
        string metadata
        string pending_organization_id
        string pending_organization_slug
    }

    registration_verification_log {
        string id
        string pending_registration_id
        string verification_type
        string verification_status
        int attempt_number
        string result_code
        string result_message
        string ip_address
        string user_agent
        int processing_time_ms
        string created_at
        string metadata
    }

    organization {
        string id
        string name
        string slug
        string logo
        string created_at
        string metadata
        boolean has_company_profile
    }

    member {
        string id
        string organization_id
        string user_id
        string role
        string created_at
    }

    invite_code {
        string code
        string status
        int usage_limit
        int used_count
        string created_at
        string expires_at
        string created_by
        string metadata
    }

    verification {
        string id
        string identifier
        string value
        string expires_at
        string created_at
        string updated_at
    }

    session {
        string id
        string expires_at
        string token
        string created_at
        string updated_at
        string ip_address
        string user_agent
        string user_id
        string impersonated_by
        string active_organization_id
    }

    account {
        string id
        string account_id
        string provider_id
        string user_id
        string access_token
        string refresh_token
        string id_token
        string access_token_expires_at
        string refresh_token_expires_at
        string scope
        string password
        string created_at
        string updated_at
    }

    subscription {
        string id
        string organization_id
        string plan
        int member_limit
        string created_at
        string updated_at
    }

    email_log {
        string id
        string user_id
        string email
        string email_type
        string status
        string provider
        string message_id
        string error_message
        int attempt_count
        string sent_at
        string delivered_at
        string created_at
        string updated_at
        string metadata
    }

    rate_limiter {
        string id
        string identifier
        string action
        string window_start
        int request_count
        int max_requests
        int window_duration_ms
        boolean blocked
        string blocked_until
        string created_at
        string updated_at
    }

    security_event {
        string id
        string user_id
        string event_type
        string severity
        string ip_address
        string user_agent
        string details
        boolean resolved
        string resolved_at
        string resolved_by
        string created_at
    }

    %% Relationships
    pending_registration ||--o{ registration_verification_log : has_logs
    user ||--o{ session : has_sessions
    user ||--o{ account : has_accounts
    user ||--o{ member : has_memberships
    user ||--o{ email_log : has_email_logs
    user ||--o{ security_event : has_events

    organization ||--o{ member : has_members
    organization ||--|| subscription : has_subscription

    %% Note: The following data flows are conceptual, not valid in erDiagram
    %% pending_registration ..> user : creates_user
    %% pending_registration ..> organization : creates_org
    %% pending_registration ..> member : creates_member
    %% pending_registration ..> invite_code : uses_invite_code
    %% verification ..> pending_registration : verifies_registration
```

## 核心实体详细定义

### 1. 临时注册表 (pending_registration)
**作用**: 在邮箱验证完成前暂存用户注册信息

**关键字段**:
- `id`: 主键，UUID格式
- `email`: 唯一约束，注册邮箱
- `verification_token`: 与Better Auth验证系统关联
- `status`: 状态流转 (pending → verified → completed)
- `registration_step`: 步骤跟踪 (email_verification → completed)
- `expires_at`: 24小时过期机制

**索引设计**:
```sql
-- 核心查询索引
CREATE INDEX idx_pending_reg_email ON pending_registration(email);
CREATE INDEX idx_pending_reg_token ON pending_registration(verification_token);
CREATE INDEX idx_pending_reg_status ON pending_registration(status);
CREATE INDEX idx_pending_reg_expires ON pending_registration(expires_at);

-- 复合索引优化常用查询
CREATE INDEX idx_pending_reg_email_status ON pending_registration(email, status);
CREATE INDEX idx_pending_reg_status_expires ON pending_registration(status, expires_at);
```

### 2. 注册验证日志表 (registration_verification_log)
**作用**: 详细记录每个验证步骤和结果

**关键字段**:
- `verification_type`: 验证类型分类
  - `email_send`: 发送验证邮件
  - `email_verify`: 验证邮件链接点击
  - `invite_code_verify`: 邀请码验证
- `verification_status`: 验证结果 (success/failed/pending)
- `processing_time_ms`: 性能监控

### 3. 用户表 (user)
**作用**: 正式用户数据存储

**关键字段**:
- `email_verified`: 邮箱验证状态标识
- `company_name`: 从临时注册表继承
- `language`: 国际化支持

## 业务流程映射

### 注册验证完整流程

```mermaid
flowchart TD
    A[用户提交注册] --> B{验证邀请码}
    B -->|无效| C[返回错误]
    B -->|有效| D[创建pending_registration记录]
    
    D --> E[生成verification_token]
    E --> F[记录verification_log: invite_code_verify]
    F --> G[发送验证邮件]
    G --> H[记录verification_log: email_send]
    H --> I[记录email_log]
    
    I --> J[用户点击验证链接]
    J --> K{验证token有效性}
    K -->|无效/过期| L[记录失败日志]
    K -->|有效| M[更新pending_registration状态]
    
    M --> N[记录verification_log: email_verify]
    N --> O[开始用户创建事务]
    
    O --> P[创建user记录]
    P --> Q[创建organization记录]
    Q --> R[创建member关系]
    R --> S[创建subscription记录]
    S --> T[更新邀请码状态]
    T --> U[删除pending_registration]
    U --> V[注册完成]
    
    %% 错误处理
    L --> W[清理过期数据]
    C --> W
    
    %% 状态样式
    classDef success fill:#d4edda,stroke:#155724,color:#155724
    classDef error fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef process fill:#cce5ff,stroke:#0066cc,color:#0066cc
    classDef decision fill:#fff3cd,stroke:#856404,color:#856404
    
    class V success
    class C,L,W error
    class D,E,F,G,H,I,M,N,O,P,Q,R,S,T,U process
    class B,K decision
```

### 状态变化时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as Auth API
    participant DB as 数据库
    participant Email as 邮件服务
    participant BizAPI as 业务API
    
    Note over Client,BizAPI: 1. 注册请求阶段
    Client->>API: POST /api/sign-up/email
    API->>BizAPI: 验证邀请码
    BizAPI-->>API: 邀请码有效
    API->>DB: 创建pending_registration
    API->>DB: 记录verification_log (invite_code_verify)
    
    Note over Client,BizAPI: 2. 邮件发送阶段
    API->>Email: 发送验证邮件
    API->>DB: 记录email_log
    API->>DB: 记录verification_log (email_send)
    API-->>Client: 注册成功，请查收邮件
    
    Note over Client,BizAPI: 3. 邮件验证阶段
    Client->>API: GET /api/email-verification?token=xxx
    API->>DB: 查询pending_registration
    API->>DB: 验证token有效性
    API->>DB: 记录verification_log (email_verify)
    
    Note over Client,BizAPI: 4. 用户创建阶段
    API->>DB: 开始事务
    API->>DB: 创建user
    API->>DB: 创建organization
    API->>DB: 创建member关系
    API->>DB: 创建subscription
    API->>BizAPI: 更新邀请码状态
    API->>DB: 删除pending_registration
    API->>DB: 提交事务
    API-->>Client: 验证成功，账户已激活
```

## 数据流向图

```mermaid
flowchart LR
    subgraph "注册数据流"
        PR[pending_registration] 
        VL[verification_log]
        EL[email_log]
        IC[invite_code<br/>外部系统]
    end
    
    subgraph "用户数据流"
        U[user]
        S[session] 
        A[account]
    end
    
    subgraph "组织数据流"
        O[organization]
        M[member]
        SUB[subscription]
    end
    
    subgraph "监控数据流"
        RL[rate_limiter]
        SE[security_event]
    end
    
    %% 注册流程数据流
    PR -->|验证成功| U
    PR -->|预创建| O
    PR -->|生成关系| M
    PR -->|一对多| VL
    VL -->|记录发送| EL
    IC -.->|验证| PR
    
    %% 用户关系数据流  
    U -->|创建会话| S
    U -->|密码认证| A
    U -->|组织成员| M
    
    %% 组织关系数据流
    O -->|包含成员| M
    O -->|订阅计划| SUB
    
    %% 监控数据流
    U -->|行为记录| RL
    U -->|安全事件| SE
    EL -->|发送频控| RL
    
    %% 样式定义
    classDef temp fill:#fff2cc,stroke:#d6b656
    classDef user fill:#d5e8d4,stroke:#82b366  
    classDef org fill:#dae8fc,stroke:#6c8ebf
    classDef monitor fill:#f8cecc,stroke:#b85450
    
    class PR,VL,EL temp
    class U,S,A user
    class O,M,SUB org
    class RL,SE monitor
```

## 关键约束和索引设计

### 数据完整性约束

```sql
-- 1. 临时注册表约束
ALTER TABLE pending_registration 
ADD CONSTRAINT chk_pending_status 
CHECK (status IN ('pending', 'verified', 'expired', 'failed'));

ALTER TABLE pending_registration 
ADD CONSTRAINT chk_registration_step 
CHECK (registration_step IN ('email_verification', 'completed'));

-- 2. 验证日志约束
ALTER TABLE registration_verification_log 
ADD CONSTRAINT chk_verification_type 
CHECK (verification_type IN ('email_send', 'email_verify', 'invite_code_verify'));

ALTER TABLE registration_verification_log 
ADD CONSTRAINT chk_verification_status 
CHECK (verification_status IN ('success', 'failed', 'pending'));

-- 3. 过期时间约束
ALTER TABLE pending_registration 
ADD CONSTRAINT chk_expires_future 
CHECK (expires_at > created_at);
```

### 性能优化索引

```sql
-- 高频查询索引
CREATE INDEX CONCURRENTLY idx_pending_reg_lookup 
ON pending_registration(email, status, expires_at);

-- 清理任务索引
CREATE INDEX CONCURRENTLY idx_pending_reg_cleanup 
ON pending_registration(status, expires_at) 
WHERE status = 'expired';

-- 验证日志分析索引
CREATE INDEX CONCURRENTLY idx_verification_log_analysis 
ON registration_verification_log(verification_type, verification_status, created_at);

-- 邮件日志索引
CREATE INDEX CONCURRENTLY idx_email_log_tracking 
ON email_log(email_type, status, created_at);
```

## 监控和告警指标

### 业务指标监控

1. **注册转化率**:
   ```sql
   -- 24小时内注册成功率
   SELECT 
     COUNT(CASE WHEN status = 'verified' THEN 1 END) * 100.0 / COUNT(*) as success_rate
   FROM pending_registration 
   WHERE created_at >= NOW() - INTERVAL '24 hours';
   ```

2. **验证失败分析**:
   ```sql
   -- 验证失败原因分布
   SELECT 
     verification_type, 
     verification_status,
     COUNT(*) as count
   FROM registration_verification_log 
   WHERE created_at >= NOW() - INTERVAL '7 days'
   GROUP BY verification_type, verification_status;
   ```

3. **邮件发送监控**:
   ```sql
   -- 邮件发送状态统计
   SELECT 
     status, 
     COUNT(*) as count,
     AVG(attempt_count) as avg_attempts
   FROM email_log 
   WHERE email_type = 'verification'
   AND created_at >= NOW() - INTERVAL '24 hours'
   GROUP BY status;
   ```

### 系统健康检查

```sql
-- 待清理的过期注册记录
SELECT COUNT(*) as expired_pending_count
FROM pending_registration 
WHERE expires_at < NOW() AND status != 'expired';

-- 频率限制触发统计
SELECT action, COUNT(*) as blocked_count
FROM rate_limiter 
WHERE blocked = true 
AND created_at >= NOW() - INTERVAL '1 hour'
GROUP BY action;
```

这个ER图完整地展现了从临时注册到正式用户创建的全流程数据关系，包含了状态跟踪、验证日志、安全监控等所有核心组件，为邮箱注册验证系统提供了完整的数据架构支撑。