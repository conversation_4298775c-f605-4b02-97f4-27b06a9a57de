import type { FastifyRequest, FastifyReply } from "fastify";
import { auth } from "../auth.js";
import { jwtTokenService } from "../services/jwt-service.js";

export interface AuthContext {
  user?: any;
  session?: any;
  authType: "cookie" | "jwt" | "none";
  isAuthenticated: boolean;
}

/**
 * 双重认证中间件
 * 支持Cookie和JWT两种认证方式
 */
export class DualAuthMiddleware {
  /**
   * 智能检测并处理认证
   * @param request Fastify请求对象
   * @param reply Fastify响应对象
   * @returns 认证上下文
   */
  static async authenticate(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<AuthContext> {
    // 检查JWT Token认证
    const jwtResult = await this.tryJWTAuth(request);
    if (jwtResult.isAuthenticated) {
      return jwtResult;
    }

    // 检查Cookie Session认证
    const cookieResult = await this.tryCookieAuth(request);
    if (cookieResult.isAuthenticated) {
      return cookieResult;
    }

    // 无有效认证
    return {
      authType: "none",
      isAuthenticated: false,
    };
  }

  /**
   * 尝试JWT认证
   * @param request Fastify请求对象
   * @returns JWT认证结果
   */
  private static async tryJWTAuth(request: FastifyRequest): Promise<AuthContext> {
    try {
      // 从Authorization header获取JWT token
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return { authType: "none", isAuthenticated: false };
      }

      const token = authHeader.substring(7); // 移除 "Bearer " 前缀
      
      // 使用Better Auth JWT插件验证token
      // 注意：这里需要调用Better Auth的JWT验证API
      const verifyResult = await jwtTokenService.verifyToken(token);
      
      if (!verifyResult.success) {
        console.warn("JWT Token验证失败:", verifyResult.error);
        return { authType: "none", isAuthenticated: false };
      }

      // 解析token payload获取用户信息
      const payload = jwtTokenService.parseTokenPayload(token);
      if (!payload) {
        return { authType: "none", isAuthenticated: false };
      }

      // 构建用户上下文
      const user = {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
      };

      const session = {
        user,
        organization: payload.organizationId ? { id: payload.organizationId } : undefined,
      };

      return {
        user,
        session,
        authType: "jwt",
        isAuthenticated: true,
      };
    } catch (error) {
      console.error("JWT认证处理失败:", error);
      return { authType: "none", isAuthenticated: false };
    }
  }

  /**
   * 尝试Cookie认证
   * @param request Fastify请求对象
   * @returns Cookie认证结果
   */
  private static async tryCookieAuth(request: FastifyRequest): Promise<AuthContext> {
    try {
      // 使用Better Auth获取session
      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session || !session.user) {
        return { authType: "none", isAuthenticated: false };
      }

      return {
        user: session.user,
        session,
        authType: "cookie",
        isAuthenticated: true,
      };
    } catch (error) {
      console.error("Cookie认证处理失败:", error);
      return { authType: "none", isAuthenticated: false };
    }
  }

  /**
   * 要求认证的中间件
   * 如果用户未认证，返回401错误
   */
  static requireAuth() {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const authContext = await this.authenticate(request, reply);
      
      if (!authContext.isAuthenticated) {
        return reply.status(401).send({
          success: false,
          error: "未认证",
          message: "需要有效的认证信息（Cookie session或JWT token）",
        });
      }

      // 将认证上下文添加到请求对象
      (request as any).authContext = authContext;
    };
  }

  /**
   * 可选认证的中间件
   * 不要求认证，但会解析认证信息
   */
  static optionalAuth() {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const authContext = await this.authenticate(request, reply);
      
      // 将认证上下文添加到请求对象（即使未认证）
      (request as any).authContext = authContext;
    };
  }

  /**
   * 角色权限检查中间件
   * @param requiredRoles 所需角色列表
   */
  static requireRoles(requiredRoles: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const authContext = await this.authenticate(request, reply);
      
      if (!authContext.isAuthenticated) {
        return reply.status(401).send({
          success: false,
          error: "未认证",
          message: "需要有效的认证信息",
        });
      }

      const userRole = authContext.user?.role || "user";
      
      if (!requiredRoles.includes(userRole)) {
        return reply.status(403).send({
          success: false,
          error: "权限不足",
          message: `需要以下角色之一: ${requiredRoles.join(", ")}`,
          userRole,
          requiredRoles,
        });
      }

      // 将认证上下文添加到请求对象
      (request as any).authContext = authContext;
    };
  }

  /**
   * 组织权限检查中间件
   * @param requiredOrgRole 所需组织角色
   */
  static requireOrgRole(requiredOrgRole: string) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const authContext = await this.authenticate(request, reply);
      
      if (!authContext.isAuthenticated) {
        return reply.status(401).send({
          success: false,
          error: "未认证",
          message: "需要有效的认证信息",
        });
      }

      const orgRole = authContext.session?.organization?.role;
      
      if (!orgRole || orgRole !== requiredOrgRole) {
        return reply.status(403).send({
          success: false,
          error: "组织权限不足",
          message: `需要组织角色: ${requiredOrgRole}`,
          currentOrgRole: orgRole,
          requiredOrgRole,
        });
      }

      // 将认证上下文添加到请求对象
      (request as any).authContext = authContext;
    };
  }
}

// 扩展FastifyRequest类型以包含authContext
declare module "fastify" {
  interface FastifyRequest {
    authContext?: AuthContext;
  }
}