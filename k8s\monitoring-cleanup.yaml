apiVersion: v1
kind: ConfigMap
metadata:
  name: cleanup-monitoring-config
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: monitoring
data:
  # Prometheus 监控规则
  cleanup-rules.yaml: |
    groups:
    - name: specific-ai-auth-cleanup
      rules:
      
      # 清理任务失败告警
      - alert: DataCleanupJobFailed
        expr: kube_job_status_failed{job_name=~"specific-ai-auth-data-cleanup.*"} > 0
        for: 0m
        labels:
          severity: warning
          service: specific-ai-auth
          component: data-cleanup
        annotations:
          summary: "数据清理任务失败"
          description: "specific-ai-auth 数据清理任务 {{ $labels.job_name }} 执行失败"
          runbook_url: "https://docs/runbooks/data-cleanup-failure"
      
      # 清理任务超时告警
      - alert: DataCleanupJobTimeout
        expr: time() - kube_job_status_start_time{job_name=~"specific-ai-auth-data-cleanup.*"} > 1800
        for: 0m
        labels:
          severity: critical
          service: specific-ai-auth
          component: data-cleanup
        annotations:
          summary: "数据清理任务超时"
          description: "specific-ai-auth 数据清理任务 {{ $labels.job_name }} 运行超过30分钟"
          
      # 清理任务未按时运行告警
      - alert: DataCleanupJobMissing
        expr: time() - kube_job_status_completion_time{job_name=~"specific-ai-auth-data-cleanup.*"} > 90000
        for: 5m
        labels:
          severity: warning
          service: specific-ai-auth
          component: data-cleanup  
        annotations:
          summary: "数据清理任务未按时执行"
          description: "specific-ai-auth 数据清理任务超过25小时未执行，可能调度出现问题"
      
      # 数据库中过期记录过多告警
      - alert: TooManyExpiredRegistrations
        expr: |
          (
            auth_pending_registrations_total{status="expired"} +
            auth_pending_registrations_total{status="pending", expires_before="24h"}
          ) > 10000
        for: 10m
        labels:
          severity: warning
          service: specific-ai-auth
          component: database
        annotations:
          summary: "过期注册记录过多"
          description: "数据库中有 {{ $value }} 条过期注册记录，可能需要调整清理策略"
      
      # 数据库空间使用告警
      - alert: DatabaseDiskSpaceHigh
        expr: |
          (
            pg_database_size_bytes{datname="auth_db"} / 1024 / 1024 / 1024
          ) > 10
        for: 5m
        labels:
          severity: warning
          service: specific-ai-auth
          component: database
        annotations:
          summary: "数据库空间使用过高"
          description: "认证数据库大小已达到 {{ $value }}GB，建议检查数据清理策略"

  # Grafana 仪表板配置
  cleanup-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "specific-ai-auth 数据清理监控",
        "tags": ["specific-ai-auth", "cleanup", "database"],
        "timezone": "Asia/Shanghai",
        "panels": [
          {
            "id": 1,
            "title": "清理任务执行状态",
            "type": "stat",
            "targets": [
              {
                "expr": "kube_job_status_succeeded{job_name=~\"specific-ai-auth-data-cleanup.*\"}",
                "legendFormat": "成功任务数"
              },
              {
                "expr": "kube_job_status_failed{job_name=~\"specific-ai-auth-data-cleanup.*\"}",
                "legendFormat": "失败任务数"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "red", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "待清理数据量",
            "type": "graph",
            "targets": [
              {
                "expr": "auth_pending_registrations_total{status=\"expired\"}",
                "legendFormat": "过期注册记录"
              },
              {
                "expr": "auth_pending_registrations_total{status=\"pending\"}",
                "legendFormat": "待验证注册记录"
              },
              {
                "expr": "auth_verification_logs_total",
                "legendFormat": "验证日志记录"
              }
            ],
            "yAxes": [
              {
                "label": "记录数",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "清理任务执行时间",
            "type": "graph",
            "targets": [
              {
                "expr": "kube_job_status_completion_time{job_name=~\"specific-ai-auth-data-cleanup.*\"} - kube_job_status_start_time{job_name=~\"specific-ai-auth-data-cleanup.*\"}",
                "legendFormat": "执行时间 (秒)"
              }
            ],
            "yAxes": [
              {
                "label": "时间 (秒)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "数据库大小趋势",
            "type": "graph",
            "targets": [
              {
                "expr": "pg_database_size_bytes{datname=\"auth_db\"} / 1024 / 1024",
                "legendFormat": "认证数据库大小 (MB)"
              }
            ],
            "yAxes": [
              {
                "label": "大小 (MB)",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
          }
        ],
        "time": {
          "from": "now-7d",
          "to": "now"
        },
        "refresh": "1m"
      }
    }

---
apiVersion: v1
kind: Service
metadata:
  name: cleanup-metrics-service
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: cleanup-metrics
spec:
  selector:
    app: specific-ai-auth
    component: data-cleanup
  ports:
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  type: ClusterIP

---
# 清理性能监控服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cleanup-metrics-exporter
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: cleanup-metrics
spec:
  replicas: 1
  selector:
    matchLabels:
      app: specific-ai-auth
      component: cleanup-metrics
  template:
    metadata:
      labels:
        app: specific-ai-auth
        component: cleanup-metrics
    spec:
      containers:
      - name: metrics-exporter
        image: **************/specific-ai/specific-ai-auth:latest
        imagePullPolicy: Always
        
        command:
          - "node"
          - "/app/scripts/metrics-exporter.js"
        
        ports:
        - name: metrics
          containerPort: 9090
          protocol: TCP
        
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: DATABASE_URL
        - name: METRICS_PORT
          value: "9090"
        - name: METRICS_PATH
          value: "/metrics"
        - name: SCRAPE_INTERVAL
          value: "30"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 30
        
        readinessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 10
          periodSeconds: 10
        
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi

---
# AlertManager 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: cleanup-alertmanager-config
  namespace: ovs
  labels:
    app: specific-ai-auth
    component: alerting
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'localhost:587'
      smtp_from: '<EMAIL>'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
      - match:
          service: specific-ai-auth
          component: data-cleanup
        receiver: 'cleanup-alerts'
    
    receivers:
    - name: 'default'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Specific-AI-Auth] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          时间: {{ .StartsAt }}
          {{ end }}
    
    - name: 'cleanup-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: '[数据清理告警] {{ .GroupLabels.alertname }}'
        body: |
          数据清理系统告警通知:
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          详细描述: {{ .Annotations.description }}
          服务组件: {{ .Labels.service }}/{{ .Labels.component }}
          告警级别: {{ .Labels.severity }}
          开始时间: {{ .StartsAt }}
          
          相关标签:
          {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
          {{ end }}
          {{ end }}
          
          请及时检查数据清理任务状态和数据库健康状况。
      
      webhook_configs:
      - url: 'http://webhook-service:8080/alerts/cleanup'
        send_resolved: true