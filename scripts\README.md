# Scripts 目录说明

## 🚀 生产环境脚本

### K8s环境初始化
- **`k8s-init.ts`** - 统一的K8s环境初始化脚本（新增）
  - 集成数据库检查、迁移、健康检查功能
  - 支持自动化部署清理系统
  - 用法: `tsx scripts/k8s-init.ts [选项]`

- **`drizzle-k8s-init.js`** - 专用的Drizzle数据库初始化
  - K8s容器环境优化
  - 自动检查和执行数据库迁移

### 数据库管理
- **`check-and-init-db.js`** - 数据库初始化检查
  - 容器启动时自动检查表结构
  - 支持自动创建缺失的表和索引

- **`drizzle-migrate.sh`** - Drizzle迁移脚本
  - 完整的数据库迁移流程
  - 包含验证和内省功能

- **`init-database.sql`** - 基础数据库结构SQL

### 监控和运维
- **`health-check.js`** - 系统健康检查
  - 数据库连接检查
  - 关键表存在性验证
  - 用于K8s健康探针

- **`cleanup-data.ts`** - 数据清理服务
  - 自动清理过期数据
  - 支持批量处理和备份
  - 多种运行模式（干运行、仅过期数据等）

- **`metrics-exporter.ts`** - Prometheus指标导出器
  - 数据库指标收集
  - 清理任务状态监控
  - HTTP服务端点：/metrics, /health

- **`deploy-cleanup-system.sh`** - 清理系统K8s部署脚本
  - 自动化部署CronJob
  - 监控系统部署
  - 支持测试和状态检查

### 环境检查
- **`check-env.cjs`** - 环境变量验证
  - 检查必需的环境变量
  - 验证API密钥格式

### 测试
- **`run-tests.sh`** - 测试运行脚本
  - 多级别测试执行
  - CI/CD集成支持

## 🔧 使用指南

### K8s环境快速初始化
```bash
# 完整初始化（推荐）
tsx scripts/k8s-init.ts

# 包含清理系统部署
tsx scripts/k8s-init.ts --deploy-cleanup

# 详细输出
tsx scripts/k8s-init.ts --verbose

# 跳过确认提示
tsx scripts/k8s-init.ts --yes
```

### 数据库迁移
```bash
# 自动化迁移
bash scripts/drizzle-migrate.sh

# 或使用K8s专用版本
node scripts/drizzle-k8s-init.js
```

### 数据清理
```bash
# 查看帮助
tsx scripts/cleanup-data.ts --help

# 模拟运行
tsx scripts/cleanup-data.ts --dry-run --verbose

# 仅清理过期数据
tsx scripts/cleanup-data.ts --only-expired

# 健康检查
tsx scripts/cleanup-data.ts --health-check
```

### 监控指标
```bash
# 启动指标导出器
tsx scripts/metrics-exporter.ts

# 访问指标端点
curl http://localhost:9090/metrics
curl http://localhost:9090/health
```

### 清理系统部署
```bash
# 部署完整清理系统
bash scripts/deploy-cleanup-system.sh deploy

# 检查状态
bash scripts/deploy-cleanup-system.sh status

# 测试清理系统
bash scripts/deploy-cleanup-system.sh test

# 查看日志
bash scripts/deploy-cleanup-system.sh logs
```

## 📋 环境变量要求

### 必需变量
- `DATABASE_URL` - PostgreSQL连接字符串
- `BETTER_AUTH_SECRET` - 认证密钥
- `BETTER_AUTH_URL` - 服务基础URL

### 可选变量
- `K8S_NAMESPACE` - K8s命名空间（默认: ovs）
- `METRICS_PORT` - 指标服务端口（默认: 9090）
- `CLEANUP_BATCH_SIZE` - 清理批次大小
- `RESEND_API_KEY` - 邮件服务API密钥

## 🗑️ 已清理的脚本

以下开发测试脚本已被移除：
- `demo-email-service.cjs`
- `test-*.js/cjs` 系列脚本
- `diagnose-*.js` 诊断脚本
- `jwt-examples.cjs`
- `validate-coverage.js`
- 冗余的迁移脚本

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
- name: Initialize K8s Environment
  run: tsx scripts/k8s-init.ts --yes --verbose

- name: Deploy Cleanup System
  run: bash scripts/deploy-cleanup-system.sh deploy --dry-run

- name: Health Check
  run: node scripts/health-check.js
```

### Docker容器启动
```dockerfile
# 在容器启动时初始化数据库
RUN node scripts/check-and-init-db.js

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
  CMD node scripts/health-check.js
```

## 🛠️ 故障排除

### 常见问题
1. **数据库连接失败** - 检查 `DATABASE_URL` 格式
2. **迁移失败** - 确保数据库权限正确
3. **清理任务异常** - 查看 `cleanup-data.ts --health-check`
4. **指标导出错误** - 检查端口占用情况

### 日志查看
```bash
# K8s环境日志
kubectl logs -l app=specific-ai-auth -n ovs

# 清理任务日志
bash scripts/deploy-cleanup-system.sh logs
```