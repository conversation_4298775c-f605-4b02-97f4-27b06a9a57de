#!/usr/bin/env tsx
/**
 * 数据清理命令行工具
 * 可以手动执行数据清理任务或在定时任务中调用
 */

import { dataCleanupService } from "../src/services/data-cleanup.js";
import type { DataCleanupService } from "../src/services/data-cleanup.js";
import { db } from "../src/lib/drizzle.js";
import { sql } from "drizzle-orm";

// 命令行参数接口
interface CleanupOptions {
  dryRun: boolean;
  verbose: boolean;
  force: boolean;
  configFile?: string;
  skipBackup: boolean;
  batchSize?: number;
  onlyExpired: boolean;
  healthCheck: boolean;
}

/**
 * 解析命令行参数
 */
function parseArgs(): CleanupOptions {
  const args = process.argv.slice(2);
  const options: CleanupOptions = {
    dryRun: false,
    verbose: false,
    force: false,
    skipBackup: false,
    onlyExpired: false,
    healthCheck: false,
  };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--force':
        options.force = true;
        break;
      case '--skip-backup':
        options.skipBackup = true;
        break;
      case '--only-expired':
        options.onlyExpired = true;
        break;
      case '--health-check':
        options.healthCheck = true;
        break;
      case '--config':
        options.configFile = args[++i];
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      default:
        console.error(`未知参数: ${arg}`);
        showHelp();
        process.exit(1);
    }
  }
  
  return options;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
数据清理工具 - specific-ai-auth

用法: tsx scripts/cleanup-data.ts [选项]

选项:
  --dry-run           仅模拟运行，不实际删除数据
  --verbose, -v       详细输出
  --force             强制执行，跳过确认
  --skip-backup       跳过备份创建
  --only-expired      仅清理过期记录
  --health-check      执行健康检查
  --config <file>     指定配置文件
  --batch-size <num>  指定批处理大小
  --help, -h          显示此帮助信息

示例:
  # 执行完整清理
  tsx scripts/cleanup-data.ts

  # 仅检查健康状态
  tsx scripts/cleanup-data.ts --health-check

  # 模拟运行
  tsx scripts/cleanup-data.ts --dry-run --verbose

  # 仅清理过期记录
  tsx scripts/cleanup-data.ts --only-expired --force

  # 自定义批处理大小
  tsx scripts/cleanup-data.ts --batch-size 500

环境变量:
  CLEANUP_CONFIG_PATH  配置文件路径
  CLEANUP_DRY_RUN     设为 'true' 启用模拟运行
  CLEANUP_VERBOSE     设为 'true' 启用详细输出
`);
}

/**
 * 加载配置文件
 */
async function loadConfig(configFile?: string): Promise<Record<string, any>> {
  const configPath = configFile || process.env.CLEANUP_CONFIG_PATH;
  
  if (!configPath) {
    return {};
  }
  
  try {
    const { default: config } = await import(configPath);
    return config;
  } catch (error) {
    console.warn(`无法加载配置文件 ${configPath}:`, error);
    return {};
  }
}

/**
 * 执行健康检查
 */
async function performHealthCheck(verbose: boolean): Promise<boolean> {
  console.log("正在执行数据库健康检查...");
  
  try {
    const healthResult = await dataCleanupService.healthCheck();
    
    console.log(`\n=== 健康检查结果 ===`);
    console.log(`状态: ${healthResult.healthy ? '✅ 健康' : '❌ 异常'}`);
    
    console.log(`\n数据库指标:`);
    console.log(`  待处理注册记录: ${healthResult.metrics.pendingRegistrationCount}`);
    console.log(`  过期注册记录: ${healthResult.metrics.expiredRegistrationCount}`);
    console.log(`  验证日志记录: ${healthResult.metrics.verificationLogCount}`);
    console.log(`  邮件日志记录: ${healthResult.metrics.emailLogCount}`);
    
    if (healthResult.issues.length > 0) {
      console.log(`\n发现的问题:`);
      healthResult.issues.forEach(issue => {
        console.log(`  ⚠️  ${issue}`);
      });
    }
    
    if (verbose) {
      const stats = dataCleanupService.getStats();
      console.log(`\n清理统计:`);
      console.log(`  上次清理: ${stats.lastCleanupAt.toISOString()}`);
      console.log(`  总清理次数: ${stats.totalCleanupsRun}`);
      console.log(`  总清理记录数: ${stats.totalRecordsCleaned}`);
      console.log(`  平均处理时间: ${Math.round(stats.averageProcessingTimeMs)}ms`);
      console.log(`  成功率: ${(stats.successRate * 100).toFixed(2)}%`);
      
      if (stats.recentErrors.length > 0) {
        console.log(`\n最近错误:`);
        stats.recentErrors.forEach(error => {
          console.log(`  ${error.timestamp.toISOString()}: ${error.error}`);
        });
      }
    }
    
    return healthResult.healthy;
    
  } catch (error) {
    console.error("健康检查失败:", error);
    return false;
  }
}

/**
 * 确认用户操作
 */
async function confirmAction(message: string): Promise<boolean> {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    rl.question(`${message} (y/N): `, (answer: string) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * 模拟运行清理
 */
async function performDryRun(verbose: boolean, onlyExpired: boolean): Promise<void> {
  console.log("🔍 模拟运行模式 - 不会实际删除数据\n");
  
  try {
    // 这里模拟查询要清理的数据量
    const queries = [
      {
        name: "过期的临时注册记录",
        query: `
          SELECT COUNT(*) as count 
          FROM auth.pending_registration 
          WHERE expires_at < NOW() - INTERVAL '7 days' 
            AND status IN ('pending', 'expired')
        `,
      },
      ...(onlyExpired ? [] : [
        {
          name: "旧的已完成注册记录",
          query: `
            SELECT COUNT(*) as count 
            FROM auth.pending_registration 
            WHERE status = 'verified' 
              AND verified_at < NOW() - INTERVAL '30 days'
          `,
        },
        {
          name: "旧的失败注册记录",
          query: `
            SELECT COUNT(*) as count 
            FROM auth.pending_registration 
            WHERE status = 'failed' 
              AND updated_at < NOW() - INTERVAL '14 days'
          `,
        },
        {
          name: "孤立的验证日志",
          query: `
            SELECT COUNT(*) as count 
            FROM auth.registration_verification_log 
            WHERE pending_registration_id NOT IN (SELECT id FROM auth.pending_registration)
              AND created_at < NOW() - INTERVAL '30 days'
          `,
        },
        {
          name: "旧的邮件日志",
          query: `SELECT COUNT(*) as count FROM auth.email_log WHERE created_at < NOW() - INTERVAL '60 days'`,
        },
        {
          name: "旧的频率限制记录",
          query: `
            SELECT COUNT(*) as count 
            FROM auth.rate_limiter 
            WHERE created_at < NOW() - INTERVAL '7 days' AND blocked = false
          `,
        },
      ]),
    ];
    
    let totalToDelete = 0;
    
    for (const { name, query } of queries) {
      try {
        const result = await db.execute(sql.raw(query));
        const count = Number((result as any).rows[0]?.count || 0);
        totalToDelete += count;
        
        console.log(`${name}: ${count} 条记录`);
        
        if (verbose && count > 0) {
          console.log(`  执行的查询: ${query.trim().replace(/\s+/g, ' ')}`);
        }
      } catch (error) {
        console.error(`查询 ${name} 失败:`, error);
      }
    }
    
    console.log(`\n总计将清理: ${totalToDelete} 条记录`);
    
    if (totalToDelete === 0) {
      console.log("✅ 没有需要清理的数据");
    } else {
      console.log(`\n要执行实际清理，请运行:`);
      console.log(`tsx scripts/cleanup-data.ts${onlyExpired ? ' --only-expired' : ''}`);
    }
    
  } catch (error) {
    console.error("模拟运行失败:", error);
    process.exit(1);
  }
}

/**
 * 执行实际清理
 */
async function performCleanup(
  service: DataCleanupService,
  verbose: boolean,
  skipBackup: boolean,
  onlyExpired: boolean
): Promise<void> {
  console.log("开始执行数据清理...\n");
  
  // 更新服务配置
  if (skipBackup) {
    service.updateConfig({ backupBeforeCleanup: false });
  }
  
  const startTime = Date.now();
  
  try {
    let result;
    
    if (onlyExpired) {
      // 仅清理过期记录的简化版本
      console.log("仅清理过期的临时注册记录...");
      const expiredCount = await (service as any).cleanupExpiredPendingRegistrations();
      
      result = {
        success: true,
        totalProcessed: expiredCount,
        recordsCleaned: {
          expiredPendingRegistrations: expiredCount,
          oldCompletedRegistrations: 0,
          oldFailedRegistrations: 0,
          orphanedVerificationLogs: 0,
          oldEmailLogs: 0,
          oldRateLimiterRecords: 0,
          oldBetterAuthVerifications: 0,
        },
        processingTimeMs: Date.now() - startTime,
        errors: [],
        warnings: [],
      };
    } else {
      // 执行完整清理
      result = await service.runCleanup();
    }
    
    // 输出结果
    console.log(`\n=== 清理完成 ===`);
    console.log(`状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`处理时间: ${result.processingTimeMs}ms`);
    console.log(`总处理记录数: ${result.totalProcessed}`);
    
    if (verbose || result.totalProcessed > 0) {
      console.log(`\n清理详情:`);
      console.log(`  过期临时注册记录: ${result.recordsCleaned.expiredPendingRegistrations}`);
      if (!onlyExpired) {
        console.log(`  旧的已完成注册记录: ${result.recordsCleaned.oldCompletedRegistrations}`);
        console.log(`  旧的失败注册记录: ${result.recordsCleaned.oldFailedRegistrations}`);
        console.log(`  孤立验证日志: ${result.recordsCleaned.orphanedVerificationLogs}`);
        console.log(`  旧的邮件日志: ${result.recordsCleaned.oldEmailLogs}`);
        console.log(`  旧的频率限制记录: ${result.recordsCleaned.oldRateLimiterRecords}`);
        console.log(`  Better Auth 旧验证记录: ${result.recordsCleaned.oldBetterAuthVerifications}`);
      }
    }
    
    if (result.backupInfo) {
      console.log(`\n备份信息:`);
      console.log(`  备份状态: ${result.backupInfo.created ? '✅ 已创建' : '❌ 失败'}`);
      if (result.backupInfo.created) {
        console.log(`  备份位置: ${result.backupInfo.location}`);
        console.log(`  备份记录数: ${result.backupInfo.recordCount}`);
      }
    }
    
    if (result.warnings.length > 0) {
      console.log(`\n⚠️  警告:`);
      result.warnings.forEach(warning => {
        console.log(`  ${warning}`);
      });
    }
    
    if (result.errors.length > 0) {
      console.log(`\n❌ 错误:`);
      result.errors.forEach(error => {
        console.log(`  ${error}`);
      });
    }
    
    if (!result.success) {
      process.exit(1);
    }
    
  } catch (error) {
    console.error("清理过程中发生错误:", error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
async function main() {
  const options = parseArgs();
  
  // 从环境变量覆盖选项
  if (process.env.CLEANUP_DRY_RUN === 'true') {
    options.dryRun = true;
  }
  if (process.env.CLEANUP_VERBOSE === 'true') {
    options.verbose = true;
  }
  
  console.log("🧹 specific-ai-auth 数据清理工具\n");
  
  try {
    // 加载配置
    if (options.configFile || process.env.CLEANUP_CONFIG_PATH) {
      const config = await loadConfig(options.configFile);
      if (Object.keys(config).length > 0) {
        dataCleanupService.updateConfig(config);
        if (options.verbose) {
          console.log("已加载自定义配置");
        }
      }
    }
    
    // 应用命令行配置覆盖
    if (options.batchSize) {
      dataCleanupService.updateConfig({ batchSize: options.batchSize });
    }
    
    // 执行健康检查
    if (options.healthCheck) {
      const healthy = await performHealthCheck(options.verbose);
      process.exit(healthy ? 0 : 1);
    }
    
    // 执行模拟运行
    if (options.dryRun) {
      await performDryRun(options.verbose, options.onlyExpired);
      return;
    }
    
    // 确认执行（除非强制模式）
    if (!options.force) {
      const confirmed = await confirmAction(
        options.onlyExpired 
          ? "确定要清理过期的临时注册记录吗？"
          : "确定要执行数据清理吗？这将删除过期和旧的数据记录。"
      );
      
      if (!confirmed) {
        console.log("操作已取消");
        return;
      }
    }
    
    // 执行清理
    await performCleanup(dataCleanupService, options.verbose, options.skipBackup, options.onlyExpired);
    
  } catch (error) {
    console.error("执行失败:", error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n收到中断信号，正在退出...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n收到终止信号，正在退出...');
  process.exit(0);
});

// 执行主函数
// 检查是否直接运行此脚本
if (typeof import.meta !== 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error("执行失败:", error);
    process.exit(1);
  });
}