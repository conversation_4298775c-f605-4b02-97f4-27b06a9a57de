import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, jest } from '@jest/globals';
import { EmailService } from '../src/services/email-service.js';
import { EmailTemplateManager } from '../src/services/email-templates.js';
import { inviteCodeContextManager } from '../src/services/invite-code-context.js';
import { EmailSendResult } from '../src/types/email.js';

// 模拟数据库
jest.mock('../src/lib/drizzle.js', () => ({
  db: {
    insert: jest.fn().mockReturnValue({
      values: jest.fn().mockResolvedValue({ rowCount: 1 })
    }),
    update: jest.fn().mockReturnValue({
      set: jest.fn().mockReturnValue({
        where: jest.fn().mockResolvedValue({ rowCount: 1 })
      })
    }),
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([])
        })
      })
    })
  }
}));

// 模拟Resend邮件服务
jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => ({
    emails: {
      send: jest.fn().mockResolvedValue({
        data: { id: 'mock-email-id-123' },
        error: null
      })
    }
  }))
}));

describe('邮箱注册流程测试', () => {
  let emailService: EmailService;
  let templateManager: EmailTemplateManager;

  beforeAll(() => {
    // 设置测试用的环境变量
    process.env.RESEND_API_KEY = 'test-api-key';
  });

  beforeEach(() => {
    // 每个测试前重新创建服务实例
    emailService = new EmailService({
      apiKey: 'test-api-key',
      fromEmail: '<EMAIL>',
      fromName: 'Test Service',
      defaultVersion: 'v1',
      replyTo: '<EMAIL>'
    });
    
    templateManager = new EmailTemplateManager();
    
    // 清理邀请码上下文
    jest.clearAllMocks();
  });

  afterEach(() => {
    // 清理邀请码上下文
    inviteCodeContextManager.cleanup();
  });

  describe('邮件发送功能', () => {
    it('应该成功发送验证邮件', async () => {
      const result = await emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123',
        version: 'v1'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock-email-id-123');
      expect(result.error).toBeUndefined();
    });

    it('应该成功发送带邀请码的验证邮件', async () => {
      const result = await emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123',
        inviteCode: 'TESTCODE2025',
        version: 'v1'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock-email-id-123');
    });

    it('应该处理邮件发送失败的情况', async () => {
      // 模拟邮件发送失败
      const mockResend = require('resend').Resend;
      const mockInstance = new mockResend();
      mockInstance.emails.send.mockResolvedValueOnce({
        data: null,
        error: { message: '邮件服务不可用' }
      });

      const result = await emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('邮件发送失败');
    });

    it('应该处理无效邮箱地址', async () => {
      // 模拟无效邮箱错误
      const mockResend = require('resend').Resend;
      const mockInstance = new mockResend();
      mockInstance.emails.send.mockResolvedValueOnce({
        data: null,
        error: { message: 'Invalid email address' }
      });

      const result = await emailService.sendVerificationEmail({
        userEmail: 'invalid-email',
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('邮件发送失败');
    });
  });

  describe('邮件模板系统', () => {
    it('应该正确渲染v1版本验证邮件模板', () => {
      const template = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          userName: '测试用户',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      expect(template.subject).toContain('邮箱验证');
      expect(template.html).toContain('测试用户');
      expect(template.html).toContain('<EMAIL>');
      expect(template.html).toContain('https://example.com/verify?token=test123');
      expect(template.text).toBeTruthy();
    });

    it('应该正确渲染v2版本验证邮件模板', () => {
      const template = templateManager.render({
        type: 'verification',
        version: 'v2',
        data: {
          userEmail: '<EMAIL>',
          userName: '测试用户',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      expect(template.subject).toContain('邮箱验证');
      expect(template.html).toContain('测试用户');
      expect(template.html).toContain('<EMAIL>');
      expect(template.html).toContain('https://example.com/verify?token=test123');
      expect(template.text).toBeTruthy();
    });

    it('应该在模板中包含邀请码信息', () => {
      const template = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          userName: '测试用户',
          verificationUrl: 'https://example.com/verify?token=test123',
          inviteCode: 'TESTCODE2025'
        }
      });

      expect(template.html).toContain('TESTCODE2025');
      expect(template.text).toContain('TESTCODE2025');
    });

    it('应该处理缺少用户名的情况', () => {
      const template = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      expect(template.subject).toBeTruthy();
      expect(template.html).toBeTruthy();
      expect(template.text).toBeTruthy();
      // 应该有合理的默认显示
      expect(template.html).toContain('<EMAIL>');
    });

    it('应该检查模板版本可用性', () => {
      expect(templateManager.isVersionAvailable('v1')).toBe(true);
      expect(templateManager.isVersionAvailable('v2')).toBe(true);
      expect(templateManager.isVersionAvailable('v999' as any)).toBe(false);
    });

    it('应该返回可用版本列表', () => {
      const versions = templateManager.getAvailableVersions();
      
      expect(versions).toContain('v1');
      expect(versions).toContain('v2');
      expect(versions.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('邀请码上下文管理', () => {
    it('应该正确设置和获取邀请码上下文', () => {
      const email = '<EMAIL>';
      const inviteCode = 'TESTCODE123';

      inviteCodeContextManager.setContext(email, inviteCode);
      
      const retrievedCode = inviteCodeContextManager.getContext(email);
      expect(retrievedCode).toBe(inviteCode);
    });

    it('应该在邮件发送时自动设置邀请码上下文', async () => {
      const email = '<EMAIL>';
      const inviteCode = 'TESTCODE123';

      await emailService.sendVerificationEmail({
        userEmail: email,
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123',
        inviteCode: inviteCode
      });

      // 验证上下文已设置（这个行为依赖于实际实现）
      // 注意：这里需要根据实际的邮件服务实现来调整
      expect(true).toBe(true); // 占位符，实际测试需要根据实现调整
    });

    it('应该正确清理邀请码上下文', () => {
      const email = '<EMAIL>';
      const inviteCode = 'TESTCODE123';

      inviteCodeContextManager.setContext(email, inviteCode);
      expect(inviteCodeContextManager.getContext(email)).toBe(inviteCode);

      inviteCodeContextManager.cleanupContext(email);
      expect(inviteCodeContextManager.getContext(email)).toBeNull();
    });

    it('应该返回正确的上下文计数', () => {
      const initialCount = inviteCodeContextManager.getContextCount();

      inviteCodeContextManager.setContext('<EMAIL>', 'CODE1');
      inviteCodeContextManager.setContext('<EMAIL>', 'CODE2');

      expect(inviteCodeContextManager.getContextCount()).toBe(initialCount + 2);

      inviteCodeContextManager.cleanupContext('<EMAIL>');
      expect(inviteCodeContextManager.getContextCount()).toBe(initialCount + 1);
    });

    it('应该处理不存在的邮箱地址', () => {
      const result = inviteCodeContextManager.getContext('<EMAIL>');
      expect(result).toBeNull();
    });
  });

  describe('错误处理和边界条件', () => {
    it('应该处理空的验证URL', async () => {
      const result = await emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: ''
      });

      // 根据实际实现，这可能成功（使用默认URL）或失败
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });

    it('应该处理特殊字符的用户名', () => {
      const template = templateManager.render({
        type: 'verification',
        version: 'v1',
        data: {
          userEmail: '<EMAIL>',
          userName: '测试用户<script>alert("xss")</script>',
          verificationUrl: 'https://example.com/verify?token=test123'
        }
      });

      // 应该对特殊字符进行转义
      expect(template.html).not.toContain('<script>');
      expect(template.html).toContain('测试用户');
    });

    it('应该处理长邮箱地址', async () => {
      const longEmail = 'a'.repeat(100) + '@example.com';
      
      const result = await emailService.sendVerificationEmail({
        userEmail: longEmail,
        userName: '测试用户',
        verificationUrl: 'https://example.com/verify?token=test123'
      });

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });

    it('应该处理非常长的验证URL', async () => {
      const longUrl = 'https://example.com/verify?token=' + 'a'.repeat(1000);
      
      const result = await emailService.sendVerificationEmail({
        userEmail: '<EMAIL>',
        userName: '测试用户',
        verificationUrl: longUrl
      });

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });
  });

  describe('配置管理', () => {
    it('应该返回正确的配置信息', () => {
      const config = emailService.getConfig();

      expect(config.fromEmail).toBe('<EMAIL>');
      expect(config.fromName).toBe('Test Service');
      expect(config.defaultVersion).toBe('v1');
      expect(config.replyTo).toBe('<EMAIL>');
      // API key 不应该在配置中暴露
      expect((config as any).apiKey).toBeUndefined();
    });

    it('应该允许切换默认模板版本', () => {
      expect(emailService.getConfig().defaultVersion).toBe('v1');

      emailService.setDefaultVersion('v2');
      expect(emailService.getConfig().defaultVersion).toBe('v2');

      emailService.setDefaultVersion('v1');
      expect(emailService.getConfig().defaultVersion).toBe('v1');
    });

    it('应该拒绝不支持的模板版本', () => {
      expect(() => {
        emailService.setDefaultVersion('v999' as any);
      }).toThrow('不支持的模板版本');
    });

    it('应该返回可用的模板版本列表', () => {
      const versions = emailService.getAvailableVersions();
      
      expect(Array.isArray(versions)).toBe(true);
      expect(versions.length).toBeGreaterThan(0);
      expect(versions).toContain('v1');
      expect(versions).toContain('v2');
    });
  });

  describe('性能测试', () => {
    it('模板渲染应该在合理时间内完成', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 100; i++) {
        templateManager.render({
          type: 'verification',
          version: i % 2 === 0 ? 'v1' : 'v2',
          data: {
            userEmail: `test${i}@example.com`,
            userName: `测试用户${i}`,
            verificationUrl: `https://example.com/verify?token=test${i}`,
            inviteCode: `CODE${i}`
          }
        });
      }
      
      const duration = Date.now() - startTime;
      const avgTime = duration / 100;
      
      // 平均每次渲染应该在10ms以内
      expect(avgTime).toBeLessThan(10);
    });

    it('邀请码上下文管理应该能处理大量数据', () => {
      const testCount = 1000;
      const startTime = Date.now();
      
      // 添加大量上下文
      for (let i = 0; i < testCount; i++) {
        inviteCodeContextManager.setContext(`test${i}@example.com`, `CODE${i}`);
      }
      
      // 验证所有数据
      for (let i = 0; i < testCount; i++) {
        const code = inviteCodeContextManager.getContext(`test${i}@example.com`);
        expect(code).toBe(`CODE${i}`);
      }
      
      // 清理所有数据
      for (let i = 0; i < testCount; i++) {
        inviteCodeContextManager.cleanupContext(`test${i}@example.com`);
      }
      
      const duration = Date.now() - startTime;
      
      // 整个过程应该在1秒内完成
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('集成测试场景', () => {
    it('应该完成完整的验证邮件发送流程', async () => {
      const email = '<EMAIL>';
      const userName = '集成测试用户';
      const inviteCode = 'INTEGRATION123';
      const verificationUrl = 'https://example.com/verify?token=integration-test';

      // 1. 设置邀请码上下文
      inviteCodeContextManager.setContext(email, inviteCode);

      // 2. 发送验证邮件
      const result = await emailService.sendVerificationEmail({
        userEmail: email,
        userName: userName,
        verificationUrl: verificationUrl,
        inviteCode: inviteCode,
        version: 'v2'
      });

      // 3. 验证结果
      expect(result.success).toBe(true);
      expect(result.messageId).toBeTruthy();

      // 4. 验证邀请码上下文仍然存在
      const contextCode = inviteCodeContextManager.getContext(email);
      expect(contextCode).toBe(inviteCode);

      // 5. 清理
      inviteCodeContextManager.cleanupContext(email);
    });

    it('应该处理并发邮件发送请求', async () => {
      const promises: Promise<EmailSendResult>[] = [];
      const concurrentCount = 5;

      for (let i = 0; i < concurrentCount; i++) {
        promises.push(
          emailService.sendVerificationEmail({
            userEmail: `concurrent${i}@example.com`,
            userName: `并发用户${i}`,
            verificationUrl: `https://example.com/verify?token=concurrent${i}`,
            inviteCode: `CONCURRENT${i}`
          })
        );
      }

      const results = await Promise.all(promises);
      
      // 所有邮件都应该发送成功
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.messageId).toBeTruthy();
      });
    });
  });
});