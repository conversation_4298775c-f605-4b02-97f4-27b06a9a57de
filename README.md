# Better Auth 认证中间服务

基于 **Fastify + Better Auth v1.2.12** 的最简邮箱登录注册认证中间服务，提供独立的Docker容器化认证解决方案。

## 🎯 项目概述

本项目实现了一个轻量级、高性能的认证中间服务，专门用于处理用户邮箱登录注册功能。采用现代化技术栈，提供自动生成的API文档和完整的类型安全保障。

### 核心特性

- ✅ **邮箱 + 密码认证** - 安全的用户注册和登录
- ✅ **自动API文档生成** - Fastify自动生成OpenAPI 3.0规范文档
- ✅ **完整类型安全** - TypeScript端到端类型保护
- ✅ **内置安全特性** - 密码哈希、限流、CSRF保护、会话管理
- ✅ **Docker容器化** - 独立部署，易于扩展
- ✅ **高性能** - Fastify提供比Express.js快3倍的性能

## 🏗️ 技术栈

### 后端框架

- **Fastify 4.x** - 高性能Web框架，自动OpenAPI文档生成
- **Better Auth v1.2.12** - 现代化TypeScript认证库
- **TypeScript 5.x** - 类型安全的开发体验
- **Node.js 18+** - LTS运行时环境

### 数据库

- **PostgreSQL 14+** - 企业级关系型数据库
- **pg** - PostgreSQL官方Node.js驱动

### 开发工具

- **pnpm** - 现代化包管理器
- **Docker** - 容器化部署
- **tsx** - 现代TypeScript执行器

## 🚀 快速开始

### 1. 环境要求

```bash
# Node.js版本要求
node --version  # >= 18.0.0

# pnpm包管理器
npm install -g pnpm
```

### 2. 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

必需的环境变量：

```bash
# 数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/auth_db"

# Better Auth配置
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="http://localhost:3001"

# 服务配置
PORT=3001
NODE_ENV=development
```

### 3. 安装依赖

```bash
pnpm install
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
pnpm db:migrate

# (可选) 填充测试数据
pnpm db:seed
```

### 5. 启动开发服务器

```bash
# 开发模式
pnpm dev

# 生产模式
pnpm start
```

服务将在 `http://localhost:3001` 启动

### 6. 查看API文档

访问 `http://localhost:3001/docs` 查看自动生成的API文档

## 📋 API端点

### 认证相关

- `POST /api/auth/sign-up/email` - 用户注册
- `POST /api/auth/sign-in/email` - 用户登录
- `POST /api/auth/sign-out` - 用户登出
- `GET /api/auth/session` - 获取当前会话
- `GET /api/auth/ok` - 健康检查

### 系统监控

- `GET /health` - 服务健康检查
- `GET /metrics` - 服务指标（生产环境）

## 🐳 Docker部署

### 构建镜像

```bash
# 构建Docker镜像
docker build -t better-auth-service .

# 使用Docker Compose
docker-compose up -d
```

### Docker Compose配置

```yaml
version: "3.8"
services:
  auth-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=**************************************/auth_db
      - BETTER_AUTH_SECRET=your-secret-key
    depends_on:
      - db

  db:
    image: postgres:14
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔧 开发指南

### 项目结构

```
better-auth-service/
├── src/
│   ├── app.ts              # Fastify应用程序入口
│   ├── auth.ts             # Better Auth配置
│   ├── server.ts           # 服务器启动文件
│   ├── routes/             # 路由定义
│   │   ├── auth.ts         # 认证路由
│   │   └── health.ts       # 健康检查路由
│   ├── middleware/         # 中间件
│   │   ├── cors.ts         # CORS配置
│   │   ├── security.ts     # 安全中间件
│   │   └── logging.ts      # 日志中间件
│   ├── types/              # TypeScript类型定义
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── prisma/                 # 数据库Schema
├── docker/                 # Docker配置
├── tests/                  # 测试文件
├── docs/                   # 项目文档
└── dist/                   # 编译输出
```

### 添加新路由

```typescript
// src/routes/example.ts
import type { FastifyPluginAsync } from "fastify";

const exampleRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get(
    "/example",
    {
      schema: {
        description: "示例端点",
        tags: ["example"],
        response: {
          200: {
            type: "object",
            properties: {
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (request, reply) => {
      return { message: "Hello World" };
    },
  );
};

export default exampleRoutes;
```

## 🧪 测试

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行端到端测试
pnpm test:e2e
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请创建Issue或联系维护团队。

---

**关键优势总结：**

- 🚀 **性能优先** - Fastify高性能Web框架
- 📚 **文档自动化** - 无需手动维护API文档
- 🔒 **安全第一** - Better Auth内置安全最佳实践
- 🐳 **部署简单** - Docker容器化，一键部署
- 💻 **开发友好** - TypeScript类型安全，现代化工具链
