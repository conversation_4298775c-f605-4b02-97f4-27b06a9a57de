apiVersion: v1
kind: Secret
metadata:
  name: specific-ai-auth-secret
  namespace: ovs
  labels:
    app: specific-ai-auth
    app.kubernetes.io/name: specific-ai-auth
    app.kubernetes.io/component: service
    environment: prod
type: Opaque
data:
  # 生产环境数据库连接配置 (base64编码)
  # 原值: **************************************************/auth_db_prod
  DATABASE_URL: ************************************************************************************
  
  # 生产环境认证密钥 (base64编码)
  # 原值: SpecificAI2025Prod!@#$%
  BETTER_AUTH_SECRET: U3BlY2lmaWNBSTIwMjVQcm9kIUAjJCU=
  
  # 生产环境邮件服务API密钥 (base64编码)
  # 原值: re_prod_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  RESEND_API_KEY: cmVfcHJvZF94eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4
  
  # 生产环境Google OAuth密钥 (base64编码)
  # 原值: your-prod-google-client-secret
  GOOGLE_CLIENT_SECRET: eW91ci1wcm9kLWdvb2dsZS1jbGllbnQtc2VjcmV0
  
  # 生产环境JWT密钥 (base64编码)
  # 原值: SpecificAI2025-v1.0-PlanS
  JWT_SECRET: U3BlY2lmaWNBMjAyNS12MS4wLVBsYW5T