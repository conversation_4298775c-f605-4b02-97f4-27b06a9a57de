# 本地测试指南

## 🧪 测试环境配置

### 1. 确保服务正常启动

```bash
# 启动认证服务
pnpm dev

# 服务应运行在 http://localhost:10086
# 前端应运行在 http://localhost:3000
```

### 2. 环境变量配置

确保 `.env` 文件包含以下配置：

```bash
DATABASE_URL=**********************************************************
BETTER_AUTH_SECRET=SpecificAI2025
BETTER_AUTH_URL=http://localhost:10086
FRONTEND_URL=http://localhost:3000
RESEND_API_KEY=re_vR8DZX4k_6QxFuoTZ1ETCs7hviuwtgJ9H
```

## 📧 邮箱注册功能测试

### 步骤1：注册新用户

```bash
curl -X POST http://localhost:10086/api/auth/sign-up/email-invite \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "name": "Test User",
    "company_name": "Test Company",
    "invite_code": "GO2025",
    "language": "chinese"
  }'
```

**预期结果**：
- 返回成功消息
- 用户收到验证邮件
- 邮件链接指向：`http://localhost:3000/verify-email?token=xxx`

### 步骤2：检查邮件内容

邮件应包含：
- 欢迎消息
- 验证按钮指向前端地址
- 邀请码信息（如果有）

### 步骤3：前端验证页面

前端需要创建 `/verify-email` 页面：

```typescript
// pages/verify-email.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function VerifyEmail() {
  const router = useRouter();
  const { token } = router.query;
  const [status, setStatus] = useState('verifying');

  useEffect(() => {
    if (token) {
      verifyEmail(token as string);
    }
  }, [token]);

  const verifyEmail = async (token: string) => {
    try {
      const response = await fetch(`http://localhost:10086/api/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setStatus('success');
        // 重定向到登录页面或首页
        setTimeout(() => {
          router.push('/login?verified=true');
        }, 2000);
      } else {
        setStatus('error');
      }
    } catch (error) {
      setStatus('error');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md">
        {status === 'verifying' && (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">验证邮箱</h2>
            <p>正在验证您的邮箱地址...</p>
          </div>
        )}
        
        {status === 'success' && (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-green-600">验证成功</h2>
            <p>您的邮箱已成功验证，即将跳转到登录页面...</p>
          </div>
        )}
        
        {status === 'error' && (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-red-600">验证失败</h2>
            <p>邮箱验证失败，请检查链接是否正确或已过期。</p>
            <button 
              onClick={() => router.push('/register')}
              className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
            >
              重新注册
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
```

## 🔑 JWT认证功能测试

### 步骤1：用户登录

```bash
curl -X POST http://localhost:10086/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }' \
  -c cookies.txt
```

### 步骤2：创建JWT Token

```bash
curl -X POST http://localhost:10086/api/auth/jwt/create \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "expiresIn": "1h",
    "customClaims": {
      "service": "test-client",
      "features": ["chat", "notifications"]
    }
  }'
```

**预期响应**：
```json
{
  "success": true,
  "token": "eyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600,
  "tokenType": "Bearer"
}
```

### 步骤3：验证JWT Token

```bash
curl -X POST http://localhost:10086/api/auth/jwt/verify \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_JWT_TOKEN_HERE"
  }'
```

### 步骤4：使用JWT Token调用API

```bash
curl -X GET http://localhost:10086/api/auth/jwt/info \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## 🧪 前端JWT集成测试

### 1. 创建JWT客户端

```typescript
// utils/jwt-client.ts
class JWTClient {
  private baseURL = 'http://localhost:10086';
  
  async login(email: string, password: string): Promise<string> {
    // 1. 登录获取Cookie Session
    const loginResponse = await fetch(`${this.baseURL}/api/auth/sign-in/email`, {
      method: 'POST',
      credentials: 'include',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    if (!loginResponse.ok) {
      throw new Error('登录失败');
    }
    
    // 2. 创建JWT Token
    const tokenResponse = await fetch(`${this.baseURL}/api/auth/jwt/create`, {
      method: 'POST',
      credentials: 'include',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        expiresIn: '24h',
        customClaims: { service: 'frontend-app' }
      })
    });
    
    const { token } = await tokenResponse.json();
    localStorage.setItem('jwt_token', token);
    return token;
  }
  
  async callAPI(endpoint: string, options: RequestInit = {}) {
    const token = localStorage.getItem('jwt_token');
    
    return fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  }
}

export const jwtClient = new JWTClient();
```

### 2. 测试组件

```typescript
// components/JWTTest.tsx
import { useState } from 'react';
import { jwtClient } from '../utils/jwt-client';

export default function JWTTest() {
  const [token, setToken] = useState<string>('');
  const [userInfo, setUserInfo] = useState<any>(null);
  
  const handleLogin = async () => {
    try {
      const jwtToken = await jwtClient.login('<EMAIL>', 'testpassword123');
      setToken(jwtToken);
      console.log('JWT Token:', jwtToken);
    } catch (error) {
      console.error('登录失败:', error);
    }
  };
  
  const handleGetUserInfo = async () => {
    try {
      const response = await jwtClient.callAPI('/api/auth/jwt/info');
      const data = await response.json();
      setUserInfo(data);
      console.log('用户信息:', data);
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };
  
  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">JWT认证测试</h2>
      
      <div className="space-y-4">
        <button 
          onClick={handleLogin}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          登录并获取JWT Token
        </button>
        
        {token && (
          <div>
            <p className="text-sm text-gray-600">JWT Token (前50字符):</p>
            <code className="block bg-gray-100 p-2 rounded text-xs">
              {token.substring(0, 50)}...
            </code>
          </div>
        )}
        
        <button 
          onClick={handleGetUserInfo}
          disabled={!token}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:bg-gray-300"
        >
          获取用户信息
        </button>
        
        {userInfo && (
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-bold">用户信息:</h3>
            <pre className="text-sm">{JSON.stringify(userInfo, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
```

## 🔍 调试技巧

### 1. 查看服务日志

```bash
# 启动服务时查看详细日志
pnpm dev
```

### 2. 检查数据库

```bash
# 查看JWKS表
psql $DATABASE_URL -c "SELECT key_id, active, created_at FROM auth.jwks;"

# 查看用户表
psql $DATABASE_URL -c "SELECT id, email, email_verified, role FROM auth.user LIMIT 5;"
```

### 3. 测试邮件发送

```bash
# 使用演示脚本
pnpm jwt:demo
```

### 4. 验证环境配置

```bash
# 检查环境变量
node -e "console.log('Frontend URL:', process.env.FRONTEND_URL)"
```

## 🚨 常见问题解决

### 1. JWKS表不存在

```sql
-- 手动创建JWKS表
CREATE TABLE IF NOT EXISTS auth.jwks (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    public_key TEXT NOT NULL,
    private_key TEXT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    algorithm TEXT DEFAULT 'EdDSA',
    curve TEXT DEFAULT 'Ed25519',
    key_id TEXT UNIQUE DEFAULT gen_random_uuid()::TEXT,
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITHOUT TIME ZONE,
    metadata JSONB DEFAULT '{}'
);
```

### 2. CORS错误

确保 `.env` 中设置：
```bash
CORS_ORIGIN=http://localhost:3000
```

### 3. 邮件发送失败

检查Resend API密钥是否正确：
```bash
curl -X POST https://api.resend.com/emails \
  -H "Authorization: Bearer YOUR_RESEND_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"from":"<EMAIL>","to":["<EMAIL>"],"subject":"Test","html":"<p>Test</p>"}'
```

## ✅ 测试检查清单

- [ ] 认证服务启动正常 (http://localhost:10086)
- [ ] 前端应用启动正常 (http://localhost:3000)
- [ ] JWKS表已创建
- [ ] 邮箱注册API正常响应
- [ ] 验证邮件发送成功
- [ ] 邮件链接指向前端地址
- [ ] JWT Token创建成功
- [ ] JWT Token验证成功
- [ ] 前端能正常调用JWT API
- [ ] Socket.IO连接认证测试（如需要）

完成这些测试后，你的JWT和邮箱注册功能就可以正常工作了！🎉