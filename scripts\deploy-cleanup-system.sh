#!/bin/bash

# specific-ai-auth 数据清理系统部署脚本
# 自动化部署数据清理 CronJob、监控和告警系统

set -euo pipefail

# 配置变量
NAMESPACE="${NAMESPACE:-ovs}"
KUBECTL_CMD="${KUBECTL_CMD:-kubectl}"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-**************/specific-ai}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
CONFIG_DIR="$(dirname "$0")/../k8s"
DRY_RUN="${DRY_RUN:-false}"
VERBOSE="${VERBOSE:-false}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $*"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $*"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $*" >&2
}

verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] DEBUG:${NC} $*"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
specific-ai-auth 数据清理系统部署脚本

用法: $0 [选项] [操作]

操作:
  deploy          部署完整的数据清理系统 (默认)
  cleanup         仅部署清理 CronJob
  monitoring      仅部署监控系统
  uninstall       卸载所有组件
  status          检查部署状态
  test            测试清理系统
  logs            查看清理任务日志

选项:
  -n, --namespace <name>     指定 K8s 命名空间 (默认: ovs)
  -r, --registry <url>       指定 Docker 镜像仓库 (默认: **************/specific-ai)
  -t, --tag <tag>            指定镜像标签 (默认: latest)
  -d, --dry-run              仅显示将要执行的命令，不实际执行
  -v, --verbose              详细输出
  -f, --force                强制执行，跳过确认
  -h, --help                 显示此帮助信息

环境变量:
  NAMESPACE                  K8s 命名空间
  KUBECTL_CMD                kubectl 命令路径
  DOCKER_REGISTRY           Docker 镜像仓库
  IMAGE_TAG                 镜像标签
  DRY_RUN                   是否为模拟运行 (true/false)
  VERBOSE                   是否详细输出 (true/false)

示例:
  # 部署完整系统
  $0 deploy

  # 模拟运行
  $0 deploy --dry-run

  # 部署到指定命名空间
  $0 deploy --namespace production

  # 仅部署监控系统
  $0 monitoring --verbose

  # 查看状态
  $0 status

  # 测试清理系统
  $0 test

  # 卸载所有组件
  $0 uninstall --force
EOF
}

# 解析命令行参数
parse_args() {
    local action="deploy"
    local force=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -r|--registry)
                DOCKER_REGISTRY="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                force=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            deploy|cleanup|monitoring|uninstall|status|test|logs)
                action="$1"
                shift
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "$action" "$force"
}

# 检查必需的工具
check_prerequisites() {
    local missing_tools=()
    
    if ! command -v "$KUBECTL_CMD" &> /dev/null; then
        missing_tools+=("kubectl")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "缺少必需的工具: ${missing_tools[*]}"
        error "请安装这些工具后重试"
        exit 1
    fi
    
    verbose "必需工具检查通过"
}

# 检查 K8s 连接
check_k8s_connection() {
    log "检查 Kubernetes 连接..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        verbose "模拟运行模式：跳过 K8s 连接检查"
        return 0
    fi
    
    if ! $KUBECTL_CMD cluster-info &> /dev/null; then
        error "无法连接到 Kubernetes 集群"
        error "请检查 kubeconfig 配置和集群状态"
        exit 1
    fi
    
    # 检查命名空间
    if ! $KUBECTL_CMD get namespace "$NAMESPACE" &> /dev/null; then
        warn "命名空间 '$NAMESPACE' 不存在，将自动创建"
        run_cmd "$KUBECTL_CMD create namespace $NAMESPACE"
    fi
    
    verbose "Kubernetes 连接检查通过"
}

# 执行命令（支持模拟运行）
run_cmd() {
    local cmd="$1"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${BLUE}[DRY-RUN]${NC} $cmd"
    else
        verbose "执行: $cmd"
        eval "$cmd"
    fi
}

# 应用 K8s 配置文件
apply_k8s_config() {
    local config_file="$1"
    local description="$2"
    
    log "部署 $description..."
    
    if [[ ! -f "$config_file" ]]; then
        error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 替换镜像标签和命名空间
    local temp_file="/tmp/$(basename "$config_file")"
    sed -e "s|**************/specific-ai/specific-ai-auth:latest|${DOCKER_REGISTRY}/specific-ai-auth:${IMAGE_TAG}|g" \
        -e "s|namespace: ovs|namespace: ${NAMESPACE}|g" \
        "$config_file" > "$temp_file"
    
    run_cmd "$KUBECTL_CMD apply -f $temp_file"
    
    # 清理临时文件
    if [[ "$DRY_RUN" != "true" ]]; then
        rm -f "$temp_file"
    fi
    
    verbose "$description 部署完成"
}

# 部署数据清理 CronJob
deploy_cleanup_cronjob() {
    log "部署数据清理 CronJob..."
    
    apply_k8s_config "$CONFIG_DIR/cronjob-data-cleanup.yaml" "数据清理 CronJob"
    
    log "数据清理 CronJob 部署完成"
}

# 部署监控系统
deploy_monitoring() {
    log "部署监控系统..."
    
    apply_k8s_config "$CONFIG_DIR/monitoring-cleanup.yaml" "清理监控系统"
    
    log "监控系统部署完成"
}

# 检查部署状态
check_deployment_status() {
    log "检查部署状态..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        verbose "模拟运行模式：跳过状态检查"
        return 0
    fi
    
    echo
    echo "=== CronJob 状态 ==="
    $KUBECTL_CMD get cronjobs -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup
    
    echo
    echo "=== 最近的清理任务 ==="
    $KUBECTL_CMD get jobs -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup --sort-by=.metadata.creationTimestamp | tail -5
    
    echo
    echo "=== 监控服务状态 ==="
    $KUBECTL_CMD get deployments,services -n "$NAMESPACE" -l app=specific-ai-auth,component=cleanup-metrics
    
    echo
    echo "=== ConfigMaps ==="
    $KUBECTL_CMD get configmaps -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup
    
    # 检查最近任务的状态
    local latest_job
    latest_job=$($KUBECTL_CMD get jobs -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[-1].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -n "$latest_job" ]]; then
        echo
        echo "=== 最新清理任务状态 ==="
        $KUBECTL_CMD describe job "$latest_job" -n "$NAMESPACE"
    fi
}

# 测试清理系统
test_cleanup_system() {
    log "测试数据清理系统..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        verbose "模拟运行模式：跳过实际测试"
        return 0
    fi
    
    # 创建测试任务
    local test_job_name="specific-ai-auth-cleanup-test-$(date +%s)"
    
    cat > "/tmp/${test_job_name}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: ${test_job_name}
  namespace: ${NAMESPACE}
  labels:
    app: specific-ai-auth
    component: data-cleanup
    job-type: test
spec:
  ttlSecondsAfterFinished: 300
  template:
    metadata:
      labels:
        app: specific-ai-auth
        component: data-cleanup
        job-type: test
    spec:
      restartPolicy: Never
      containers:
      - name: test-cleanup
        image: ${DOCKER_REGISTRY}/specific-ai-auth:${IMAGE_TAG}
        command: 
          - "/bin/sh"
          - "-c"
          - |
            echo "=== 数据清理系统测试 ==="
            echo "开始时间: \$(date)"
            
            # 健康检查
            echo "执行健康检查..."
            tsx /app/scripts/cleanup-data.ts --health-check --verbose
            
            # 模拟清理
            echo "执行模拟清理..."
            tsx /app/scripts/cleanup-data.ts --dry-run --verbose
            
            echo "测试完成时间: \$(date)"
            echo "=== 测试成功 ==="
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: DATABASE_URL
        - name: BETTER_AUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: specific-ai-auth-secret
              key: BETTER_AUTH_SECRET
        - name: NODE_ENV
          value: "production"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 200m
            memory: 256Mi
EOF
    
    log "创建测试任务: $test_job_name"
    run_cmd "$KUBECTL_CMD apply -f /tmp/${test_job_name}.yaml"
    
    # 等待任务完成
    log "等待测试任务完成..."
    if ! run_cmd "$KUBECTL_CMD wait --for=condition=complete --timeout=300s job/$test_job_name -n $NAMESPACE"; then
        error "测试任务超时或失败"
        
        # 显示任务日志
        echo
        echo "=== 测试任务日志 ==="
        $KUBECTL_CMD logs job/$test_job_name -n "$NAMESPACE" || true
        
        # 显示任务状态
        echo
        echo "=== 测试任务状态 ==="
        $KUBECTL_CMD describe job "$test_job_name" -n "$NAMESPACE" || true
        
        return 1
    fi
    
    # 显示测试结果
    echo
    echo "=== 测试任务日志 ==="
    $KUBECTL_CMD logs job/$test_job_name -n "$NAMESPACE"
    
    log "测试完成，任务将在5分钟后自动清理"
    
    # 清理临时文件
    rm -f "/tmp/${test_job_name}.yaml"
}

# 查看清理任务日志
view_cleanup_logs() {
    log "查看数据清理任务日志..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        verbose "模拟运行模式：跳过日志查看"
        return 0
    fi
    
    # 获取最近的清理任务
    local recent_jobs
    recent_jobs=$($KUBECTL_CMD get jobs -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | tail -3)
    
    if [[ -z "$recent_jobs" ]]; then
        warn "未找到任何清理任务"
        return 0
    fi
    
    echo "=== 最近的清理任务日志 ==="
    
    for job in $recent_jobs; do
        echo
        echo "--- 任务: $job ---"
        
        # 显示任务基本信息
        $KUBECTL_CMD get job "$job" -n "$NAMESPACE" -o wide
        
        echo
        echo "任务日志:"
        $KUBECTL_CMD logs job/"$job" -n "$NAMESPACE" --tail=50 || echo "无法获取日志"
        
        echo "----------------------------------------"
    done
}

# 卸载数据清理系统
uninstall_cleanup_system() {
    local force="$1"
    
    if [[ "$force" != "true" ]] && [[ "$DRY_RUN" != "true" ]]; then
        echo -n "确定要卸载数据清理系统吗？这将删除所有相关的 K8s 资源。(y/N): "
        read -r response
        if [[ "$response" != "y" ]] && [[ "$response" != "Y" ]]; then
            log "卸载操作已取消"
            return 0
        fi
    fi
    
    log "卸载数据清理系统..."
    
    # 删除 CronJob 和相关资源
    run_cmd "$KUBECTL_CMD delete cronjobs -n $NAMESPACE -l app=specific-ai-auth,component=data-cleanup --ignore-not-found=true"
    
    # 删除监控资源
    run_cmd "$KUBECTL_CMD delete deployments,services,configmaps -n $NAMESPACE -l app=specific-ai-auth,component=cleanup-metrics --ignore-not-found=true"
    
    # 删除清理相关的 ConfigMaps
    run_cmd "$KUBECTL_CMD delete configmaps -n $NAMESPACE -l app=specific-ai-auth,component=data-cleanup --ignore-not-found=true"
    
    # 删除所有清理相关的 Jobs（保留最近3个）
    local all_jobs
    all_jobs=$($KUBECTL_CMD get jobs -n "$NAMESPACE" -l app=specific-ai-auth,component=data-cleanup --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n')
    
    if [[ -n "$all_jobs" ]]; then
        local jobs_to_delete
        jobs_to_delete=$(echo "$all_jobs" | head -n -3)  # 保留最近3个
        
        if [[ -n "$jobs_to_delete" ]]; then
            for job in $jobs_to_delete; do
                run_cmd "$KUBECTL_CMD delete job $job -n $NAMESPACE --ignore-not-found=true"
            done
        fi
    fi
    
    log "数据清理系统卸载完成"
}

# 手动触发清理任务
trigger_cleanup_manually() {
    log "手动触发数据清理任务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        verbose "模拟运行模式：跳过手动触发"
        return 0
    fi
    
    # 从 CronJob 创建一个立即执行的 Job
    local manual_job_name="specific-ai-auth-manual-cleanup-$(date +%s)"
    
    $KUBECTL_CMD create job "$manual_job_name" --from=cronjob/specific-ai-auth-data-cleanup -n "$NAMESPACE"
    
    log "手动清理任务已创建: $manual_job_name"
    log "使用以下命令查看进度:"
    echo "  $KUBECTL_CMD logs job/$manual_job_name -n $NAMESPACE -f"
    echo "  $KUBECTL_CMD get job $manual_job_name -n $NAMESPACE -w"
}

# 主函数
main() {
    local args
    args=$(parse_args "$@")
    local action force
    read -r action force <<< "$args"
    
    echo "=== specific-ai-auth 数据清理系统部署工具 ==="
    echo "操作: $action"
    echo "命名空间: $NAMESPACE"
    echo "镜像: ${DOCKER_REGISTRY}/specific-ai-auth:${IMAGE_TAG}"
    echo "模拟运行: $DRY_RUN"
    echo "详细输出: $VERBOSE"
    echo
    
    # 检查必需的工具
    check_prerequisites
    
    # 检查 K8s 连接
    check_k8s_connection
    
    # 执行指定的操作
    case $action in
        deploy)
            deploy_cleanup_cronjob
            deploy_monitoring
            log "完整的数据清理系统部署完成"
            check_deployment_status
            ;;
        cleanup)
            deploy_cleanup_cronjob
            ;;
        monitoring)
            deploy_monitoring
            ;;
        status)
            check_deployment_status
            ;;
        test)
            test_cleanup_system
            ;;
        logs)
            view_cleanup_logs
            ;;
        trigger)
            trigger_cleanup_manually
            ;;
        uninstall)
            uninstall_cleanup_system "$force"
            ;;
        *)
            error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo
        warn "这是模拟运行，没有实际执行任何更改"
        warn "要执行实际部署，请去掉 --dry-run 参数"
    fi
    
    log "操作完成"
}

# 运行主函数
main "$@"