# Better Auth Service Dockerfile
# 极致优化的多阶段构建，最小化镜像体积

# === 构建阶段 ===
FROM node:20-alpine AS builder

# 安装构建工具（仅此阶段需要）
RUN apk add --no-cache python3 make g++

WORKDIR /app

# 安装 pnpm（仅构建阶段）
RUN npm install -g pnpm

# 优化依赖安装顺序
COPY package.json pnpm-lock.yaml ./

# 安装所有依赖（包括开发依赖）
RUN --mount=type=cache,target=/root/.pnpm-store \
    pnpm install --frozen-lockfile

# 复制源码和脚本
COPY tsconfig.json ./
COPY src/ ./src/
COPY scripts/ ./scripts/

# 构建项目
RUN pnpm run build && \
    pnpm prune --prod

# === 生产运行阶段 - 使用兼容的基础镜像 ===
FROM node:20-alpine AS production

# 设置工作目录
WORKDIR /app

# 创建非特权用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -u 1001 -G nodejs nodejs

# 仅复制运行时必需文件
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/scripts ./scripts

# 给脚本添加执行权限
RUN chmod +x /app/scripts/*.js

# 修改文件所有者
RUN chown -R nodejs:nodejs /app

# 切换到非特权用户
USER nodejs:nodejs

# 暴露端口
EXPOSE 10086

# 健康检查 - 修正路径为/api/health
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD ["node", "-e", "const http = require('http'); http.get('http://localhost:10086/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }).on('error', () => process.exit(1));"]

# 启动命令
CMD ["node", "dist/server.js"]