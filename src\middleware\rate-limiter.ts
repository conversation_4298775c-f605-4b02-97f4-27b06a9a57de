import { FastifyRequest, FastifyReply } from "fastify";
import { randomUUID } from "crypto";
import { db } from "../lib/drizzle.js";
import { rateLimiter, securityEvent } from "../lib/auth-schema.js";
import { eq, and, sql } from "drizzle-orm";

/**
 * 频率限制配置接口
 */
export interface RateLimitConfig {
  action: string; // 操作类型：'email_send', 'api_request', 'login_attempt'
  maxRequests: number; // 时间窗口内最大请求数
  windowMs: number; // 时间窗口（毫秒）
  blockDurationMs?: number; // 阻塞持续时间（毫秒），默认等于窗口时间
  skipOnSuccess?: boolean; // 成功请求是否计入限制
  keyGenerator?: (request: FastifyRequest) => string; // 自定义标识符生成器
}

/**
 * 默认频率限制配置
 */
export const DEFAULT_RATE_LIMITS: Record<string, RateLimitConfig | undefined> = {
  // 邮件发送限制：每10分钟最多3封邮件
  email_send: {
    action: 'email_send',
    maxRequests: 3,
    windowMs: 10 * 60 * 1000, // 10分钟
    blockDurationMs: 30 * 60 * 1000, // 阻塞30分钟
  },
  
  // API请求限制：每分钟最多100个请求
  api_request: {
    action: 'api_request',
    maxRequests: 100,
    windowMs: 60 * 1000, // 1分钟
    blockDurationMs: 5 * 60 * 1000, // 阻塞5分钟
  },
  
  // 登录尝试限制：每15分钟最多5次失败尝试
  login_attempt: {
    action: 'login_attempt',
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15分钟
    blockDurationMs: 60 * 60 * 1000, // 阻塞1小时
  },
  
  // 密码重置限制：每小时最多3次请求
  password_reset: {
    action: 'password_reset',
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1小时
    blockDurationMs: 2 * 60 * 60 * 1000, // 阻塞2小时
  },
  
  // 邮箱验证请求限制：每30分钟最多5次请求
  verification_request: {
    action: 'verification_request',
    maxRequests: 5,
    windowMs: 30 * 60 * 1000, // 30分钟
    blockDurationMs: 60 * 60 * 1000, // 阻塞1小时
  },
};

/**
 * 频率限制结果
 */
export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number; // 秒数
  blocked: boolean;
}

/**
 * 频率限制器类
 */
export class RateLimiterService {
  /**
   * 检查频率限制
   */
  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig,
    request?: FastifyRequest
  ): Promise<RateLimitResult> {
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.windowMs);
    
    try {
      // 清理过期记录
      await this.cleanupExpiredRecords(config.action, windowStart);
      
      // 检查是否被阻塞
      const blockedRecord = await db
        .select()
        .from(rateLimiter)
        .where(
          and(
            eq(rateLimiter.identifier, identifier),
            eq(rateLimiter.action, config.action),
            eq(rateLimiter.blocked, true),
            sql`${rateLimiter.blockedUntil} > ${now}`
          )
        )
        .limit(1);

      if (blockedRecord.length > 0) {
        const blockedUntil = blockedRecord[0]!.blockedUntil!;
        const retryAfter = Math.ceil((blockedUntil.getTime() - now.getTime()) / 1000);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: blockedUntil,
          retryAfter,
          blocked: true,
        };
      }

      // 获取或创建频率限制记录
      let limitRecord = await db
        .select()
        .from(rateLimiter)
        .where(
          and(
            eq(rateLimiter.identifier, identifier),
            eq(rateLimiter.action, config.action),
            sql`${rateLimiter.windowStart} > ${windowStart}`
          )
        )
        .limit(1);

      if (limitRecord.length === 0) {
        // 创建新的频率限制记录
        const newRecord = {
          id: randomUUID(),
          identifier,
          action: config.action,
          windowStart: now,
          requestCount: 1,
          maxRequests: config.maxRequests,
          windowDurationMs: config.windowMs,
          blocked: false,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(rateLimiter).values(newRecord);
        
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: new Date(now.getTime() + config.windowMs),
          blocked: false,
        };
      }

      const record = limitRecord[0]!;
      const newCount = record.requestCount + 1;

      // 检查是否超过限制
      if (newCount > config.maxRequests) {
        // 超过限制，阻塞用户
        const blockDuration = config.blockDurationMs || config.windowMs;
        const blockedUntil = new Date(now.getTime() + blockDuration);
        
        await db
          .update(rateLimiter)
          .set({
            requestCount: newCount,
            blocked: true,
            blockedUntil,
            updatedAt: now,
          })
          .where(eq(rateLimiter.id, record.id));

        // 记录安全事件
        await this.recordSecurityEvent(
          identifier,
          'rate_limit_exceeded',
          'medium',
          request,
          {
            action: config.action,
            requestCount: newCount,
            maxRequests: config.maxRequests,
            windowMs: config.windowMs,
          }
        );

        const retryAfter = Math.ceil(blockDuration / 1000);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: blockedUntil,
          retryAfter,
          blocked: true,
        };
      }

      // 更新请求计数
      await db
        .update(rateLimiter)
        .set({
          requestCount: newCount,
          updatedAt: now,
        })
        .where(eq(rateLimiter.id, record.id));

      const resetTime = new Date(record.windowStart.getTime() + config.windowMs);
      
      return {
        allowed: true,
        remaining: config.maxRequests - newCount,
        resetTime,
        blocked: false,
      };

    } catch (error) {
      console.error('频率限制检查失败:', error);
      // 发生错误时，允许请求通过，但记录错误
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: new Date(now.getTime() + config.windowMs),
        blocked: false,
      };
    }
  }

  /**
   * 清理过期的频率限制记录
   */
  private async cleanupExpiredRecords(action: string, windowStart: Date): Promise<void> {
    try {
      await db
        .delete(rateLimiter)
        .where(
          and(
            eq(rateLimiter.action, action),
            eq(rateLimiter.blocked, false),
            sql`${rateLimiter.windowStart} < ${windowStart}`
          )
        );
    } catch (error) {
      console.error('清理过期频率限制记录失败:', error);
    }
  }

  /**
   * 记录安全事件
   */
  private async recordSecurityEvent(
    identifier: string,
    eventType: string,
    severity: string,
    request?: FastifyRequest,
    details?: any
  ): Promise<void> {
    try {
      const eventId = randomUUID();
      const now = new Date();
      
      const eventData = {
        id: eventId,
        userId: null, // 可以在有用户上下文时设置
        eventType,
        severity,
        ipAddress: this.getClientIP(request),
        userAgent: request?.headers['user-agent'] as string,
        details: JSON.stringify({
          identifier,
          timestamp: now.toISOString(),
          ...details,
        }),
        resolved: false,
        createdAt: now,
      };

      await db.insert(securityEvent).values(eventData);
    } catch (error) {
      console.error('记录安全事件失败:', error);
    }
  }

  /**
   * 获取客户端IP地址
   */
  public getClientIP(request?: FastifyRequest): string | undefined {
    if (!request) return undefined;
    
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (request.headers['x-real-ip'] as string) ||
      request.ip ||
      request.socket.remoteAddress
    );
  }

  /**
   * 重置频率限制（用于管理员操作）
   */
  async resetRateLimit(identifier: string, action: string): Promise<void> {
    try {
      await db
        .delete(rateLimiter)
        .where(
          and(
            eq(rateLimiter.identifier, identifier),
            eq(rateLimiter.action, action)
          )
        );
    } catch (error) {
      console.error('重置频率限制失败:', error);
      throw error;
    }
  }

  /**
   * 获取频率限制状态
   */
  async getRateLimitStatus(identifier: string, action: string): Promise<RateLimitResult | null> {
    try {
      const record = await db
        .select()
        .from(rateLimiter)
        .where(
          and(
            eq(rateLimiter.identifier, identifier),
            eq(rateLimiter.action, action)
          )
        )
        .limit(1);

      if (record.length === 0) {
        return null;
      }

      const limit = record[0]!;
      const now = new Date();
      const resetTime = new Date(limit.windowStart.getTime() + limit.windowDurationMs);
      
      let retryAfter: number | undefined;
      if (limit.blocked && limit.blockedUntil && limit.blockedUntil > now) {
        retryAfter = Math.ceil((limit.blockedUntil.getTime() - now.getTime()) / 1000);
      }

      return {
        allowed: !limit.blocked || !!(limit.blockedUntil && limit.blockedUntil <= now),
        remaining: Math.max(0, limit.maxRequests - limit.requestCount),
        resetTime,
        retryAfter,
        blocked: Boolean(limit.blocked && limit.blockedUntil && limit.blockedUntil > now),
      };
    } catch (error) {
      console.error('获取频率限制状态失败:', error);
      return null;
    }
  }
}

// 创建全局实例
export const rateLimiterService = new RateLimiterService();

/**
 * 创建频率限制中间件
 */
export function createRateLimitMiddleware(config: RateLimitConfig) {
  return async function rateLimitMiddleware(
    request: FastifyRequest,
    reply: FastifyReply
  ) {
    try {
      // 生成标识符
      const identifier = config.keyGenerator 
        ? config.keyGenerator(request)
        : rateLimiterService.getClientIP(request) || 'anonymous';

      // 检查频率限制
      const result = await rateLimiterService.checkRateLimit(identifier, config, request);

      // 设置响应头
      reply.header('X-RateLimit-Limit', config.maxRequests);
      reply.header('X-RateLimit-Remaining', result.remaining);
      reply.header('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000));

      if (!result.allowed) {
        if (result.retryAfter) {
          reply.header('Retry-After', result.retryAfter);
        }

        return reply.status(429).send({
          success: false,
          error: 'RATE_LIMIT_EXCEEDED',
          message: result.blocked 
            ? `请求过于频繁，已被暂时阻塞。请在 ${result.retryAfter} 秒后重试。`
            : '请求过于频繁，请稍后重试。',
          retryAfter: result.retryAfter,
          resetTime: result.resetTime.toISOString(),
        });
      }

      // 将频率限制信息附加到请求对象
      (request as any).rateLimit = result;
    } catch (error) {
      console.error('频率限制中间件错误:', error);
      // 错误时允许请求继续，但记录错误
    }
  };
}

/**
 * 预定义的中间件实例
 */
export const emailSendRateLimit = createRateLimitMiddleware(DEFAULT_RATE_LIMITS.email_send!);
export const apiRequestRateLimit = createRateLimitMiddleware(DEFAULT_RATE_LIMITS.api_request!);
export const loginAttemptRateLimit = createRateLimitMiddleware(DEFAULT_RATE_LIMITS.login_attempt!);
export const passwordResetRateLimit = createRateLimitMiddleware(DEFAULT_RATE_LIMITS.password_reset!);
export const verificationRequestRateLimit = createRateLimitMiddleware(DEFAULT_RATE_LIMITS.verification_request!);