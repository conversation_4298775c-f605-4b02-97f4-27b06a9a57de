import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";
import { db } from "../lib/drizzle.js";
import {
  member,
  organization,
  subscription,
  invitation,
  user,
} from "../lib/auth-schema.js";
import { eq, and, count } from "drizzle-orm";
import { checkMembershipLimit } from "../lib/organization.js";
import { auth } from "../auth.js";

// 成员邀请请求的验证schema
const inviteMemberSchema = z.object({
  email: z.string().email("请输入有效的邮箱地址"),
  role: z.enum(["member", "admin", "owner"]).default("member"),
  organizationId: z.string().min(1, "组织ID不能为空"),
});

const updateSubscriptionSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  plan: z.enum(["basic", "pro", "enterprise"]),
});

const updateCompanyProfileStatusSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  hasCompanyProfile: z.boolean(),
});

export async function registerOrganizationRoutes(app: FastifyInstance) {
  // 检查成员限制状态
  app.get(
    "/api/auth/organization/:orgId/member-status",
    {
      schema: {
        description: "获取组织成员限制状态",
        tags: ["Organization"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        params: {
          type: "object",
          properties: {
            orgId: { type: "string" },
          },
          required: ["orgId"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              canAddMember: { type: "boolean" },
              currentCount: { type: "number" },
              limit: { type: "number" },
              plan: { type: "string" },
              suggestedPlan: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{ Params: { orgId: string } }>,
      reply: FastifyReply,
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        const { orgId } = request.params;

        // 检查用户是否是该组织成员
        const memberRecord = await db
          .select()
          .from(member)
          .where(
            and(
              eq(member.userId, session.user.id),
              eq(member.organizationId, orgId),
            ),
          )
          .limit(1);

        if (memberRecord.length === 0) {
          return reply.status(403).send({
            error: "FORBIDDEN",
            message: "无权访问该组织",
          });
        }

        // 获取成员限制状态
        const memberStatus = await checkMembershipLimit(orgId);

        return reply.status(200).send(memberStatus);
      } catch (error) {
        app.log.error("获取成员状态失败:", error);
        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "获取成员状态失败",
        });
      }
    },
  );

  // 邀请成员（需要权限检查）
  app.post(
    "/api/auth/organization/invite",
    {
      schema: {
        description: "邀请成员加入组织",
        tags: ["Organization"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        body: {
          type: "object",
          properties: {
            email: { type: "string", format: "email" },
            role: { type: "string", enum: ["member", "admin", "owner"] },
            organizationId: { type: "string" },
          },
          required: ["email", "organizationId"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              invitationId: { type: "string" },
            },
          },
          400: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          email: string;
          role?: string;
          organizationId: string;
        };
      }>,
      reply: FastifyReply,
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 验证请求数据
        const {
          email,
          role = "member",
          organizationId,
        } = inviteMemberSchema.parse(request.body);

        // 检查用户是否有邀请权限
        const memberRecord = await db
          .select()
          .from(member)
          .where(
            and(
              eq(member.userId, session.user.id),
              eq(member.organizationId, organizationId),
            ),
          )
          .limit(1);

        if (
          memberRecord.length === 0 ||
          !["owner", "admin"].includes(memberRecord[0]!.role)
        ) {
          return reply.status(403).send({
            error: "FORBIDDEN",
            message: "无权邀请成员",
          });
        }

        // 检查成员限制
        const memberStatus = await checkMembershipLimit(organizationId);
        if (!memberStatus.canAddMember) {
          return reply.status(400).send({
            error: "MEMBER_LIMIT_EXCEEDED",
            message:
              `已达到 ${memberStatus.plan} 计划的成员上限 (${memberStatus.limit})。` +
              (memberStatus.suggestedPlan
                ? ` 请升级到 ${memberStatus.suggestedPlan} 计划以添加更多成员。`
                : " 请联系管理员了解更多选项。"),
          });
        }

        // 检查邮箱是否已被邀请
        const existingInvitation = await db
          .select()
          .from(invitation)
          .where(
            and(
              eq(invitation.email, email),
              eq(invitation.organizationId, organizationId),
              eq(invitation.status, "pending"),
            ),
          )
          .limit(1);

        if (existingInvitation.length > 0) {
          return reply.status(400).send({
            error: "ALREADY_INVITED",
            message: "该邮箱已被邀请",
          });
        }

        // 创建邀请
        const newInvitationId = `inv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        await db.insert(invitation).values({
          id: newInvitationId,
          organizationId,
          email,
          role,
          status: "pending",
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
          inviterId: session.user.id,
        });

        return reply.status(200).send({
          success: true,
          message: "邀请发送成功",
          invitationId: newInvitationId,
        });
      } catch (error) {
        app.log.error("邀请成员失败:", error);

        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: error.errors.map((e) => e.message).join(", "),
          });
        }

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "邀请成员失败",
        });
      }
    },
  );

  // 管理员更新订阅计划
  app.put(
    "/api/auth/organization/subscription",
    {
      schema: {
        description: "更新组织订阅计划（管理员专用）",
        tags: ["Organization"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        body: {
          type: "object",
          properties: {
            organizationId: { type: "string" },
            plan: { type: "string", enum: ["basic", "pro", "enterprise"] },
          },
          required: ["organizationId", "plan"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          organizationId: string;
          plan: string;
        };
      }>,
      reply: FastifyReply,
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 验证请求数据
        const { organizationId, plan } = updateSubscriptionSchema.parse(
          request.body,
        );

        // 检查用户是否有管理权限（暂时允许owner，后续可以添加super_admin检查）
        const memberRecord = await db
          .select()
          .from(member)
          .where(
            and(
              eq(member.userId, session.user.id),
              eq(member.organizationId, organizationId),
              eq(member.role, "owner"),
            ),
          )
          .limit(1);

        if (memberRecord.length === 0) {
          return reply.status(403).send({
            error: "FORBIDDEN",
            message: "无权修改订阅计划",
          });
        }

        // 获取计划配置
        const { getPlanMemberLimit } = await import("../lib/plans.js");
        const newMemberLimit = getPlanMemberLimit(plan as any);

        // 检查当前成员数量是否超过新计划限制
        const memberCountResult = await db
          .select({ count: count() })
          .from(member)
          .where(eq(member.organizationId, organizationId));

        const currentMemberCount = memberCountResult[0]!.count;

        if (currentMemberCount > newMemberLimit) {
          return reply.status(400).send({
            error: "MEMBER_COUNT_EXCEEDS_LIMIT",
            message: `当前成员数量 (${currentMemberCount}) 超过了 ${plan} 计划的限制 (${newMemberLimit})`,
          });
        }

        // 更新订阅计划 (使用 ON CONFLICT 实现 upsert)
        await db
          .insert(subscription)
          .values({
            id: `sub_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            organizationId,
            plan,
            memberLimit: newMemberLimit,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .onConflictDoUpdate({
            target: subscription.organizationId,
            set: {
              plan,
              memberLimit: newMemberLimit,
              updatedAt: new Date(),
            },
          });

        return reply.status(200).send({
          success: true,
          message: `订阅计划已更新为 ${plan}`,
        });
      } catch (error) {
        app.log.error("更新订阅计划失败:", error);

        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: error.errors.map((e) => e.message).join(", "),
          });
        }

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "更新订阅计划失败",
        });
      }
    },
  );

  // 获取所有用户的组织列表（管理员接口）
  app.get(
    "/api/auth/organization/users-orgs",
    {
      schema: {
        description: "获取所有用户的组织列表（管理员专用）",
        tags: ["Organization"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    user: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        name: { type: "string" },
                        email: { type: "string" },
                        role: { type: "string" },
                        createdAt: { type: "string" },
                        companyName: { type: "string" },
                      },
                    },
                    organizations: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          id: { type: "string" },
                          name: { type: "string" },
                          slug: { type: "string" },
                          memberRole: { type: "string" },
                          joinedAt: { type: "string" },
                          subscription: {
                            type: "object",
                            properties: {
                              plan: { type: "string" },
                              memberLimit: { type: "number" },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 检查用户是否有管理员权限
        const currentUser = await db
          .select()
          .from(user)
          .where(eq(user.id, session.user.id))
          .limit(1);

        if (
          currentUser.length === 0 ||
          !["super_admin", "admin"].includes(currentUser[0]!.role || "")
        ) {
          return reply.status(403).send({
            error: "FORBIDDEN",
            message: "需要管理员权限",
          });
        }

        // 查询所有 role === "user" 的用户及其组织信息
        const usersWithOrgs = await db
          .select({
            user: user,
            member: member,
            organization: organization,
            subscription: subscription,
          })
          .from(user)
          .leftJoin(member, eq(user.id, member.userId))
          .leftJoin(organization, eq(member.organizationId, organization.id))
          .leftJoin(subscription, eq(organization.id, subscription.organizationId))
          .where(eq(user.role, "user"))
          .orderBy(user.createdAt, user.email);

        // 组织数据：按用户分组
        const userMap = new Map();

        for (const row of usersWithOrgs) {
          const userId = row.user.id;
          
          if (!userMap.has(userId)) {
            userMap.set(userId, {
              user: {
                id: row.user.id,
                name: row.user.name,
                email: row.user.email,
                role: row.user.role,
                createdAt: row.user.createdAt.toISOString(),
                companyName: row.user.companyName,
              },
              organizations: [],
            });
          }

          // 如果有组织信息，添加到用户的组织列表中
          if (row.organization && row.member) {
            userMap.get(userId).organizations.push({
              id: row.organization.id,
              name: row.organization.name,
              slug: row.organization.slug,
              memberRole: row.member.role,
              joinedAt: row.member.createdAt.toISOString(),
              subscription: row.subscription
                ? {
                    plan: row.subscription.plan,
                    memberLimit: row.subscription.memberLimit,
                  }
                : null,
            });
          }
        }

        // 转换为数组格式
        const result = Array.from(userMap.values());

        return reply.status(200).send({
          success: true,
          data: result,
        });
      } catch (error) {
        app.log.error("获取用户组织列表失败:", error);
        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "获取用户组织列表失败",
        });
      }
    },
  );

  // 获取指定组织的所有成员信息（管理员接口）
  app.get(
    "/api/auth/organization/:orgId/members",
    {
      schema: {
        description: "获取指定组织的所有成员信息（管理员专用）",
        tags: ["Organization"],
        security: [{ BearerAuth: [] }, { CookieAuth: [] }],
        params: {
          type: "object",
          properties: {
            orgId: { type: "string" },
          },
          required: ["orgId"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  organization: {
                    type: "object",
                    properties: {
                      id: { type: "string" },
                      name: { type: "string" },
                      slug: { type: "string" },
                      createdAt: { type: "string" },
                      metadata: { type: "string" },
                    },
                  },
                  subscription: {
                    type: "object",
                    properties: {
                      plan: { type: "string" },
                      memberLimit: { type: "number" },
                      createdAt: { type: "string" },
                      updatedAt: { type: "string" },
                    },
                  },
                  members: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        role: { type: "string" },
                        joinedAt: { type: "string" },
                        user: {
                          type: "object",
                          properties: {
                            id: { type: "string" },
                            name: { type: "string" },
                            email: { type: "string" },
                            role: { type: "string" },
                            createdAt: { type: "string" },
                            companyName: { type: "string" },
                            emailVerified: { type: "boolean" },
                          },
                        },
                      },
                    },
                  },
                  pendingInvitations: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        email: { type: "string" },
                        role: { type: "string" },
                        status: { type: "string" },
                        expiresAt: { type: "string" },
                        inviter: {
                          type: "object",
                          properties: {
                            id: { type: "string" },
                            name: { type: "string" },
                            email: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                  statistics: {
                    type: "object",
                    properties: {
                      totalMembers: { type: "number" },
                      membersByRole: {
                        type: "object",
                        properties: {
                          owner: { type: "number" },
                          admin: { type: "number" },
                          member: { type: "number" },
                        },
                      },
                      pendingInvitationsCount: { type: "number" },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{ Params: { orgId: string } }>,
      reply: FastifyReply,
    ) => {
      try {
        // 验证用户会话
        const headers = new Headers();
        Object.entries(request.headers).forEach(([key, value]) => {
          if (typeof value === "string") {
            headers.set(key, value);
          } else if (Array.isArray(value)) {
            headers.set(key, value.join(", "));
          }
        });

        const session = await auth.api.getSession({ headers });

        if (!session?.user) {
          return reply.status(401).send({
            error: "UNAUTHORIZED",
            message: "请先登录",
          });
        }

        // 检查用户是否有管理员权限
        const currentUser = await db
          .select()
          .from(user)
          .where(eq(user.id, session.user.id))
          .limit(1);

        if (
          currentUser.length === 0 ||
          !["super_admin", "admin"].includes(currentUser[0]!.role || "")
        ) {
          return reply.status(403).send({
            error: "FORBIDDEN",
            message: "需要管理员权限",
          });
        }

        const { orgId } = request.params;

        // 检查组织是否存在
        const orgResult = await db
          .select()
          .from(organization)
          .where(eq(organization.id, orgId))
          .limit(1);

        if (orgResult.length === 0) {
          return reply.status(404).send({
            error: "NOT_FOUND",
            message: "组织不存在",
          });
        }

        const org = orgResult[0]!;

        // 获取组织订阅信息
        const subscriptionResult = await db
          .select()
          .from(subscription)
          .where(eq(subscription.organizationId, orgId))
          .limit(1);

        // 获取所有成员及其用户信息
        const membersResult = await db
          .select({
            member: member,
            user: user,
          })
          .from(member)
          .leftJoin(user, eq(member.userId, user.id))
          .where(eq(member.organizationId, orgId))
          .orderBy(member.createdAt);

        // 获取待处理的邀请
        const invitationsResult = await db
          .select({
            invitation: invitation,
            inviter: user,
          })
          .from(invitation)
          .leftJoin(user, eq(invitation.inviterId, user.id))
          .where(
            and(
              eq(invitation.organizationId, orgId),
              eq(invitation.status, "pending"),
            ),
          )
          .orderBy(invitation.expiresAt);

        // 构建成员列表
        const members = membersResult.map((row) => ({
          id: row.member.id,
          role: row.member.role,
          joinedAt: row.member.createdAt.toISOString(),
          user: row.user
            ? {
                id: row.user.id,
                name: row.user.name,
                email: row.user.email,
                role: row.user.role,
                createdAt: row.user.createdAt.toISOString(),
                companyName: row.user.companyName,
                emailVerified: row.user.emailVerified,
              }
            : null,
        }));

        // 构建待处理邀请列表
        const pendingInvitations = invitationsResult.map((row) => ({
          id: row.invitation.id,
          email: row.invitation.email,
          role: row.invitation.role,
          status: row.invitation.status,
          expiresAt: row.invitation.expiresAt.toISOString(),
          inviter: row.inviter
            ? {
                id: row.inviter.id,
                name: row.inviter.name,
                email: row.inviter.email,
              }
            : null,
        }));

        // 计算统计信息
        const membersByRole = {
          owner: members.filter((m) => m.role === "owner").length,
          admin: members.filter((m) => m.role === "admin").length,
          member: members.filter((m) => m.role === "member").length,
        };

        const statistics = {
          totalMembers: members.length,
          membersByRole,
          pendingInvitationsCount: pendingInvitations.length,
        };

        return reply.status(200).send({
          success: true,
          data: {
            organization: {
              id: org.id,
              name: org.name,
              slug: org.slug,
              createdAt: org.createdAt.toISOString(),
              metadata: org.metadata,
            },
            subscription: subscriptionResult.length > 0
              ? {
                  plan: subscriptionResult[0]!.plan,
                  memberLimit: subscriptionResult[0]!.memberLimit,
                  createdAt: subscriptionResult[0]!.createdAt.toISOString(),
                  updatedAt: subscriptionResult[0]!.updatedAt.toISOString(),
                }
              : null,
            members,
            pendingInvitations,
            statistics,
          },
        });
      } catch (error) {
        app.log.error("获取组织成员信息失败:", error);
        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "获取组织成员信息失败",
        });
      }
    },
  );

  // 内部微服务接口：更新组织公司资料完成状态
  app.put(
    "/api/internal/organization/company-profile-status",
    {
      schema: {
        description: "更新组织公司资料完成状态（内部微服务专用）",
        tags: ["Organization"],
        body: {
          type: "object",
          properties: {
            organizationId: { type: "string" },
            hasCompanyProfile: { type: "boolean" },
          },
          required: ["organizationId", "hasCompanyProfile"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
          400: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
          404: {
            type: "object",
            properties: {
              error: { type: "string" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    async (
      request: FastifyRequest<{
        Body: {
          organizationId: string;
          hasCompanyProfile: boolean;
        };
      }>,
      reply: FastifyReply,
    ) => {
      try {
        // 验证请求数据
        const { organizationId, hasCompanyProfile } = 
          updateCompanyProfileStatusSchema.parse(request.body);

        // 检查组织是否存在
        const orgResult = await db
          .select()
          .from(organization)
          .where(eq(organization.id, organizationId))
          .limit(1);

        if (orgResult.length === 0) {
          return reply.status(404).send({
            error: "ORGANIZATION_NOT_FOUND",
            message: "组织不存在",
          });
        }

        // 更新 hasCompanyProfile 字段
        await db
          .update(organization)
          .set({
            hasCompanyProfile: hasCompanyProfile,
          })
          .where(eq(organization.id, organizationId));

        return reply.status(200).send({
          success: true,
          message: "公司资料状态更新成功",
        });
      } catch (error) {
        app.log.error("更新公司资料状态失败:", error);

        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "VALIDATION_ERROR",
            message: error.errors.map((e) => e.message).join(", "),
          });
        }

        return reply.status(500).send({
          error: "INTERNAL_ERROR",
          message: "更新公司资料状态失败",
        });
      }
    },
  );
}
