import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { FastifyRequest, FastifyReply } from 'fastify';
import {
  requireEmailVerification,
  optionalEmailVerificationCheck,
  addEmailVerificationReminder,
  emailVerificationRateLimit,
  logEmailVerificationAttempt,
  recordSecurityEvent,
  trackEmailSending,
  updateEmailLogStatus
} from '../src/middleware/email-verification.js';

// 模拟数据库
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  name: '测试用户',
  emailVerified: false
};

const mockVerifiedUser = {
  id: 'verified-user-id',
  email: '<EMAIL>',
  name: '已验证用户',
  emailVerified: true
};

jest.mock('../src/lib/drizzle.js', () => ({
  db: {
    insert: jest.fn().mockReturnValue({
      values: jest.fn().mockResolvedValue({ rowCount: 1 })
    }),
    update: jest.fn().mockReturnValue({
      set: jest.fn().mockReturnValue({
        where: jest.fn().mockResolvedValue({ rowCount: 1 })
      })
    }),
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([mockUser])
        })
      })
    })
  }
}));

// 模拟Better Auth
jest.mock('../src/auth.js', () => ({
  auth: {
    api: {
      getSession: jest.fn().mockResolvedValue({
        user: mockUser
      })
    }
  }
}));

// 模拟频率限制服务
jest.mock('../src/middleware/rate-limiter.js', () => ({
  rateLimiterService: {
    checkRateLimit: jest.fn().mockResolvedValue({
      allowed: true,
      remaining: 4,
      resetTime: new Date(Date.now() + 60000),
      retryAfter: undefined,
      blocked: false
    })
  },
  DEFAULT_RATE_LIMITS: {
    verification_request: {
      maxRequests: 5,
      windowMs: 60000
    }
  }
}));

describe('邮箱验证中间件测试', () => {
  let mockRequest: Partial<FastifyRequest>;
  let mockReply: Partial<FastifyReply>;

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();

    // 创建mock的request和reply对象
    mockRequest = {
      headers: {
        'authorization': 'Bearer test-token',
        'user-agent': 'test-agent',
        'x-forwarded-for': '127.0.0.1'
      },
      ip: '127.0.0.1',
      socket: {
        remoteAddress: '127.0.0.1'
      } as any,
      method: 'POST',
      url: '/api/test'
    };

    mockReply = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      header: jest.fn().mockReturnThis(),
      addHook: jest.fn().mockReturnThis()
    };
  });

  describe('requireEmailVerification 中间件', () => {
    it('应该允许已验证邮箱的用户通过', async () => {
      // 模拟已验证用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 不应该调用reply.status或reply.send（表示中间件通过）
      expect(mockReply.status).not.toHaveBeenCalled();
      expect(mockReply.send).not.toHaveBeenCalled();
    });

    it('应该阻止未验证邮箱的用户', async () => {
      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(403);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'EMAIL_NOT_VERIFIED',
        message: '请先验证您的邮箱地址',
        data: {
          requireEmailVerification: true,
          userId: mockUser.id,
          email: mockUser.email
        }
      });
    });

    it('应该拒绝未登录的用户', async () => {
      // 模拟未登录用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce(null);

      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '请先登录'
      });
    });

    it('应该处理用户不存在的情况', async () => {
      // 模拟用户不存在
      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([]) // 空数组表示用户不存在
          })
        })
      });

      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'USER_NOT_FOUND',
        message: '用户不存在'
      });
    });

    it('应该处理数据库错误', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('数据库连接失败'))
          })
        })
      });

      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '验证邮箱状态时出错'
      });
    });

    it('应该处理Better Auth错误', async () => {
      // 模拟Better Auth错误
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockRejectedValueOnce(new Error('认证服务异常'));

      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '验证邮箱状态时出错'
      });
    });
  });

  describe('optionalEmailVerificationCheck 中间件', () => {
    it('应该为已登录用户设置验证状态', async () => {
      await optionalEmailVerificationCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 检查request对象是否被正确设置
      expect((mockRequest as any).emailVerificationStatus).toEqual({
        isVerified: false,
        userId: mockUser.id,
        email: mockUser.email
      });
    });

    it('应该为已验证用户设置正确状态', async () => {
      // 模拟已验证用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      await optionalEmailVerificationCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect((mockRequest as any).emailVerificationStatus).toEqual({
        isVerified: true,
        userId: mockVerifiedUser.id,
        email: mockVerifiedUser.email
      });
    });

    it('应该正确处理未登录用户', async () => {
      // 模拟未登录用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce(null);

      await optionalEmailVerificationCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 不应该设置emailVerificationStatus
      expect((mockRequest as any).emailVerificationStatus).toBeUndefined();
    });

    it('应该忽略错误并继续执行', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('数据库错误'))
          })
        })
      });

      // 不应该抛出异常
      await expect(
        optionalEmailVerificationCheck(
          mockRequest as FastifyRequest,
          mockReply as FastifyReply
        )
      ).resolves.not.toThrow();

      // 不应该设置验证状态
      expect((mockRequest as any).emailVerificationStatus).toBeUndefined();
    });
  });

  describe('addEmailVerificationReminder 函数', () => {
    it('应该为未验证用户添加提醒信息', () => {
      const response = { message: '操作成功' };
      const emailVerificationStatus = {
        isVerified: false,
        userId: 'test-user-id',
        email: '<EMAIL>'
      };

      const result = addEmailVerificationReminder(response, emailVerificationStatus);

      expect(result).toEqual({
        message: '操作成功',
        emailVerification: {
          required: true,
          message: '建议验证邮箱以获得完整功能访问权限',
          userId: 'test-user-id',
          email: '<EMAIL>'
        }
      });
    });

    it('应该为已验证用户保持原响应', () => {
      const response = { message: '操作成功' };
      const emailVerificationStatus = {
        isVerified: true,
        userId: 'verified-user-id',
        email: '<EMAIL>'
      };

      const result = addEmailVerificationReminder(response, emailVerificationStatus);

      expect(result).toEqual({ message: '操作成功' });
    });

    it('应该处理未提供验证状态的情况', () => {
      const response = { message: '操作成功' };

      const result = addEmailVerificationReminder(response);

      expect(result).toEqual({ message: '操作成功' });
    });
  });

  describe('emailVerificationRateLimit 中间件', () => {
    it('应该允许正常频率的请求通过', async () => {
      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 应该设置频率限制头部
      expect(mockReply.header).toHaveBeenCalledWith('X-RateLimit-Limit', 5);
      expect(mockReply.header).toHaveBeenCalledWith('X-RateLimit-Remaining', 4);
      expect(mockReply.header).toHaveBeenCalledWith('X-RateLimit-Reset', expect.any(Number));

      // 不应该阻止请求
      expect(mockReply.status).not.toHaveBeenCalledWith(429);

      // 应该在request对象上设置频率限制信息
      expect((mockRequest as any).emailVerificationRateLimit).toBeDefined();
    });

    it('应该阻止超过频率限制的请求', async () => {
      // 模拟频率限制触发
      const { rateLimiterService } = require('../src/middleware/rate-limiter.js');
      rateLimiterService.checkRateLimit.mockResolvedValueOnce({
        allowed: false,
        remaining: 0,
        resetTime: new Date(Date.now() + 60000),
        retryAfter: 60,
        blocked: false
      });

      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(429);
      expect(mockReply.header).toHaveBeenCalledWith('Retry-After', 60);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: '验证邮件发送过于频繁，请稍后重试。',
        retryAfter: 60,
        resetTime: expect.any(String)
      });
    });

    it('应该处理被阻止的用户', async () => {
      // 模拟用户被阻止
      const { rateLimiterService } = require('../src/middleware/rate-limiter.js');
      rateLimiterService.checkRateLimit.mockResolvedValueOnce({
        allowed: false,
        remaining: 0,
        resetTime: new Date(Date.now() + 60000),
        retryAfter: 600,
        blocked: true
      });

      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(429);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: '验证邮件发送过于频繁，已被暂时阻止。请在 600 秒后重试。',
        retryAfter: 600,
        resetTime: expect.any(String)
      });
    });

    it('应该使用邮箱作为标识符', async () => {
      const { rateLimiterService } = require('../src/middleware/rate-limiter.js');

      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(rateLimiterService.checkRateLimit).toHaveBeenCalledWith(
        mockUser.email, // 应该使用用户邮箱作为标识符
        expect.any(Object),
        mockRequest
      );
    });

    it('应该使用IP地址作为未登录用户的标识符', async () => {
      // 模拟未登录用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValueOnce(null);

      const { rateLimiterService } = require('../src/middleware/rate-limiter.js');

      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(rateLimiterService.checkRateLimit).toHaveBeenCalledWith(
        '127.0.0.1', // 应该使用IP地址作为标识符
        expect.any(Object),
        mockRequest
      );
    });

    it('应该处理频率限制服务错误', async () => {
      // 模拟频率限制服务错误
      const { rateLimiterService } = require('../src/middleware/rate-limiter.js');
      rateLimiterService.checkRateLimit.mockRejectedValueOnce(new Error('服务异常'));

      // 不应该抛出异常，应该允许请求继续
      await expect(
        emailVerificationRateLimit(
          mockRequest as FastifyRequest,
          mockReply as FastifyReply
        )
      ).resolves.not.toThrow();

      // 不应该阻止请求
      expect(mockReply.status).not.toHaveBeenCalledWith(429);
    });
  });

  describe('logEmailVerificationAttempt 函数', () => {
    it('应该记录成功的验证尝试', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await logEmailVerificationAttempt(
        'test-user-id',
        '<EMAIL>',
        'success',
        mockRequest as FastifyRequest
      );

      expect(db.insert).toHaveBeenCalled();
      
      // 检查插入的数据
      const insertCall = db.insert.mock.calls[0];
      const valuesCall = insertCall[0]().values.mock.calls[0][0];
      
      expect(valuesCall.userId).toBe('test-user-id');
      expect(valuesCall.email).toBe('<EMAIL>');
      expect(valuesCall.emailType).toBe('verification');
      expect(valuesCall.status).toBe('delivered');
      expect(valuesCall.provider).toBe('internal');
    });

    it('应该记录失败的验证尝试', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await logEmailVerificationAttempt(
        'test-user-id',
        '<EMAIL>',
        'failed',
        mockRequest as FastifyRequest,
        { reason: 'email_not_verified' }
      );

      expect(db.insert).toHaveBeenCalled();
      
      const insertCall = db.insert.mock.calls[0];
      const valuesCall = insertCall[0]().values.mock.calls[0][0];
      
      expect(valuesCall.status).toBe('failed');
      expect(JSON.parse(valuesCall.metadata)).toMatchObject({
        verificationAttempt: true,
        status: 'failed',
        reason: 'email_not_verified'
      });
    });

    it('应该处理数据库错误', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.insert.mockReturnValueOnce({
        values: jest.fn().mockRejectedValue(new Error('数据库错误'))
      });

      // 不应该抛出异常
      await expect(
        logEmailVerificationAttempt(
          'test-user-id',
          '<EMAIL>',
          'success'
        )
      ).resolves.not.toThrow();
    });
  });

  describe('recordSecurityEvent 函数', () => {
    it('应该记录安全事件', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await recordSecurityEvent(
        'test-user-id',
        'rate_limit_exceeded',
        'medium',
        mockRequest as FastifyRequest,
        { action: 'verification_request' }
      );

      expect(db.insert).toHaveBeenCalled();
      
      const insertCall = db.insert.mock.calls[0];
      const valuesCall = insertCall[0]().values.mock.calls[0][0];
      
      expect(valuesCall.userId).toBe('test-user-id');
      expect(valuesCall.eventType).toBe('rate_limit_exceeded');
      expect(valuesCall.severity).toBe('medium');
      expect(valuesCall.ipAddress).toBe('127.0.0.1');
      expect(valuesCall.resolved).toBe(false);
    });

    it('应该处理null用户ID', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await recordSecurityEvent(
        null,
        'suspicious_activity',
        'high',
        mockRequest as FastifyRequest
      );

      expect(db.insert).toHaveBeenCalled();
      
      const insertCall = db.insert.mock.calls[0];
      const valuesCall = insertCall[0]().values.mock.calls[0][0];
      
      expect(valuesCall.userId).toBeNull();
      expect(valuesCall.eventType).toBe('suspicious_activity');
      expect(valuesCall.severity).toBe('high');
    });

    it('应该从不同的头部获取IP地址', async () => {
      // 测试x-forwarded-for头部
      const requestWithForwardedFor = {
        ...mockRequest,
        headers: {
          ...mockRequest.headers,
          'x-forwarded-for': '***********, ********'
        }
      };

      await recordSecurityEvent(
        'test-user-id',
        'test_event',
        'low',
        requestWithForwardedFor as FastifyRequest
      );

      const { db } = require('../src/lib/drizzle.js');
      const insertCall = db.insert.mock.calls[0];
      const valuesCall = insertCall[0]().values.mock.calls[0][0];
      
      expect(valuesCall.ipAddress).toBe('***********'); // 应该取第一个IP
    });

    it('应该处理数据库错误', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.insert.mockReturnValueOnce({
        values: jest.fn().mockRejectedValue(new Error('数据库错误'))
      });

      // 不应该抛出异常
      await expect(
        recordSecurityEvent('test-user-id', 'test_event', 'low')
      ).resolves.not.toThrow();
    });
  });

  describe('trackEmailSending 中间件', () => {
    it('应该初始化邮件跟踪', async () => {
      await trackEmailSending(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 应该在request对象上设置跟踪信息
      expect((mockRequest as any).emailTracking).toBeDefined();
      expect((mockRequest as any).emailTracking.startTime).toBeTypeOf('number');
      expect((mockRequest as any).emailTracking.emailLogId).toBeTypeOf('string');

      // 应该添加响应钩子
      expect(mockReply.addHook).toHaveBeenCalledWith('onSend', expect.any(Function));
    });

    it('响应钩子应该在成功时更新状态', async () => {
      await trackEmailSending(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 获取添加的钩子函数
      const hookCall = (mockReply.addHook as jest.Mock).mock.calls[0];
      const hookFunction = hookCall[1];

      // 模拟成功响应
      const mockReplyWithStatus = { ...mockReply, statusCode: 200 };
      
      await hookFunction(mockRequest, mockReplyWithStatus, 'payload');

      // 应该调用updateEmailLogStatus
      const { db } = require('../src/lib/drizzle.js');
      expect(db.update).toHaveBeenCalled();
    });

    it('应该处理错误情况', async () => {
      // 模拟trackEmailSending内部错误
      const originalAddHook = mockReply.addHook;
      (mockReply.addHook as jest.Mock).mockImplementationOnce(() => {
        throw new Error('钩子添加失败');
      });

      // 不应该抛出异常
      await expect(
        trackEmailSending(
          mockRequest as FastifyRequest,
          mockReply as FastifyReply
        )
      ).resolves.not.toThrow();

      // 恢复原始mock
      mockReply.addHook = originalAddHook;
    });
  });

  describe('updateEmailLogStatus 函数', () => {
    it('应该更新邮件状态为已发送', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await updateEmailLogStatus('test-log-id', 'sent', undefined, 'message-123');

      expect(db.update).toHaveBeenCalled();
      
      const updateCall = db.update.mock.calls[0];
      const setCall = updateCall[0]().set.mock.calls[0][0];
      
      expect(setCall.status).toBe('sent');
      expect(setCall.messageId).toBe('message-123');
      expect(setCall.sentAt).toBeInstanceOf(Date);
    });

    it('应该更新邮件状态为已送达', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await updateEmailLogStatus('test-log-id', 'delivered');

      expect(db.update).toHaveBeenCalled();
      
      const updateCall = db.update.mock.calls[0];
      const setCall = updateCall[0]().set.mock.calls[0][0];
      
      expect(setCall.status).toBe('delivered');
      expect(setCall.deliveredAt).toBeInstanceOf(Date);
    });

    it('应该更新邮件状态为失败', async () => {
      const { db } = require('../src/lib/drizzle.js');

      await updateEmailLogStatus('test-log-id', 'failed', '邮件发送失败');

      expect(db.update).toHaveBeenCalled();
      
      const updateCall = db.update.mock.calls[0];
      const setCall = updateCall[0]().set.mock.calls[0][0];
      
      expect(setCall.status).toBe('failed');
      expect(setCall.errorMessage).toBe('邮件发送失败');
    });

    it('应该处理数据库更新错误', async () => {
      // 模拟数据库错误
      const { db } = require('../src/lib/drizzle.js');
      db.update.mockReturnValueOnce({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockRejectedValue(new Error('更新失败'))
        })
      });

      // 不应该抛出异常
      await expect(
        updateEmailLogStatus('test-log-id', 'sent')
      ).resolves.not.toThrow();
    });
  });

  describe('中间件集成测试', () => {
    it('应该正确处理完整的验证流程', async () => {
      // 1. 首先进行可选验证检查
      await optionalEmailVerificationCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect((mockRequest as any).emailVerificationStatus).toBeDefined();

      // 2. 然后进行频率限制检查
      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect((mockRequest as any).emailVerificationRateLimit).toBeDefined();

      // 3. 最后进行必需的验证检查
      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 应该被阻止（因为用户未验证）
      expect(mockReply.status).toHaveBeenCalledWith(403);
    });

    it('应该为已验证用户完成完整流程', async () => {
      // 模拟已验证用户
      const { auth } = require('../src/auth.js');
      auth.api.getSession.mockResolvedValue({
        user: mockVerifiedUser
      });

      const { db } = require('../src/lib/drizzle.js');
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockVerifiedUser])
          })
        })
      });

      // 重置reply mock
      mockReply.status = jest.fn().mockReturnThis();
      mockReply.send = jest.fn().mockReturnThis();

      // 1. 可选验证检查
      await optionalEmailVerificationCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect((mockRequest as any).emailVerificationStatus.isVerified).toBe(true);

      // 2. 频率限制检查
      await emailVerificationRateLimit(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 3. 必需验证检查
      await requireEmailVerification(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      // 不应该被阻止
      expect(mockReply.status).not.toHaveBeenCalledWith(403);
      expect(mockReply.status).not.toHaveBeenCalledWith(401);
    });
  });
});