/**
 * 注册接口重构测试
 * 测试新的临时注册表方式的注册流程
 */

import { test, expect, describe, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import { createTestApp } from './setup';
import { db } from '../src/lib/drizzle';
import { pendingRegistration, user, organization, member, subscription } from '../src/lib/auth-schema';
import { eq } from 'drizzle-orm';

describe('注册接口重构测试', () => {
  let app: FastifyInstance;
  const testEmail = '<EMAIL>';
  const testData = {
    email: testEmail,
    password: 'TestPassword123!',
    name: '测试用户重构',
    company_name: '测试公司重构',
    invite_code: 'TEST2025',
    language: 'chinese',
  };

  beforeAll(async () => {
    app = await createTestApp();
  });

  afterAll(async () => {
    await app?.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    try {
      await db.delete(pendingRegistration).where(eq(pendingRegistration.email, testEmail));
      await db.delete(user).where(eq(user.email, testEmail));
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('POST /api/auth/sign-up-invite - 重构版本', () => {
    test('应该成功创建临时注册记录', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      console.log('注册响应:', JSON.stringify(body, null, 2));
      
      expect(body.success).toBe(true);
      expect(body.data).toBeDefined();
      expect(body.data.pendingId).toBeDefined();
      expect(body.data.email).toBe(testEmail);
      expect(body.data.verificationSent).toBeDefined();
      expect(body.data.nextSteps).toBeDefined();
      
      // 向后兼容性检查
      expect(body.user).toBeDefined();
      expect(body.user.email).toBe(testEmail);
      expect(body.user.name).toBe(testData.name);
      expect(body.user.emailVerified).toBe(false);
      
      // 验证数据库中是否创建了临时注册记录
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(eq(pendingRegistration.email, testEmail))
        .limit(1);
      
      expect(pendingRecords.length).toBe(1);
      const pendingRecord = pendingRecords[0]!;
      expect(pendingRecord.email).toBe(testEmail);
      expect(pendingRecord.name).toBe(testData.name);
      expect(pendingRecord.companyName).toBe(testData.company_name);
      expect(pendingRecord.inviteCode).toBe(testData.invite_code);
      expect(pendingRecord.status).toBe('pending');
      expect(pendingRecord.inviteCodeVerified).toBe(true);
      
      // 验证正式用户表中没有记录
      const userRecords = await db
        .select()
        .from(user)
        .where(eq(user.email, testEmail))
        .limit(1);
      
      expect(userRecords.length).toBe(0);
    });

    test('应该拒绝无效的邀请码', async () => {
      const invalidData = { ...testData, invite_code: 'INVALID_CODE' };
      
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: invalidData,
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('INVALID_INVITE_CODE');
      expect(body.message).toContain('邀请码无效');
    });

    test('应该拒绝已存在的邮箱', async () => {
      // 先创建一个用户
      await db.insert(user).values({
        id: 'test-user-id',
        email: testEmail,
        name: '已存在用户',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(response.statusCode).toBe(409);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('USER_EXISTS');
      expect(body.message).toContain('已注册');
      
      // 清理测试数据
      await db.delete(user).where(eq(user.email, testEmail));
    });

    test('应该验证请求数据格式', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: '123', // 太短
        // 缺少必需字段
      };
      
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: invalidData,
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('VALIDATION_ERROR');
    });

    test('应该处理重复的临时注册请求', async () => {
      // 第一次注册
      const firstResponse = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(firstResponse.statusCode).toBe(200);
      
      // 第二次注册相同邮箱
      const secondResponse = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(secondResponse.statusCode).toBe(200);
      
      // 验证数据库中只有一条记录（旧记录被替换）
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(eq(pendingRegistration.email, testEmail));
      
      expect(pendingRecords.length).toBe(1);
    });
  });

  describe('POST /api/auth/resend-pending-verification - 重构版本', () => {
    test('应该成功重发验证邮件', async () => {
      // 先创建一个临时注册记录
      await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      // 等待一小段时间以避免频率限制
      await new Promise(resolve => setTimeout(resolve, 100));

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-pending-verification',
        payload: { email: testEmail },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.email).toBe(testEmail);
      expect(body.data.verificationSent).toBeDefined();
      expect(body.data.nextSteps).toBeDefined();
    });

    test('应该拒绝不存在的邮箱', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-pending-verification',
        payload: { email: '<EMAIL>' },
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBe('RESEND_FAILED');
    });

    test('应该处理频率限制', async () => {
      // 先创建一个临时注册记录
      await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      // 立即重发（触发频率限制）
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/resend-pending-verification',
        payload: { email: testEmail },
      });

      // 可能返回429或者成功（取决于实际的频率限制实现）
      expect([200, 429]).toContain(response.statusCode);
    });
  });

  describe('GET /api/auth/verify-pending-registration - 重构版本', () => {
    test('应该处理无效令牌', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-pending-registration?token=invalid-token',
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
      expect(response.body).toContain('验证'); // HTML错误页面应该包含验证相关文字
    });

    test('应该处理缺少令牌的请求', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/verify-pending-registration',
      });

      expect(response.statusCode).toBe(400); // 根据schema，token是必需的
    });
  });

  describe('向后兼容性测试', () => {
    test('旧的API响应格式应该保持兼容', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      
      // 检查旧版本的字段仍然存在
      expect(body.success).toBeDefined();
      expect(body.message).toBeDefined();
      expect(body.user).toBeDefined();
      expect(body.user.id).toBeDefined();
      expect(body.user.email).toBeDefined();
      expect(body.user.name).toBeDefined();
      expect(body.nextSteps).toBeDefined();
      
      // 检查新版本的字段
      expect(body.data).toBeDefined();
      expect(body.data.pendingId).toBeDefined();
      expect(body.data.verificationSent).toBeDefined();
    });
  });

  describe('错误处理测试', () => {
    test('应该处理服务异常', async () => {
      // 使用一个会导致内部错误的payload
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: {
          ...testData,
          email: null, // 无效数据
        },
      });

      expect([400, 500]).toContain(response.statusCode);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toBeDefined();
    });
  });

  describe('数据一致性测试', () => {
    test('未完成验证的临时注册不应创建正式用户', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(response.statusCode).toBe(200);
      
      // 验证没有创建正式用户
      const userRecords = await db
        .select()
        .from(user)
        .where(eq(user.email, testEmail))
        .limit(1);
      
      expect(userRecords.length).toBe(0);
      
      // 验证创建了临时注册记录
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(eq(pendingRegistration.email, testEmail))
        .limit(1);
      
      expect(pendingRecords.length).toBe(1);
    });

    test('临时注册记录应包含完整信息', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/sign-up-invite',
        payload: testData,
      });

      expect(response.statusCode).toBe(200);
      
      const pendingRecords = await db
        .select()
        .from(pendingRegistration)
        .where(eq(pendingRegistration.email, testEmail))
        .limit(1);
      
      expect(pendingRecords.length).toBe(1);
      const record = pendingRecords[0]!;
      
      expect(record.email).toBe(testData.email);
      expect(record.name).toBe(testData.name);
      expect(record.companyName).toBe(testData.company_name);
      expect(record.inviteCode).toBe(testData.invite_code);
      expect(record.language).toBe(testData.language);
      expect(record.password).toBeDefined(); // 应该是加密后的密码
      expect(record.pendingOrganizationId).toBeDefined();
      expect(record.pendingOrganizationSlug).toBeDefined();
      expect(record.status).toBe('pending');
      expect(record.registrationStep).toBe('email_verification');
      expect(record.inviteCodeVerified).toBe(true);
      expect(record.expiresAt).toBeDefined();
      expect(new Date(record.expiresAt) > new Date()).toBe(true); // 未来时间
    });
  });
});