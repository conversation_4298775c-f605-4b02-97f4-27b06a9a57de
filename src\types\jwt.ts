/**
 * JWT相关类型定义
 */

export interface JWTPayload {
  /**
   * 用户ID
   */
  userId: string;

  /**
   * 用户邮箱
   */
  email: string;

  /**
   * 用户角色
   */
  role?: string;

  /**
   * 组织ID
   */
  organizationId?: string;

  /**
   * 组织角色
   */
  organizationRole?: string;

  /**
   * 签发时间 (Unix timestamp)
   */
  iat?: number;

  /**
   * 过期时间 (Unix timestamp)
   */
  exp?: number;

  /**
   * 签发者
   */
  iss?: string;

  /**
   * 受众
   */
  aud?: string | string[];

  /**
   * 主题
   */
  sub?: string;

  /**
   * JWT ID
   */
  jti?: string;

  /**
   * 自定义声明
   */
  [key: string]: any;
}

export interface JWTCreateRequest {
  /**
   * Token有效期（如：1h, 30m, 1d）
   */
  expiresIn?: string;

  /**
   * 自定义声明
   */
  customClaims?: Record<string, any>;

  /**
   * 受众列表
   */
  audience?: string[];
}

export interface JWTCreateResponse {
  /**
   * 操作是否成功
   */
  success: boolean;

  /**
   * JWT Token字符串
   */
  token?: string;

  /**
   * Token有效期（秒）
   */
  expiresIn?: number;

  /**
   * Token类型
   */
  tokenType?: string;

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 详细错误信息
   */
  message?: string;
}

export interface JWTVerifyRequest {
  /**
   * 要验证的JWT Token
   */
  token: string;
}

export interface JWTVerifyResponse {
  /**
   * 操作是否成功
   */
  success: boolean;

  /**
   * Token是否有效
   */
  valid?: boolean;

  /**
   * 解析的payload
   */
  payload?: JWTPayload;

  /**
   * Token过期时间
   */
  expiresAt?: string;

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 详细错误信息
   */
  message?: string;
}

export interface JWTRefreshRequest {
  /**
   * 刷新Token
   */
  refresh_token: string;
}

export interface JWTRefreshResponse {
  /**
   * 操作是否成功
   */
  success: boolean;

  /**
   * 新的JWT Token
   */
  token?: string;

  /**
   * Token有效期（秒）
   */
  expiresIn?: number;

  /**
   * Token类型
   */
  tokenType?: string;

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 详细错误信息
   */
  message?: string;
}

export interface JWTRevokeRequest {
  /**
   * 要撤销的JWT Token
   */
  token: string;
}

export interface JWTRevokeResponse {
  /**
   * 操作是否成功
   */
  success: boolean;

  /**
   * 成功信息
   */
  message?: string;

  /**
   * 错误信息
   */
  error?: string;
}

export interface JWTInfoResponse {
  /**
   * 操作是否成功
   */
  success: boolean;

  /**
   * 当前认证类型
   */
  authType?: "cookie" | "jwt" | "none";

  /**
   * 用户信息
   */
  user?: {
    id: string;
    email: string;
    role?: string;
  };

  /**
   * 是否可以创建Token
   */
  canCreateToken?: boolean;

  /**
   * 提示信息
   */
  message?: string;

  /**
   * 错误信息
   */
  error?: string;
}

/**
 * JWT配置选项
 */
export interface JWTConfig {
  /**
   * JWT密钥
   */
  secret?: string;

  /**
   * 默认Token有效期
   */
  expiresIn?: string;

  /**
   * 签名算法
   */
  algorithm?: string;

  /**
   * 签发者
   */
  issuer?: string;

  /**
   * 受众列表
   */
  audience?: string[];

  /**
   * JWKS密钥大小
   */
  keySize?: number;
}

/**
 * JWT错误类型
 */
export enum JWTErrorType {
  INVALID_TOKEN = "INVALID_TOKEN",
  EXPIRED_TOKEN = "EXPIRED_TOKEN",
  MALFORMED_TOKEN = "MALFORMED_TOKEN",
  SIGNATURE_VERIFICATION_FAILED = "SIGNATURE_VERIFICATION_FAILED",
  MISSING_TOKEN = "MISSING_TOKEN",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  INTERNAL_ERROR = "INTERNAL_ERROR",
}

/**
 * JWT错误信息
 */
export interface JWTError {
  type: JWTErrorType;
  message: string;
  details?: any;
}